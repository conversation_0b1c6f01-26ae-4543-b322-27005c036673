#!/bin/bash

# Monitor Simples de Duplicações - Sistema Tradição

echo "🔍 MONITOR SIMPLES INICIADO"
echo "=========================="
echo "Verificando a cada 30 segundos..."
echo "Para parar: Ctrl+C"
echo ""

# Criar diretório de logs
mkdir -p logs

# Função para log
log_message() {
    echo "[$(date '+%H:%M:%S')] $1" | tee -a logs/monitor_simple.log
}

# Função para criar alerta
create_alert() {
    cat > ALERTA_DUPLICACOES.txt << EOF
🚨 DUPLICAÇÕES DETECTADAS! 🚨
============================

Hora: $(date '+%H:%M:%S')
Problema: $1

PARA CORRIGIR:
1. Execute: bash scripts/check-quick.sh
2. Se necessário: bash scripts/clean-duplications.sh

EOF
    echo "🚨 ALERTA CRIADO: ALERTA_DUPLICACOES.txt"
}

# Função para remover alerta
remove_alert() {
    if [ -f "ALERTA_DUPLICACOES.txt" ]; then
        rm "ALERTA_DUPLICACOES.txt"
        echo "✅ Alerta removido"
    fi
}

# Verificação inicial
log_message "Monitor iniciado"

# Loop principal
while true; do
    echo "🔍 Verificando... $(date '+%H:%M:%S')"
    
    if bash scripts/check-quick.sh >/dev/null 2>&1; then
        log_message "✅ Sistema OK"
        remove_alert
    else
        log_message "❌ Problemas detectados"
        create_alert "Duplicações encontradas"
    fi
    
    sleep 30  # 30 segundos
done
