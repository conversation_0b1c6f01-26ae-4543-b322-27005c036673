#!/bin/bash

# Script para corrigir a tabela attachments adicionando colunas faltantes
# Data: 2025-05-25

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🔧 CORREÇÃO DA TABELA ATTACHMENTS${NC}"
echo "=================================="

# Configurações do banco
DB_HOST="postgres-ag-br1-03.conteige.cloud"
DB_PORT="54243"
DB_NAME="fcobdj_tradicao"
DB_USER="fcobdj_tradicao"

echo -e "${CYAN}📋 Configurações do banco:${NC}"
echo "Host: $DB_HOST"
echo "Port: $DB_PORT"
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo ""

# Solicitar senha
echo -e "${YELLOW}🔑 Digite a senha do banco de dados:${NC}"
read -s DB_PASS
echo ""

# Verificar conexão
echo -e "${CYAN}🔍 Verificando conexão com o banco...${NC}"
export PGPASSWORD="$DB_PASS"

if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Conexão estabelecida com sucesso!${NC}"
else
    echo -e "${RED}❌ Erro ao conectar com o banco de dados!${NC}"
    exit 1
fi

echo ""

# Verificar estrutura atual
echo -e "${CYAN}🔍 Verificando estrutura atual da tabela attachments...${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'attachments' 
ORDER BY ordinal_position;
"

echo ""

# Aplicar migração
echo -e "${CYAN}🚀 Aplicando correções na tabela...${NC}"
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f migrations/add_missing_attachment_columns.sql; then
    echo -e "${GREEN}✅ Correções aplicadas com sucesso!${NC}"
else
    echo -e "${RED}❌ Erro ao aplicar correções!${NC}"
    exit 1
fi

echo ""

# Verificar estrutura final
echo -e "${CYAN}📊 Verificando estrutura final da tabela...${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'attachments' 
AND column_name IN ('entity_type', 'entity_id', 'category', 'file_hash', 'original_name', 'file_name')
ORDER BY column_name;
"

echo ""

# Verificar índices
echo -e "${CYAN}📋 Verificando índices criados...${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'attachments' 
AND indexname LIKE 'idx_attachments_%'
ORDER BY indexname;
"

echo ""

# Verificar constraints
echo -e "${CYAN}🔒 Verificando constraints criadas...${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT conname, contype, pg_get_constraintdef(oid) as definition
FROM pg_constraint 
WHERE conrelid = 'attachments'::regclass
AND conname LIKE 'chk_attachments_%'
ORDER BY conname;
"

echo ""

# Teste básico
echo -e "${CYAN}🧪 Executando teste básico...${NC}"
RESULT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM attachments;")
echo -e "${GREEN}✅ Tabela funcionando corretamente ($RESULT registros)${NC}"

echo ""
echo -e "${GREEN}🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!${NC}"
echo "=================================="
echo ""
echo -e "${CYAN}📋 Próximos passos:${NC}"
echo "1. Reiniciar o sistema: ./app"
echo "2. Testar API: curl http://localhost:8080/api/test-attachments/stats"
echo "3. Testar galeria: http://localhost:8080/attachments/gallery"
echo ""
echo -e "${YELLOW}💡 Funcionalidades agora disponíveis:${NC}"
echo "  ✅ Colunas entity_type e entity_id"
echo "  ✅ Sistema de categorização"
echo "  ✅ Versionamento de arquivos"
echo "  ✅ Detecção de duplicatas"
echo "  ✅ Auditoria completa"
echo "  ✅ Controle de permissões"
echo ""
echo -e "${GREEN}Sistema de Anexos totalmente funcional! 🚀${NC}"
