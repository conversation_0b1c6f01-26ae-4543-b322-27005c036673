#!/bin/bash

# Script de Limpeza Automática de Duplicações - Sistema Tradição
# CUIDADO: <PERSON>ste script remove arquivos duplicados automaticamente

set -e

echo "🧹 LIMPEZA AUTOMÁTICA DE DUPLICAÇÕES"
echo "===================================="

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Função para confirmar ação
confirm_action() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    read -p "Continuar? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${RED}❌ Operação cancelada${NC}"
        exit 1
    fi
}

# Função para backup
create_backup() {
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    echo -e "${BLUE}📦 Criando backup em $BACKUP_DIR...${NC}"
    mkdir -p "$BACKUP_DIR"
    
    # Backup dos arquivos que serão removidos
    find internal/handlers -name "*order*.go" -not -name "unified_order_handler.go" -exec cp {} "$BACKUP_DIR/" \;
    find web/static/js -name "*order*.js" -not -name "unified_orders.js" -exec cp {} "$BACKUP_DIR/" \;
    find internal/routes -name "*order*.go" -not -name "unified_order_routes_new.go" -exec cp {} "$BACKUP_DIR/" \;
    
    echo -e "${GREEN}✅ Backup criado em $BACKUP_DIR${NC}"
}

echo -e "${BLUE}1. Analisando duplicações...${NC}"

# Contar duplicações
DUPLICATE_HANDLERS=$(find internal/handlers -name "*order*.go" -not -name "unified_order_handler.go" | wc -l)
DUPLICATE_JS=$(find web/static/js -name "*order*.js" -not -name "unified_orders.js" | wc -l)
DUPLICATE_ROUTES=$(find internal/routes -name "*order*.go" -not -name "unified_order_routes_new.go" | wc -l)

echo "Handlers duplicados encontrados: $DUPLICATE_HANDLERS"
echo "JavaScript duplicados encontrados: $DUPLICATE_JS"
echo "Rotas duplicadas encontradas: $DUPLICATE_ROUTES"

if [ "$DUPLICATE_HANDLERS" -eq 0 ] && [ "$DUPLICATE_JS" -eq 0 ] && [ "$DUPLICATE_ROUTES" -eq 0 ]; then
    echo -e "${GREEN}🎉 Nenhuma duplicação encontrada! Sistema já está limpo.${NC}"
    exit 0
fi

confirm_action "Isso irá remover $((DUPLICATE_HANDLERS + DUPLICATE_JS + DUPLICATE_ROUTES)) arquivos duplicados."

# Criar backup
create_backup

echo -e "${BLUE}2. Removendo handlers duplicados...${NC}"

# Lista de handlers duplicados para remover
HANDLERS_TO_REMOVE=(
    "internal/handlers/order_handlers_extended.go"
    "internal/handlers/maintenance_order_generic_handler.go"
    "internal/handlers/order_handler_test.go"
    "internal/handlers/technician_order_handler.go"
    "internal/handlers/order_handlers.go"
    "internal/handlers/order.go"
    "internal/handlers/maintenance_order_handler.go"
    "internal/handlers/maintenance_order_handler_test.go"
    "internal/handlers/order_assignment_handler.go"
)

for handler in "${HANDLERS_TO_REMOVE[@]}"; do
    if [ -f "$handler" ]; then
        echo "Removendo: $handler"
        rm "$handler"
    fi
done

echo -e "${BLUE}3. Removendo JavaScript duplicados...${NC}"

# Lista de JS duplicados para remover
JS_TO_REMOVE=(
    "web/static/js/orders_premium.js"
    "web/static/js/order_detail.js"
    "web/static/js/dashboard_orders.js"
    "web/static/js/orders_gallery.js"
    "web/static/js/orders_create.js"
    "web/static/js/order_detail_new.js"
    "web/static/js/orders.js"
    "web/static/js/orders_simple.js"
    "web/static/js/order_detail_service.js"
    "web/static/js/ordens_create_order.js"
    "web/static/js/calendar-flip.js"
    "web/static/js/calendar-init.js"
    "web/static/js/Ordermtecnico.js"
)

for js in "${JS_TO_REMOVE[@]}"; do
    if [ -f "$js" ]; then
        echo "Removendo: $js"
        rm "$js"
    fi
done

echo -e "${BLUE}4. Removendo rotas duplicadas...${NC}"

# Lista de rotas duplicadas para remover
ROUTES_TO_REMOVE=(
    "internal/routes/order_routes.go"
    "internal/routes/ordem_routes.go"
    "internal/routes/maintenance_order_routes.go"
    "internal/routes/unified_order_routes.go"  # versão antiga
)

for route in "${ROUTES_TO_REMOVE[@]}"; do
    if [ -f "$route" ]; then
        echo "Removendo: $route"
        rm "$route"
    fi
done

echo -e "${BLUE}5. Limpando imports desnecessários...${NC}"

# Remover imports dos arquivos removidos
find . -name "*.go" -exec sed -i '/order_handlers_extended\|maintenance_order_generic_handler\|order_handler_test\|technician_order_handler\|order_handlers\|maintenance_order_handler\|order_assignment_handler/d' {} \;

echo -e "${BLUE}6. Atualizando templates...${NC}"

# Atualizar templates que ainda referenciam JS antigos
find web/templates -name "*.html" -exec sed -i 's/orders\.js/unified_orders.js/g' {} \;
find web/templates -name "*.html" -exec sed -i 's/orders_gallery\.js/unified_orders.js/g' {} \;
find web/templates -name "*.html" -exec sed -i 's/dashboard_orders\.js/unified_orders.js/g' {} \;
find web/templates -name "*.html" -exec sed -i 's/calendar-flip\.js/unified_orders.js/g' {} \;
find web/templates -name "*.html" -exec sed -i 's/Ordermtecnico\.js/unified_orders.js/g' {} \;

echo -e "${BLUE}7. Verificando compilação...${NC}"

if go build -o /tmp/test_clean ./cmd/main.go; then
    echo -e "${GREEN}✅ Compilação OK após limpeza${NC}"
    rm /tmp/test_clean
else
    echo -e "${RED}❌ Erro de compilação após limpeza${NC}"
    echo -e "${YELLOW}💡 Restaurar backup se necessário: cp $BACKUP_DIR/* internal/handlers/${NC}"
    exit 1
fi

echo -e "${BLUE}8. Executando verificação final...${NC}"

# Executar verificação de duplicações
if ./scripts/check-duplications.sh; then
    echo -e "${GREEN}🎉 LIMPEZA CONCLUÍDA COM SUCESSO!${NC}"
    echo -e "${GREEN}✅ Todas as duplicações foram removidas${NC}"
    echo -e "${GREEN}✅ Sistema compilando corretamente${NC}"
    echo -e "${GREEN}✅ Arquitetura unificada implementada${NC}"
else
    echo -e "${YELLOW}⚠️  Ainda existem algumas duplicações menores${NC}"
    echo -e "${YELLOW}💡 Execute novamente ou corrija manualmente${NC}"
fi

echo ""
echo "=================================================="
echo -e "${BLUE}RESUMO DA LIMPEZA:${NC}"
echo "=================================================="
echo "📦 Backup criado em: $BACKUP_DIR"
echo "🗑️  Handlers removidos: $DUPLICATE_HANDLERS"
echo "🗑️  JavaScript removidos: $DUPLICATE_JS"
echo "🗑️  Rotas removidas: $DUPLICATE_ROUTES"
echo "🔧 Templates atualizados"
echo "✅ Compilação verificada"
echo ""
echo -e "${GREEN}🎯 Sistema agora segue a arquitetura unificada obrigatória!${NC}"
