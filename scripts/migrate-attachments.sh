#!/bin/bash

# Script para aplicar migração da tabela de anexos
# Sistema Tradição

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 MIGRAÇÃO DO SISTEMA DE ANEXOS${NC}"
echo "=================================="

# Verificar se arquivo de migração existe
if [ ! -f "migrations/create_attachments_table.sql" ]; then
    echo -e "${RED}❌ Arquivo de migração não encontrado!${NC}"
    echo "Esperado: migrations/create_attachments_table.sql"
    exit 1
fi

# Configurações do banco (usar as mesmas do script iniciar_rapido.sh)
DB_HOST="postgres-ag-br1-03.conteige.cloud"
DB_PORT="54243"
DB_NAME="fcobdj_tradicao"
DB_USER="fcobdj_tradicao"

echo -e "${YELLOW}📋 Configurações do banco:${NC}"
echo "Host: $DB_HOST"
echo "Port: $DB_PORT"
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo ""

# Solicitar senha
echo -e "${YELLOW}🔑 Digite a senha do banco de dados:${NC}"
read -s DB_PASSWORD
echo ""

# Verificar conexão
echo -e "${BLUE}🔍 Verificando conexão com o banco...${NC}"
export PGPASSWORD="$DB_PASSWORD"

if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
    echo -e "${RED}❌ Erro ao conectar com o banco de dados!${NC}"
    echo "Verifique as credenciais e tente novamente."
    exit 1
fi

echo -e "${GREEN}✅ Conexão estabelecida com sucesso!${NC}"
echo ""

# Verificar se tabela já existe
echo -e "${BLUE}🔍 Verificando se tabela attachments já existe...${NC}"
TABLE_EXISTS=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'attachments');" 2>/dev/null | tr -d ' ')

if [ "$TABLE_EXISTS" = "t" ]; then
    echo -e "${YELLOW}⚠️  Tabela attachments já existe!${NC}"
    echo -e "${YELLOW}Deseja recriar a tabela? (isso apagará todos os dados) [y/N]:${NC}"
    read -r RECREATE
    
    if [[ $RECREATE =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🗑️  Removendo tabela existente...${NC}"
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "DROP TABLE IF EXISTS attachments CASCADE;" >/dev/null 2>&1
        echo -e "${GREEN}✅ Tabela removida!${NC}"
    else
        echo -e "${BLUE}ℹ️  Aplicando apenas alterações incrementais...${NC}"
    fi
fi

# Aplicar migração
echo -e "${BLUE}🚀 Aplicando migração...${NC}"
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "migrations/create_attachments_table.sql"; then
    echo ""
    echo -e "${GREEN}✅ Migração aplicada com sucesso!${NC}"
else
    echo ""
    echo -e "${RED}❌ Erro ao aplicar migração!${NC}"
    exit 1
fi

# Verificar estrutura da tabela
echo ""
echo -e "${BLUE}📊 Verificando estrutura da tabela...${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\d attachments"

# Verificar índices
echo ""
echo -e "${BLUE}📋 Índices criados:${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT indexname, indexdef FROM pg_indexes WHERE tablename = 'attachments';"

# Verificar constraints
echo ""
echo -e "${BLUE}🔒 Constraints criadas:${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT conname, contype, pg_get_constraintdef(oid) FROM pg_constraint WHERE conrelid = 'attachments'::regclass;"

# Criar diretórios necessários
echo ""
echo -e "${BLUE}📁 Criando diretórios de upload...${NC}"
mkdir -p uploads/attachments/thumbnails
chmod 755 uploads/attachments
chmod 755 uploads/attachments/thumbnails

echo -e "${GREEN}✅ Diretórios criados:${NC}"
echo "  - uploads/attachments/"
echo "  - uploads/attachments/thumbnails/"

# Teste básico
echo ""
echo -e "${BLUE}🧪 Executando teste básico...${NC}"
TEST_RESULT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM attachments;" 2>/dev/null | tr -d ' ')

if [ "$TEST_RESULT" = "0" ]; then
    echo -e "${GREEN}✅ Tabela funcionando corretamente (0 registros)${NC}"
else
    echo -e "${GREEN}✅ Tabela funcionando corretamente ($TEST_RESULT registros)${NC}"
fi

# Limpar senha da memória
unset PGPASSWORD
unset DB_PASSWORD

echo ""
echo -e "${GREEN}🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!${NC}"
echo "=================================="
echo ""
echo -e "${BLUE}📋 Próximos passos:${NC}"
echo "1. Reiniciar o sistema: ./scripts/iniciar_rapido.sh"
echo "2. Testar upload: http://localhost:8080/attachments/gallery"
echo "3. Verificar logs: tail -f logs/sistema.log"
echo ""
echo -e "${YELLOW}💡 Funcionalidades disponíveis:${NC}"
echo "  ✅ Upload de múltiplos arquivos"
echo "  ✅ Galeria de imagens com lightbox"
echo "  ✅ Versionamento de arquivos"
echo "  ✅ Categorização e tags"
echo "  ✅ Thumbnails automáticos"
echo "  ✅ Detecção de duplicatas"
echo "  ✅ Controle de permissões"
echo ""
echo -e "${GREEN}Sistema de Anexos pronto para uso! 🚀${NC}"
