#!/bin/bash

# Script de Verificação de Duplicações - Sistema Tradição
# Este script deve ser executado antes de cada commit/deploy

set -e

echo "🔍 VERIFICANDO DUPLICAÇÕES NO SISTEMA TRADIÇÃO..."
echo "=================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Contadores
ERRORS=0
WARNINGS=0

# Função para reportar erro
report_error() {
    echo -e "${RED}❌ ERRO: $1${NC}"
    ERRORS=$((ERRORS + 1))
}

# Função para reportar warning
report_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
    WARNINGS=$((WARNINGS + 1))
}

# Função para reportar sucesso
report_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo -e "${BLUE}1. Verificando endpoints duplicados...${NC}"

# Verificar endpoints duplicados em rotas
DUPLICATE_ROUTES=$(find internal/routes -name "*.go" -exec grep -l "GET.*orders" {} \; | wc -l)
if [ "$DUPLICATE_ROUTES" -gt 2 ]; then
    report_error "Encontrados $DUPLICATE_ROUTES arquivos com rotas de orders. Máximo permitido: 2 (unified + legacy redirect)"
else
    report_success "Rotas de orders não duplicadas"
fi

# Verificar handlers duplicados
echo -e "${BLUE}2. Verificando handlers duplicados...${NC}"

DUPLICATE_HANDLERS=$(find internal/handlers -name "*order*.go" -not -name "unified_order_handler.go" | wc -l)
if [ "$DUPLICATE_HANDLERS" -gt 0 ]; then
    report_warning "Encontrados $DUPLICATE_HANDLERS handlers de order além do unificado:"
    find internal/handlers -name "*order*.go" -not -name "unified_order_handler.go"
fi

# Verificar JavaScript duplicado
echo -e "${BLUE}3. Verificando JavaScript duplicado...${NC}"

DUPLICATE_JS=$(find web/static/js -name "*order*.js" -not -name "unified_orders.js" | wc -l)
if [ "$DUPLICATE_JS" -gt 0 ]; then
    report_error "Encontrados $DUPLICATE_JS arquivos JS de orders além do unificado:"
    find web/static/js -name "*order*.js" -not -name "unified_orders.js"
fi

# Verificar funções duplicadas
echo -e "${BLUE}4. Verificando funções duplicadas...${NC}"

# Procurar por funções com nomes similares
SIMILAR_FUNCTIONS=$(grep -r "func.*Order" internal/ --include="*.go" | grep -v "unified_order_handler.go" | wc -l)
if [ "$SIMILAR_FUNCTIONS" -gt 5 ]; then
    report_warning "Encontradas $SIMILAR_FUNCTIONS funções relacionadas a Order fora do handler unificado"
fi

# Verificar imports desnecessários
echo -e "${BLUE}5. Verificando imports desnecessários...${NC}"

UNUSED_IMPORTS=$(find . -name "*.go" -exec go list -f '{{.ImportPath}}: {{.Imports}}' {} \; 2>/dev/null | grep -c "handlers.*Order" || true)
if [ "$UNUSED_IMPORTS" -gt 1 ]; then
    report_warning "Possíveis imports desnecessários de handlers de Order"
fi

# Verificar estruturas duplicadas
echo -e "${BLUE}6. Verificando estruturas duplicadas...${NC}"

DUPLICATE_STRUCTS=$(grep -r "type.*Order.*struct" internal/ --include="*.go" | wc -l)
if [ "$DUPLICATE_STRUCTS" -gt 3 ]; then
    report_warning "Encontradas $DUPLICATE_STRUCTS estruturas de Order. Verificar se são necessárias"
fi

# Verificar templates duplicados
echo -e "${BLUE}7. Verificando templates duplicados...${NC}"

DUPLICATE_TEMPLATES=$(find web/templates -name "*order*" | wc -l)
if [ "$DUPLICATE_TEMPLATES" -gt 3 ]; then
    report_warning "Encontrados $DUPLICATE_TEMPLATES templates de order. Verificar consolidação"
fi

# Verificar URLs hardcoded
echo -e "${BLUE}8. Verificando URLs hardcoded...${NC}"

HARDCODED_URLS=$(grep -r "/api/ordens" web/ --include="*.html" --include="*.js" | wc -l)
if [ "$HARDCODED_URLS" -gt 0 ]; then
    report_error "Encontradas $HARDCODED_URLS URLs antigas hardcoded:"
    grep -r "/api/ordens" web/ --include="*.html" --include="*.js"
fi

# Verificar ordem #18 hardcoded
echo -e "${BLUE}9. Verificando ordem #18 hardcoded...${NC}"

ORDER_18_REFS=$(grep -r "18\|\"18\"" web/static/js/ --include="*.js" | grep -v "unified_orders.js" | wc -l)
if [ "$ORDER_18_REFS" -gt 0 ]; then
    report_error "Encontradas referências à ordem #18 fora do handler unificado"
fi

# Verificar dependências circulares
echo -e "${BLUE}10. Verificando dependências circulares...${NC}"

if command -v go &> /dev/null; then
    CIRCULAR_DEPS=$(go list -f '{{.ImportPath}}: {{.Imports}}' ./... 2>/dev/null | grep -c "cycle" || true)
    if [ "$CIRCULAR_DEPS" -gt 0 ]; then
        report_error "Encontradas $CIRCULAR_DEPS dependências circulares"
    else
        report_success "Nenhuma dependência circular encontrada"
    fi
fi

echo ""
echo "=================================================="
echo -e "${BLUE}RESUMO DA VERIFICAÇÃO:${NC}"
echo "=================================================="

if [ "$ERRORS" -eq 0 ] && [ "$WARNINGS" -eq 0 ]; then
    echo -e "${GREEN}🎉 PERFEITO! Nenhuma duplicação encontrada!${NC}"
    exit 0
elif [ "$ERRORS" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  $WARNINGS warnings encontrados. Revisar recomendado.${NC}"
    exit 0
else
    echo -e "${RED}❌ $ERRORS erros e $WARNINGS warnings encontrados!${NC}"
    echo -e "${RED}🚫 BLOQUEANDO DEPLOY - Corrigir duplicações antes de continuar${NC}"
    exit 1
fi
