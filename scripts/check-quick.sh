#!/bin/bash

# Verificação Rápida de Duplicações - Sistema Tradição
# Script simples para verificação rápida sem Git

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}⚡ VERIFICAÇÃO RÁPIDA DE DUPLICAÇÕES${NC}"
echo "=================================="

# Contadores
ERRORS=0
WARNINGS=0

# Função para reportar erro
report_error() {
    echo -e "${RED}❌ $1${NC}"
    ERRORS=$((ERRORS + 1))
}

# Função para reportar warning
report_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    WARNINGS=$((WARNINGS + 1))
}

# Função para reportar sucesso
report_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo -e "${BLUE}1. Verificando handlers duplicados...${NC}"
DUPLICATE_HANDLERS=$(find internal/handlers -name "*order*.go" -not -name "unified_order_handler.go" 2>/dev/null | wc -l)
if [ "$DUPLICATE_HANDLERS" -gt 0 ]; then
    report_error "Encontrados $DUPLICATE_HANDLERS handlers duplicados"
    find internal/handlers -name "*order*.go" -not -name "unified_order_handler.go" 2>/dev/null
else
    report_success "Handlers não duplicados"
fi

echo -e "${BLUE}2. Verificando JavaScript duplicado...${NC}"
DUPLICATE_JS=$(find web/static/js -name "*order*.js" -not -name "unified_orders.js" 2>/dev/null | wc -l)
if [ "$DUPLICATE_JS" -gt 0 ]; then
    report_error "Encontrados $DUPLICATE_JS arquivos JS duplicados"
    find web/static/js -name "*order*.js" -not -name "unified_orders.js" 2>/dev/null
else
    report_success "JavaScript não duplicado"
fi

echo -e "${BLUE}3. Verificando URLs antigas...${NC}"
HARDCODED_URLS=$(grep -r "/api/ordens" web/ --include="*.html" --include="*.js" 2>/dev/null | wc -l)
if [ "$HARDCODED_URLS" -gt 0 ]; then
    report_error "Encontradas $HARDCODED_URLS URLs antigas"
    echo "Execute: grep -r '/api/ordens' web/ --include='*.html' --include='*.js'"
else
    report_success "Nenhuma URL antiga encontrada"
fi

echo -e "${BLUE}4. Verificando compilação...${NC}"
if go build -o /tmp/test_quick ./cmd/main.go >/dev/null 2>&1; then
    report_success "Compilação OK"
    rm -f /tmp/test_quick
else
    report_error "Erro de compilação"
    echo "Execute: go build ./cmd/main.go"
fi

echo ""
echo "=================================="
if [ "$ERRORS" -eq 0 ] && [ "$WARNINGS" -eq 0 ]; then
    echo -e "${GREEN}🎉 SISTEMA LIMPO! Nenhum problema encontrado.${NC}"
    exit 0
elif [ "$ERRORS" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  $WARNINGS warnings encontrados. Sistema OK.${NC}"
    exit 0
else
    echo -e "${RED}❌ $ERRORS erros encontrados!${NC}"
    echo -e "${YELLOW}💡 Para corrigir: ./scripts/clean-duplications.sh${NC}"
    exit 1
fi
