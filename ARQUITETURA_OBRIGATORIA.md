# Arquitetura Obrigatória - Sistema Tradição

## 🛡️ **REGRAS INVIOLÁVEIS**

Este documento define a arquitetura **OBRIGATÓRIA** do Sistema Tradição. Qualquer desvio dessas regras será **AUTOMATICAMENTE BLOQUEADO** pelos sistemas de qualidade.

---

## 📋 **ESTRUTURA UNIFICADA OBRIGATÓRIA**

### **1. HANDLERS - APENAS UM POR DOMÍNIO**

```
✅ PERMITIDO:
internal/handlers/
├── unified_order_handler.go     ← ÚNICO handler para ordens
├── user_handler.go              ← ÚNICO handler para usuários  
├── branch_handler.go            ← ÚNICO handler para filiais
└── auth_handler.go              ← ÚNICO handler para autenticação

❌ PROIBIDO:
├── order_handler.go             ← DUPLICAÇÃO
├── ordem_handler.go             ← DUPLICAÇÃO
├── maintenance_order_handler.go ← DUPLICAÇÃO
├── calendar_handler.go          ← DUPLICAÇÃO
└── dashboard_orders_handler.go  ← DUPLICAÇÃO
```

### **2. ROTAS - CONSOLIDAÇÃO OBRIGATÓRIA**

```
✅ PERMITIDO:
internal/routes/
├── unified_order_routes_new.go  ← ÚNICO arquivo de rotas para ordens
├── user_routes.go               ← Rotas de usuários
├── branch_routes.go             ← Rotas de filiais
└── auth_routes.go               ← Rotas de autenticação

❌ PROIBIDO:
├── order_routes.go              ← DUPLICAÇÃO
├── ordem_routes.go              ← DUPLICAÇÃO
├── maintenance_order_routes.go  ← DUPLICAÇÃO
└── calendar_routes.go           ← DUPLICAÇÃO
```

### **3. JAVASCRIPT - ARQUIVO ÚNICO**

```
✅ PERMITIDO:
web/static/js/
├── unified_orders.js            ← ÚNICO arquivo para ordens
├── common.js                    ← Funções comuns
├── theme.js                     ← Tema
└── sidebar.js                   ← Menu lateral

❌ PROIBIDO:
├── orders.js                    ← DUPLICAÇÃO
├── orders_gallery.js           ← DUPLICAÇÃO
├── dashboard_orders.js          ← DUPLICAÇÃO
├── calendar-flip.js             ← DUPLICAÇÃO
└── Ordermtecnico.js            ← DUPLICAÇÃO
```

---

## 🔗 **ENDPOINTS UNIFICADOS OBRIGATÓRIOS**

### **ENDPOINTS PERMITIDOS:**

```http
✅ GET  /api/orders                 ← Listar ordens (com paginação)
✅ GET  /api/orders/:id             ← Obter ordem específica
✅ GET  /api/orders/calendar        ← Ordens do calendário
✅ GET  /api/orders/technician      ← Ordens do técnico
✅ GET  /api/orders/metrics         ← Métricas e estatísticas
✅ POST /api/orders                 ← Criar nova ordem
✅ PUT  /api/orders/:id             ← Atualizar ordem
✅ DELETE /api/orders/:id           ← Excluir ordem
```

### **ENDPOINTS PROIBIDOS:**

```http
❌ /api/ordens                      ← DUPLICAÇÃO
❌ /api/ordens/:id                  ← DUPLICAÇÃO
❌ /api/ordens/tecnico              ← DUPLICAÇÃO
❌ /api/ordens/calendario           ← DUPLICAÇÃO
❌ /api/calendar-events             ← DUPLICAÇÃO
❌ /api/orders/list                 ← DUPLICAÇÃO
❌ /api/maintenance-orders          ← DUPLICAÇÃO
```

### **REDIRECIONAMENTOS PERMITIDOS (Temporários):**

```http
✅ /api/ordens → /api/orders (301)
✅ /api/ordens/:id → /api/orders/:id (301)
✅ /api/ordens/tecnico → /api/orders/technician (301)
✅ /api/ordens/calendario → /api/orders/calendar (301)
```

---

## 🏗️ **PADRÕES ARQUITETURAIS OBRIGATÓRIOS**

### **1. RESPOSTA PADRONIZADA**

```go
// ✅ OBRIGATÓRIO - Usar em TODOS os endpoints
type StandardResponse struct {
    Success bool        `json:"success"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    Meta    interface{} `json:"meta,omitempty"`
    Error   string      `json:"error,omitempty"`
}
```

### **2. FILTROS PADRONIZADOS**

```go
// ✅ OBRIGATÓRIO - Usar em TODOS os endpoints de listagem
type OrderFilters struct {
    Status       string `json:"status"`
    BranchID     uint   `json:"branch_id"`
    TechnicianID uint   `json:"technician_id"`
    StartDate    string `json:"start_date"`
    EndDate      string `json:"end_date"`
    Priority     string `json:"priority"`
    Search       string `json:"search"`
    ExcludeTest  bool   `json:"exclude_test"`
}
```

### **3. PAGINAÇÃO OBRIGATÓRIA**

```go
// ✅ OBRIGATÓRIO - Usar em TODOS os endpoints de listagem
type PaginationMeta struct {
    Page       int   `json:"page"`
    PageSize   int   `json:"page_size"`
    Total      int64 `json:"total"`
    TotalPages int   `json:"total_pages"`
}
```

---

## 🔒 **REGRAS DE SEGURANÇA OBRIGATÓRIAS**

### **1. ORDEM #18 - BLOQUEIO ABSOLUTO**

```go
// ✅ OBRIGATÓRIO - Verificar em TODOS os endpoints
if id == 18 || id == "18" {
    return StandardResponse{
        Success: false,
        Message: "Ordem não disponível",
        Error:   "Esta ordem não está disponível para visualização",
    }
}
```

### **2. VALIDAÇÃO OBRIGATÓRIA**

```go
// ✅ OBRIGATÓRIO - Validar TODOS os parâmetros
func validateOrderID(idStr string) (uint, error) {
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        return 0, errors.New("ID inválido")
    }
    if id == 18 {
        return 0, errors.New("Ordem não disponível")
    }
    return uint(id), nil
}
```

### **3. AUTENTICAÇÃO OBRIGATÓRIA**

```go
// ✅ OBRIGATÓRIO - Aplicar em TODAS as rotas protegidas
router.Use(middleware.AuthMiddleware())
router.Use(middleware.FilialMiddleware())
```

---

## ⚡ **OTIMIZAÇÕES OBRIGATÓRIAS**

### **1. CACHE OBRIGATÓRIO**

```javascript
// ✅ OBRIGATÓRIO - Implementar cache com TTL
const UnifiedCache = {
    data: new Map(),
    lastUpdate: new Date(),
    ttl: 5 * 60 * 1000, // 5 minutos
    
    get(key) {
        const now = new Date();
        if (this.data.has(key) && (now - this.lastUpdate) < this.ttl) {
            return this.data.get(key);
        }
        return null;
    }
};
```

### **2. QUERIES OTIMIZADAS**

```go
// ✅ OBRIGATÓRIO - Usar JOINs, evitar N+1 queries
func (r *Repository) GetAllOptimized(ctx context.Context, filters map[string]interface{}) {
    query := r.db.WithContext(ctx).
        Preload("Branch").
        Preload("Equipment").
        Preload("Technician").
        Where("id != ?", 18)
    
    // Aplicar filtros...
}
```

### **3. PAGINAÇÃO OBRIGATÓRIA**

```go
// ✅ OBRIGATÓRIO - Limitar resultados
func (r *Repository) GetAll(page, pageSize int) {
    if pageSize > 100 {
        pageSize = 100 // Máximo 100 itens por página
    }
    offset := (page - 1) * pageSize
    
    return r.db.Limit(pageSize).Offset(offset).Find(&orders)
}
```

---

## 🧪 **TESTES OBRIGATÓRIOS**

### **1. TESTES DE DUPLICAÇÃO**

```bash
# ✅ OBRIGATÓRIO - Executar antes de cada commit
./scripts/check-duplications.sh
```

### **2. TESTES DE ENDPOINTS**

```go
// ✅ OBRIGATÓRIO - Testar TODOS os endpoints
func TestUnifiedOrderEndpoints(t *testing.T) {
    // Testar /api/orders
    // Testar /api/orders/:id
    // Testar /api/orders/calendar
    // Testar /api/orders/technician
}
```

### **3. TESTES DE SEGURANÇA**

```go
// ✅ OBRIGATÓRIO - Testar bloqueio da ordem #18
func TestOrder18Blocked(t *testing.T) {
    response := callAPI("/api/orders/18")
    assert.Equal(t, 403, response.StatusCode)
}
```

---

## 🚫 **VIOLAÇÕES QUE BLOQUEIAM DEPLOY**

### **BLOQUEIO AUTOMÁTICO se detectado:**

1. ❌ **Handler duplicado** para ordens
2. ❌ **Rota duplicada** para ordens  
3. ❌ **JavaScript duplicado** para ordens
4. ❌ **Endpoint duplicado** (/api/ordens)
5. ❌ **URL antiga hardcoded** no código
6. ❌ **Ordem #18 não bloqueada**
7. ❌ **Resposta não padronizada**
8. ❌ **Paginação ausente**
9. ❌ **Cache não implementado**
10. ❌ **Validação ausente**

---

## 📊 **MÉTRICAS DE QUALIDADE OBRIGATÓRIAS**

### **METAS INVIOLÁVEIS:**

- **Duplicação:** 0% (zero tolerância)
- **Cobertura de testes:** ≥ 80%
- **Performance:** ≥ 90%
- **Manutenibilidade:** ≥ 85%

### **ALERTAS AUTOMÁTICOS:**

- **Duplicação > 0%** → Bloquear deploy
- **Performance < 80%** → Alerta crítico
- **Testes < 70%** → Alerta warning
- **Complexidade > 10** → Refatorar obrigatório

---

## 🔄 **PROCESSO DE DESENVOLVIMENTO OBRIGATÓRIO**

### **1. ANTES DE CODIFICAR:**
1. ✅ Verificar se já existe implementação
2. ✅ Consultar esta documentação
3. ✅ Usar apenas padrões aprovados

### **2. DURANTE O DESENVOLVIMENTO:**
1. ✅ Seguir estrutura unificada
2. ✅ Implementar validações obrigatórias
3. ✅ Usar cache e otimizações

### **3. ANTES DO COMMIT:**
1. ✅ Executar `make check-quality`
2. ✅ Executar `make deploy-check`
3. ✅ Verificar se hook passou

### **4. ANTES DO DEPLOY:**
1. ✅ Executar `make deploy-check`
2. ✅ Verificar métricas de qualidade
3. ✅ Confirmar zero duplicações

---

## 🎯 **RESPONSABILIDADES**

### **DESENVOLVEDOR:**
- Seguir arquitetura obrigatória
- Executar verificações antes do commit
- Não criar duplicações

### **TECH LEAD:**
- Revisar conformidade arquitetural
- Aprovar apenas código que segue padrões
- Manter documentação atualizada

### **SISTEMA AUTOMÁTICO:**
- Bloquear commits com duplicações
- Alertar sobre violações
- Gerar relatórios de qualidade

---

## ⚖️ **EXCEÇÕES**

### **ÚNICAS EXCEÇÕES PERMITIDAS:**

1. **Testes unitários** (arquivos *.test.go)
2. **Documentação** (arquivos *.md)
3. **Migrations** (arquivos de migração)
4. **Configurações** (arquivos de config)

### **PROCESSO PARA EXCEÇÕES:**

1. Documentar motivo da exceção
2. Aprovação do Tech Lead
3. Prazo definido para correção
4. Monitoramento contínuo

---

## 📞 **CONTATO**

**Em caso de dúvidas sobre arquitetura:**
- Consultar esta documentação
- Executar `make help`
- Verificar `.quality-rules.yaml`

**LEMBRE-SE:** Esta arquitetura é **OBRIGATÓRIA** e **NÃO NEGOCIÁVEL**. Qualquer violação será automaticamente bloqueada pelos sistemas de qualidade.
