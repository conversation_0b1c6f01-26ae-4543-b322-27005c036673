-- Migração incremental para adicionar colunas faltantes na tabela attachments
-- Data: 2025-05-25
-- Descrição: Adiciona colunas necessárias para o sistema de anexos moderno

-- Adicionar colunas básicas de arquivo
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS original_name TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS file_name TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS file_size BIGINT DEFAULT 0;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS mime_type TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS file_hash TEXT;

-- Adicionar colunas de categorização
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'outros';
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS title TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS tags TEXT;

-- Adicionar colunas de entidade polimórfica
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS entity_type TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS entity_id BIGINT;

-- Adicionar colunas de versionamento
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS parent_id BIGINT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS version_notes TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS is_latest_version BOOLEAN DEFAULT true;

-- Adicionar colunas de controle
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT false;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS has_thumbnail BOOLEAN DEFAULT false;

-- Adicionar colunas de imagem
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS image_width INTEGER;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS image_height INTEGER;

-- Adicionar colunas de auditoria
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS uploaded_by_id BIGINT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS uploaded_by_name TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS ip_address TEXT;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS user_agent TEXT;

-- Adicionar colunas de material (compatibilidade)
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS material_id BIGINT;

-- Atualizar dados existentes
UPDATE attachments SET 
    entity_type = 'maintenance_order',
    entity_id = maintenance_order_id,
    category = 'evidencia',
    is_public = false,
    is_active = true,
    version = 1,
    is_latest_version = true
WHERE maintenance_order_id IS NOT NULL AND entity_type IS NULL;

UPDATE attachments SET 
    entity_type = 'maintenance_activity',
    entity_id = maintenance_activity_id,
    category = 'evidencia',
    is_public = false,
    is_active = true,
    version = 1,
    is_latest_version = true
WHERE maintenance_activity_id IS NOT NULL AND entity_type IS NULL;

-- Criar índices para as novas colunas
CREATE INDEX IF NOT EXISTS idx_attachments_entity ON attachments(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_attachments_category ON attachments(category);
CREATE INDEX IF NOT EXISTS idx_attachments_file_hash ON attachments(file_hash);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_by ON attachments(uploaded_by_id);
CREATE INDEX IF NOT EXISTS idx_attachments_parent ON attachments(parent_id);
CREATE INDEX IF NOT EXISTS idx_attachments_latest ON attachments(is_latest_version) WHERE is_latest_version = true;
CREATE INDEX IF NOT EXISTS idx_attachments_active ON attachments(is_active) WHERE is_active = true;

-- Adicionar constraints
ALTER TABLE attachments ADD CONSTRAINT IF NOT EXISTS chk_attachments_category 
    CHECK (category IN ('tecnico', 'evidencia', 'nota_fiscal', 'contrato', 'certificado', 'manual', 'outros'));

ALTER TABLE attachments ADD CONSTRAINT IF NOT EXISTS chk_attachments_entity_type 
    CHECK (entity_type IN ('order', 'maintenance_order', 'maintenance_activity', 'equipment', 'technician', 'provider', 'material'));

ALTER TABLE attachments ADD CONSTRAINT IF NOT EXISTS chk_attachments_version 
    CHECK (version > 0);

ALTER TABLE attachments ADD CONSTRAINT IF NOT EXISTS chk_attachments_file_size 
    CHECK (file_size >= 0);

-- Comentários nas colunas
COMMENT ON COLUMN attachments.original_name IS 'Nome original do arquivo enviado pelo usuário';
COMMENT ON COLUMN attachments.file_name IS 'Nome do arquivo no sistema de arquivos';
COMMENT ON COLUMN attachments.file_hash IS 'Hash MD5 do arquivo para detecção de duplicatas';
COMMENT ON COLUMN attachments.category IS 'Categoria do anexo (tecnico, evidencia, nota_fiscal, etc.)';
COMMENT ON COLUMN attachments.entity_type IS 'Tipo da entidade à qual o anexo está vinculado';
COMMENT ON COLUMN attachments.entity_id IS 'ID da entidade à qual o anexo está vinculado';
COMMENT ON COLUMN attachments.version IS 'Número da versão do arquivo';
COMMENT ON COLUMN attachments.parent_id IS 'ID do anexo pai (para versionamento)';
COMMENT ON COLUMN attachments.is_latest_version IS 'Indica se esta é a versão mais recente';

-- Verificar resultado
SELECT 'Migração concluída com sucesso!' as status;
