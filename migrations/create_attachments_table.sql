-- Migração para criar tabela de anexos robusta
-- Sistema Tradição - Anexos

-- Criar tabela de anexos
CREATE TABLE IF NOT EXISTS attachments (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    -- Informações básicas do arquivo
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL UNIQUE,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL DEFAULT 0,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,

    -- Classificação
    type VARCHAR(20) NOT NULL DEFAULT 'other',
    category VARCHAR(50) NOT NULL DEFAULT 'other',

    -- Metadados
    title VARCHAR(255),
    description TEXT,
    tags TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,

    -- Informações de imagem (se aplicável)
    image_width INTEGER,
    image_height INTEGER,
    has_thumbnail BOOLEAN DEFAULT FALSE,

    -- Versionamento
    version INTEGER DEFAULT 1,
    parent_id INTEGER REFERENCES attachments(id),
    version_notes TEXT,
    is_latest_version BOOLEAN DEFAULT TRUE,

    -- Relacionamentos
    entity_type VARCHAR(50) NOT NULL,
    entity_id INTEGER NOT NULL,

    -- Auditoria
    uploaded_by_id INTEGER NOT NULL,
    uploaded_by_name VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,

    -- Campos legados (compatibilidade)
    maintenance_order_id INTEGER,
    maintenance_activity_id INTEGER,
    material_id INTEGER
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_attachments_entity ON attachments(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_attachments_type ON attachments(type);
CREATE INDEX IF NOT EXISTS idx_attachments_category ON attachments(category);
CREATE INDEX IF NOT EXISTS idx_attachments_hash ON attachments(file_hash);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_by ON attachments(uploaded_by_id);
CREATE INDEX IF NOT EXISTS idx_attachments_parent ON attachments(parent_id);
CREATE INDEX IF NOT EXISTS idx_attachments_latest ON attachments(is_latest_version) WHERE is_latest_version = TRUE;
CREATE INDEX IF NOT EXISTS idx_attachments_active ON attachments(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_attachments_deleted ON attachments(deleted_at) WHERE deleted_at IS NULL;

-- Índices legados para compatibilidade
CREATE INDEX IF NOT EXISTS idx_attachments_maintenance_order ON attachments(maintenance_order_id);
CREATE INDEX IF NOT EXISTS idx_attachments_maintenance_activity ON attachments(maintenance_activity_id);
CREATE INDEX IF NOT EXISTS idx_attachments_material ON attachments(material_id);

-- Constraints
ALTER TABLE attachments ADD CONSTRAINT chk_attachments_type 
    CHECK (type IN ('image', 'document', 'video', 'audio', 'other'));

ALTER TABLE attachments ADD CONSTRAINT chk_attachments_category 
    CHECK (category IN ('technical', 'evidence', 'invoice', 'contract', 'certificate', 'manual', 'other',
                       'equipment', 'equipment_before', 'equipment_after', 'part_old', 'part_new', 'report'));

ALTER TABLE attachments ADD CONSTRAINT chk_attachments_entity_type 
    CHECK (entity_type IN ('order', 'equipment', 'user', 'branch', 'technician', 'provider', 'activity', 'material'));

ALTER TABLE attachments ADD CONSTRAINT chk_attachments_version 
    CHECK (version > 0);

ALTER TABLE attachments ADD CONSTRAINT chk_attachments_file_size 
    CHECK (file_size >= 0);

-- Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION update_attachments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_attachments_updated_at
    BEFORE UPDATE ON attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_attachments_updated_at();

-- Função para garantir apenas uma versão mais recente por grupo
CREATE OR REPLACE FUNCTION ensure_single_latest_version()
RETURNS TRIGGER AS $$
BEGIN
    -- Se está marcando como versão mais recente
    IF NEW.is_latest_version = TRUE THEN
        -- Desmarcar outras versões do mesmo grupo
        UPDATE attachments 
        SET is_latest_version = FALSE 
        WHERE (parent_id = NEW.parent_id OR (parent_id IS NULL AND id = NEW.parent_id) OR id = NEW.parent_id)
          AND id != NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ensure_single_latest_version
    BEFORE INSERT OR UPDATE ON attachments
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_latest_version();

-- Comentários na tabela
COMMENT ON TABLE attachments IS 'Sistema robusto de anexos com versionamento e categorização';
COMMENT ON COLUMN attachments.original_name IS 'Nome original do arquivo enviado pelo usuário';
COMMENT ON COLUMN attachments.file_name IS 'Nome único do arquivo no sistema de arquivos';
COMMENT ON COLUMN attachments.file_path IS 'Caminho completo do arquivo no disco';
COMMENT ON COLUMN attachments.file_hash IS 'Hash MD5 do arquivo para detecção de duplicatas';
COMMENT ON COLUMN attachments.type IS 'Tipo do arquivo: image, document, video, audio, other';
COMMENT ON COLUMN attachments.category IS 'Categoria do anexo para organização';
COMMENT ON COLUMN attachments.entity_type IS 'Tipo da entidade à qual o anexo está vinculado';
COMMENT ON COLUMN attachments.entity_id IS 'ID da entidade à qual o anexo está vinculado';
COMMENT ON COLUMN attachments.version IS 'Número da versão do arquivo';
COMMENT ON COLUMN attachments.parent_id IS 'ID do arquivo pai para versionamento';
COMMENT ON COLUMN attachments.is_latest_version IS 'Indica se é a versão mais recente';

-- Inserir dados de exemplo (opcional)
-- INSERT INTO attachments (
--     original_name, file_name, file_path, file_size, mime_type, file_hash,
--     type, category, title, description, entity_type, entity_id,
--     uploaded_by_id, uploaded_by_name
-- ) VALUES (
--     'manual_equipamento.pdf', 'manual_equipamento_123456.pdf', 
--     'uploads/attachments/manual_equipamento_123456.pdf', 1024000, 'application/pdf',
--     'abc123def456', 'document', 'manual', 'Manual do Equipamento XYZ',
--     'Manual técnico do equipamento modelo XYZ', 'equipment', 1, 1, 'Admin'
-- );

-- Verificar se a migração foi aplicada
SELECT 'Tabela attachments criada com sucesso!' as status;
