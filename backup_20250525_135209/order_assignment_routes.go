package routes

import (
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// SetupOrderAssignmentRoutes configura as rotas para atribuição de ordens
func SetupOrderAssignmentRoutes(router *gin.Engine, authMiddleware gin.HandlerFunc) {
	// Grupo de rotas para a API de atribuição de ordens
	apiAssignments := router.Group("/api/order-assignments")
	apiAssignments.Use(authMiddleware)

	// Rotas para atribuição de ordens a técnicos
	// Apenas administradores, gerentes e prestadores podem atribuir ordens a técnicos
	technicianAssignments := apiAssignments.Group("/technician")
	technicianAssignments.Use(middleware.RoleMiddleware("admin", "gerente", "prestador"))
	{
		// Atribuir ordem a técnico
		technicianAssignments.POST("", handlers.AssignOrderToTechnicianHandler)

		// Compatibilidade com a rota antiga
		technicianAssignments.GET("", handlers.AssignOrderToTechnicianHandler)
	}

	// Rotas para atribuição de ordens a prestadores
	// Apenas administradores, gerentes e financeiro podem atribuir ordens a prestadores
	providerAssignments := apiAssignments.Group("/provider")
	providerAssignments.Use(permissions.RoleMiddleware("admin", "gerente", "financeiro"))
	{
		// Atribuir ordem a prestador
		providerAssignments.POST("", handlers.AssignOrderToProviderHandler)

		// Compatibilidade com a rota antiga
		providerAssignments.GET("", handlers.AssignOrderToProviderHandler)
	}

	// Manter compatibilidade com a rota antiga
	router.GET("/api/ordens/atribuir", authMiddleware, permissions.RoleMiddleware("admin", "gerente", "prestador"), handlers.AtribuirOrdemHandler)
}
