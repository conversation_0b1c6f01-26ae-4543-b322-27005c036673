#!/bin/bash

# <PERSON>ript para iniciar monitor em background

echo "🔍 INICIANDO MONITOR AUTOMÁTICO EM BACKGROUND"
echo "============================================="

# Função do monitor
monitor_function() {
    while true; do
        echo "[$(date '+%H:%M:%S')] Verificando duplicações..." >> logs/monitor_background.log
        
        if bash scripts/check-quick.sh >/dev/null 2>&1; then
            echo "[$(date '+%H:%M:%S')] ✅ Sistema OK" >> logs/monitor_background.log
            # Remover alerta se existir
            if [ -f "ALERTA_DUPLICACOES.txt" ]; then
                rm "ALERTA_DUPLICACOES.txt"
                echo "[$(date '+%H:%M:%S')] ✅ Alerta removido" >> logs/monitor_background.log
            fi
        else
            echo "[$(date '+%H:%M:%S')] ❌ Problemas detectados!" >> logs/monitor_background.log
            # Criar alerta
            cat > ALERTA_DUPLICACOES.txt << EOF
🚨 DUPLICAÇÕES DETECTADAS! 🚨
============================

Detectado em: $(date '+%Y-%m-%d %H:%M:%S')

PARA CORRIGIR:
1. Execute: bash scripts/check-quick.sh
2. Se necessário: bash scripts/clean-duplications.sh

Este arquivo será removido automaticamente 
quando as duplicações forem corrigidas.
EOF
            echo "[$(date '+%H:%M:%S')] 🚨 Alerta criado" >> logs/monitor_background.log
        fi
        
        sleep 60  # Verificar a cada 1 minuto
    done
}

# Criar diretório de logs
mkdir -p logs

# Verificar se já está rodando
if pgrep -f "monitor_function" >/dev/null; then
    echo "❌ Monitor já está rodando!"
    echo "Para parar: pkill -f monitor_function"
    exit 1
fi

# Iniciar monitor em background
echo "✅ Iniciando monitor..."
echo "📝 Log: logs/monitor_background.log"
echo "🚨 Alertas: ALERTA_DUPLICACOES.txt (se houver problemas)"
echo "🛑 Para parar: pkill -f monitor_function"
echo ""

# Executar em background
monitor_function &

echo "✅ Monitor iniciado em background (PID: $!)"
echo "📊 Para ver status: tail -f logs/monitor_background.log"
