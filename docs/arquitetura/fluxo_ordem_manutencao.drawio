<mxfile host="app.diagrams.net">
  <diagram name="Fluxo Ordem de Manutenção" id="fluxo-ordem-manutencao">
    <mxGraphModel dx="1000" dy="600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <!-- Usu<PERSON><PERSON> da Filial -->
        <mxCell id="2" value="Usuário da Filial" style="shape=mxgraph.office.user;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="120" width="120" height="60" as="geometry"/>
        </mxCell>
        <!-- Solicita -->
        <mxCell id="3" value="Solicita" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;" edge="1" parent="1" source="2" target="4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- Sistema Web/API -->
        <mxCell id="4" value="Sistema Web/API" style="shape=mxgraph.office.server;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="120" width="140" height="60" as="geometry"/>
        </mxCell>
        <!-- Criação Ordem -->
        <mxCell id="5" value="Criação Ordem" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="120" width="140" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="6" value="Processa" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;" edge="1" parent="1" source="4" target="5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- maintenance_orders -->
        <mxCell id="7" value="Tabela maintenance_orders" style="shape=mxgraph.office.database;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="660" y="120" width="180" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="8" value="Salva" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;" edge="1" parent="1" source="5" target="7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- branches -->
        <mxCell id="9" value="branches" style="shape=mxgraph.office.database;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="900" y="40" width="100" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="10" value="Relaciona" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="9">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- equipment -->
        <mxCell id="11" value="equipment" style="shape=mxgraph.office.database;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="900" y="100" width="100" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="12" value="Relaciona" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="11">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- users (Solicitante) -->
        <mxCell id="13" value="users (Solicitante)" style="shape=mxgraph.office.user;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="900" y="160" width="120" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="14" value="Relaciona" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="13">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- users (Aprovador) -->
        <mxCell id="15" value="users (Aprovador)" style="shape=mxgraph.office.user;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="900" y="220" width="120" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="16" value="Aprovação" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="15">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- users (Técnico) -->
        <mxCell id="17" value="users (Técnico)" style="shape=mxgraph.office.user;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="900" y="280" width="120" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="18" value="Atribuição" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="17">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- cost_items -->
        <mxCell id="19" value="cost_items" style="shape=mxgraph.office.database;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1100" y="100" width="100" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="20" value="Custos" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="19">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- maintenance_materials -->
        <mxCell id="21" value="maintenance_materials" style="shape=mxgraph.office.database;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1100" y="160" width="140" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="22" value="Materiais" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="21">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- interactions -->
        <mxCell id="23" value="interactions" style="shape=mxgraph.office.database;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1100" y="220" width="100" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="24" value="Interações" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="23">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- notes -->
        <mxCell id="25" value="notes" style="shape=mxgraph.office.database;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1100" y="280" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="26" value="Notas" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;dashed=1;" edge="1" parent="1" source="7" target="25">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <!-- Finalização -->
        <mxCell id="27" value="Ordem Concluída" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1320" y="180" width="160" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="28" value="Finaliza" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;strokeColor=#000000;" edge="1" parent="1" source="7" target="27">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 