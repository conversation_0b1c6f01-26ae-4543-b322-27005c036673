# Fluxo de Criação da Ordem de Manutenção

## Diagrama do Fluxo

```mermaid
flowchart TD
    A[Usuário da Filial] -->|Solicita| B[Criação Ordem de Manutenção]
    B -->|Salva| C[maintenance_orders]
    C -->|Relaciona| D[branches]
    C -->|Relaciona| E[equipment]
    C -->|Relaciona| F[users (Solicitante)]
    C -->|Aprovação| G[users (Aprovador)]
    C -->|Atribuição| H[users (Técnico)]
    C -->|Custos| I[cost_items]
    C -->|Materiais| J[maintenance_materials]
    C -->|Interações| K[interactions]
    C -->|Notas| L[notes]
    H -->|Executa Serviço| M[Atualiza Status/Interage]
    M -->|Finaliza| N[Ordem Concluída]
```

---

## Explicação do Fluxo

1. **Usuário da Filial** inicia a solicitação de uma ordem de manutenção.
2. **Criação da Ordem:**
   - Dados são enviados para o endpoint de criação.
   - Ordem é salva na tabela `maintenance_orders`.
   - Relacionamentos são criados com filial (`branches`), equipamento (`equipment`), e usuário solicitante (`users`).
3. **Aprovação:**
   - Se necessário, um aprovador (usuário) valida a ordem.
4. **Atribuição:**
   - Um técnico é designado para a ordem.
5. **Execução:**
   - Técnico executa o serviço, atualiza status e interage via comentários/interações.
   - Custos e materiais podem ser adicionados.
6. **Finalização:**
   - Ordem é concluída, status e campos finais são atualizados.

---

## Tabelas/Entidades Envolvidas

- **maintenance_orders**: Ordem principal, relaciona-se com todas as demais.
- **branches**: Filial que solicitou a ordem.
- **equipment**: Equipamento relacionado.
- **users**: Usuários (solicitante, técnico, aprovador).
- **cost_items**: Custos associados à ordem.
- **maintenance_materials**: Materiais usados na ordem.
- **interactions**: Interações e comentários.
- **notes**: Notas da ordem.

---

> **Observação:**
> O fluxo pode variar conforme regras de negócio (aprovação obrigatória, etapas extras, etc). Este diagrama cobre o fluxo padrão do sistema. 