# Anális<PERSON> das Requisições para Visualizar Ordens por Filial
_Análise realizada em 25/05/2025 - Verificação contra código real_

---

## RESUMO EXECUTIVO

**STATUS:** ✅ SISTEMA IMPLEMENTADO E FUNCIONAL
- **Filtro por filial:** ✅ IMPLEMENTADO
- **Controle de acesso:** ✅ ROBUSTO
- **Múltiplas rotas:** ✅ DISPONÍVEIS
- **Middleware de segurança:** ✅ ATIVO

---

## 1. ENDPOINTS PARA VISUALIZAR ORDENS POR FILIAL

### 1.1 Rota Principal - API Ordens
```
GET /api/ordens
```
- **Handler:** `ListarOrdensAPI`
- **Middleware:** `AuthMiddleware()` + `FilialMiddleware()`
- **Filtro automático:** Por `branch_id` do usuário conectado

### 1.2 Rota Específica por Filial
```
GET /filial/:filialID/ordens
```
- **Handler:** `OrdemV2Handler.ListarOrdensPorFilial`
- **Middleware:** `AuthMiddleware()`
- **Parâmetros:** `filialID` na URL

### 1.3 Rota de Controle por Branch
```
GET /api/maintenance-orders/branch/:branchID
```
- **Handler:** `MaintenanceOrderController.GetByBranch`
- **Validação:** ID da filial + role do usuário

---

## 2. SISTEMA DE FILTROS POR FILIAL

### 2.1 FilialMiddleware - Filtro Automático
**Arquivo:** `internal/middleware/filial_middleware.go`

**Funcionamento:**
1. Extrai `userID` do contexto de autenticação
2. Busca `BranchID` do usuário na tabela `users`
3. Adiciona `filialID` ao contexto da requisição
4. **APENAS** para usuários com role `filial` ou `branch_user`

**Código-chave:**
```go
// Para usuários de filial, mostrar apenas ordens da sua filial
if userRole == "filial" || userRole == "branch_user" {
    var user models.User
    if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
        query = query.Where("branch_id = ?", *user.BranchID)
    }
}
```

### 2.2 Repository - Filtros de Segurança
**Arquivo:** `internal/repository/maintenance_order_repository.go`

**Níveis de acesso:**
- **Admin/Gerente/Financeiro:** ✅ VÊ TODAS as ordens
- **Filial/Branch_user:** ❌ VÊ APENAS sua filial
- **Técnico:** ⚠️ VÊ ordens atribuídas + sua filial

**Implementação em TODOS os métodos:**
- `GetAllMaintenanceOrders()`
- `GetMaintenanceOrdersByStatus()`
- `GetRecentMaintenanceOrders()`
- `GetOverdueMaintenanceOrders()`
- `GetUpcomingMaintenanceOrders()`
- `GetMaintenanceOrdersByDateRange()`

---

## 3. CONTROLE DE ACESSO POR ROLE

### 3.1 Roles com Acesso Total
```go
if userRole != "admin" && userRole != "gerente" && userRole != "financeiro"
```
- **admin:** Acesso total
- **gerente:** Acesso total  
- **financeiro:** Acesso total

### 3.2 Roles com Filtro por Filial
```go
if userRole == "filial" || userRole == "branch_user"
```
- **filial:** Apenas sua filial
- **branch_user:** Apenas sua filial

### 3.3 Fallback para user_branches
```go
// Se não conseguir obter BranchID do usuário
var branchIDs []uint
if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil {
    query = query.Where("branch_id IN ?", branchIDs)
}
```

---

## 4. TABELAS DISPONÍVEIS AOS CLIENTES

### 4.1 Tabelas Principais ✅
- **`users`** - Usuários do sistema
- **`branches`** - Filiais/Estações
- **`equipment`** - Equipamentos
- **`maintenance_orders`** - Ordens de manutenção
- **`service_providers`** - Prestadores de serviço

### 4.2 Tabelas de Relacionamento ✅
- **`cost_items`** - Itens de custo das ordens
- **`interactions`** - Interações/Comentários
- **`maintenance_materials`** - Materiais utilizados
- **`technician_branches`** - Técnicos x Filiais
- **`user_branches`** - Usuários x Filiais (múltiplas)

### 4.3 Tabelas de Controle ✅
- **`equipment_types`** - Tipos de equipamento
- **`tags`** - Sistema de tags
- **`notifications`** - Notificações
- **`security_policies`** - Políticas de segurança
- **`calendar_events`** - Eventos de calendário
- **`schema_versions`** - Controle de versões

### 4.4 Tabelas de Junção ✅
- **`equipment_tags`** - Equipamentos x Tags
- **`provider_branches`** - Prestadores x Filiais
- **`technician_orders`** - Técnicos x Ordens

---

## 5. FLUXO DE REQUISIÇÃO COMPLETO

### 5.1 Usuário Filial Fazendo Requisição
```
1. GET /api/ordens
2. AuthMiddleware() → Valida JWT, extrai userID
3. FilialMiddleware() → Busca BranchID do usuário
4. ListarOrdensAPI() → Chama serviço
5. Repository → Aplica filtro WHERE branch_id = ?
6. Retorna apenas ordens da filial do usuário
```

### 5.2 Usuário Admin Fazendo Requisição  
```
1. GET /api/ordens
2. AuthMiddleware() → Valida JWT, extrai userID
3. FilialMiddleware() → Detecta role admin, não filtra
4. ListarOrdensAPI() → Chama serviço
5. Repository → NÃO aplica filtro de filial
6. Retorna TODAS as ordens do sistema
```

---

## 6. LOGS DE SEGURANÇA

O sistema implementa logs detalhados:
```go
log.Printf("[SECURITY] Filtrando ordens para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
log.Printf("[FILTER] Filtrando por filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
```

---

## CONCLUSÃO

✅ **SISTEMA ROBUSTO E SEGURO**
- Filtros automáticos por filial implementados
- Controle granular por role
- Múltiplas camadas de segurança
- Logs de auditoria ativos
- Fallbacks para casos especiais
