package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"tradicao/internal/database"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// AssignOrderToTechnicianHandler atribui uma ordem a um técnico usando o serviço centralizado
func AssignOrderToTechnicianHandler(c *gin.Context) {
	// Obter parâmetros da requisição
	orderIDStr := c.<PERSON>ry("order_id")
	technicianIDStr := c.<PERSON>ry("technician_id")

	// Validar parâmetros
	if orderIDStr == "" || technicianIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Parâmetros order_id e technician_id são obrigatórios",
		})
		return
	}

	// Converter para inteiros
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID de ordem inválido: " + err.Error(),
		})
		return
	}

	technicianID, err := strconv.ParseUint(technicianIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID de técnico inválido: " + err.Error(),
		})
		return
	}

	// Obter ID do usuário que está fazendo a atribuição
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Usuário não autenticado",
		})
		return
	}

	// Converter userID para uint
	var assignedByID uint
	switch v := userIDInterface.(type) {
	case uint:
		assignedByID = v
	case int:
		assignedByID = uint(v)
	case int64:
		assignedByID = uint(v)
	case float64:
		assignedByID = uint(v)
	case string:
		// Converter string para uint
		userIDInt, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro interno do servidor",
			})
			return
		}
		assignedByID = uint(userIDInt)
	default:
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro interno do servidor",
		})
		return
	}

	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Criar instâncias dos serviços necessários
	technicianOrderRepo := repository.NewTechnicianOrderRepository(db)
	permissionService := services.NewPermissionAssignmentService(db, technicianOrderRepo, nil)
	orderAssignmentService := services.NewOrderAssignmentService(db, technicianOrderRepo, permissionService)

	// Atribuir a ordem ao técnico
	err = orderAssignmentService.AssignOrderToTechnician(uint(orderID), uint(technicianID), assignedByID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao atribuir ordem ao técnico: " + err.Error(),
		})
		return
	}

	// Retornar sucesso
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Ordem %d atribuída com sucesso ao técnico %d", orderID, technicianID),
		"data": gin.H{
			"order_id":      orderID,
			"technician_id": technicianID,
		},
	})
}

// AssignOrderToProviderHandler atribui uma ordem a um prestador de serviço usando o serviço centralizado
func AssignOrderToProviderHandler(c *gin.Context) {
	// Obter parâmetros da requisição
	orderIDStr := c.Query("order_id")
	providerIDStr := c.Query("provider_id")

	// Validar parâmetros
	if orderIDStr == "" || providerIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Parâmetros order_id e provider_id são obrigatórios",
		})
		return
	}

	// Converter para inteiros
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID de ordem inválido: " + err.Error(),
		})
		return
	}

	providerID, err := strconv.ParseUint(providerIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID de prestador inválido: " + err.Error(),
		})
		return
	}

	// Obter ID do usuário que está fazendo a atribuição
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Usuário não autenticado",
		})
		return
	}

	// Converter userID para uint
	var assignedByID uint
	switch v := userIDInterface.(type) {
	case uint:
		assignedByID = v
	case int:
		assignedByID = uint(v)
	case int64:
		assignedByID = uint(v)
	case float64:
		assignedByID = uint(v)
	case string:
		// Converter string para uint
		userIDInt, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro interno do servidor",
			})
			return
		}
		assignedByID = uint(userIDInt)
	default:
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro interno do servidor",
		})
		return
	}

	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Criar instâncias dos serviços necessários
	technicianOrderRepo := repository.NewTechnicianOrderRepository(db)
	permissionService := services.NewPermissionAssignmentService(db, technicianOrderRepo, nil)
	orderAssignmentService := services.NewOrderAssignmentService(db, technicianOrderRepo, permissionService)

	// Atribuir a ordem ao prestador
	err = orderAssignmentService.AssignOrderToProvider(uint(orderID), uint(providerID), assignedByID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao atribuir ordem ao prestador: " + err.Error(),
		})
		return
	}

	// Retornar sucesso
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Ordem %d atribuída com sucesso ao prestador %d", orderID, providerID),
		"data": gin.H{
			"order_id":    orderID,
			"provider_id": providerID,
		},
	})
}
