package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// MaintenanceOrderHandler representa o handler de ordens de manutenção
type MaintenanceOrderHandler struct {
	service *services.MaintenanceOrderService
}

// NewMaintenanceOrderHandler cria uma nova instância do handler
func NewMaintenanceOrderHandler(service *services.MaintenanceOrderService) *MaintenanceOrderHandler {
	return &MaintenanceOrderHandler{
		service: service,
	}
}

// GetAll retorna todas as ordens de manutenção
func (h *MaintenanceOrderHandler) GetAll(c *gin.Context) {
	// Obter parâmetros de paginação
	page, _ := strconv.Atoi(c.Default<PERSON>uer<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("limit", "10"))

	// Obter filtros
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if branchID := c.Query("branch_id"); branchID != "" {
		filters["branch_id"] = branchID
	}
	if equipmentID := c.Query("equipment_id"); equipmentID != "" {
		filters["equipment_id"] = equipmentID
	}
	// Adicionar filtro por técnico se fornecido
	if technicianID := c.Query("technician_id"); technicianID != "" {
		techID, err := strconv.ParseUint(technicianID, 10, 32)
		if err == nil {
			filters["technician_id"] = uint(techID)
			// Log para debug
			fmt.Printf("[DEBUG] Filtrando ordens por técnico ID: %d\n", techID)
		}
	}
	// Adicionar filtro por data se fornecido
	if date := c.Query("date"); date != "" {
		filters["date"] = date
	}
	// Adicionar filtro por mês e ano se fornecidos
	if month := c.Query("month"); month != "" {
		filters["month"] = month
	}
	if year := c.Query("year"); year != "" {
		filters["year"] = year
	}

	// Obter usuário do contexto
	userID := c.GetInt64("user_id")
	userRole := c.GetString("user_role")

	orders, total, err := h.service.GetAll(c.Request.Context(), filters, userID, userRole, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": orders,
		"meta": gin.H{
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// GetOrderByID retorna uma ordem de manutenção pelo ID
func (h *MaintenanceOrderHandler) GetOrderByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	order, err := h.service.GetOrderByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// CreateOrder cria uma nova ordem de manutenção
func (h *MaintenanceOrderHandler) CreateOrder(c *gin.Context) {
	var order models.MaintenanceOrder
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.service.CreateOrder(c.Request.Context(), &order); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// UpdateOrder atualiza uma ordem de manutenção existente
func (h *MaintenanceOrderHandler) UpdateOrder(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var order models.MaintenanceOrder
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Definir o ID da ordem
	order.ID = uint(id)

	if err := h.service.UpdateOrder(c.Request.Context(), &order); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// DeleteOrder remove uma ordem de manutenção
func (h *MaintenanceOrderHandler) DeleteOrder(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.service.DeleteOrder(c.Request.Context(), uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Ordem de manutenção removida com sucesso"})
}

// AddNote adiciona uma nota a uma ordem de manutenção
func (h *MaintenanceOrderHandler) AddNote(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var note struct {
		Content string `json:"content" binding:"required"`
	}
	if err := c.ShouldBindJSON(&note); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetInt64("user_id")

	if err := h.service.AddNote(c.Request.Context(), id, note.Content, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Nota adicionada com sucesso"})
}

// AddMaterial adiciona um material a uma ordem de manutenção
func (h *MaintenanceOrderHandler) AddMaterial(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var material models.MaterialRequest
	if err := c.ShouldBindJSON(&material); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetInt64("user_id")

	if err := h.service.AddMaterial(c.Request.Context(), id, material, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Material adicionado com sucesso"})
}

// GetMetrics retorna as métricas das ordens de manutenção
func (h *MaintenanceOrderHandler) GetMetrics(c *gin.Context) {
	// Obter filtros
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if branchID := c.Query("branch_id"); branchID != "" {
		filters["branch_id"] = branchID
	}

	// Obter usuário do contexto
	userID := c.GetInt64("user_id")
	userRole := c.GetString("user_role")

	metrics, err := h.service.GetMetrics(c.Request.Context(), filters, userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetOrdersByStatus retorna as ordens de manutenção por status
func (h *MaintenanceOrderHandler) GetOrdersByStatus(c *gin.Context) {
	status := c.Param("status")
	if status == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Status é obrigatório"})
		return
	}

	userID := c.GetInt64("user_id")
	userRole := c.GetString("user_role")

	orders, err := h.service.GetOrdersByStatus(c.Request.Context(), status, userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetOrdersByBranch retorna as ordens de manutenção por filial
func (h *MaintenanceOrderHandler) GetOrdersByBranch(c *gin.Context) {
	branchID, err := strconv.ParseUint(c.Param("branch_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	userID := c.GetInt64("user_id")
	userRole := c.GetString("user_role")

	orders, err := h.service.GetOrdersByBranch(c.Request.Context(), uint(branchID), userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetOrdersByEquipment retorna as ordens de manutenção por equipamento
func (h *MaintenanceOrderHandler) GetOrdersByEquipment(c *gin.Context) {
	equipmentID, err := strconv.ParseUint(c.Param("equipment_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do equipamento inválido"})
		return
	}

	userID := c.GetInt64("user_id")
	userRole := c.GetString("user_role")

	orders, err := h.service.GetOrdersByEquipment(c.Request.Context(), uint(equipmentID), userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetOrdersByServiceProvider retorna as ordens de manutenção por prestador de serviço
func (h *MaintenanceOrderHandler) GetOrdersByServiceProvider(c *gin.Context) {
	providerID, err := strconv.ParseUint(c.Param("provider_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	userID := c.GetInt64("user_id")
	userRole := c.GetString("user_role")

	orders, err := h.service.GetOrdersByServiceProvider(c.Request.Context(), uint(providerID), userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetOrderCosts retorna os custos de uma ordem de manutenção
func (h *MaintenanceOrderHandler) GetOrderCosts(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	costs, err := h.service.GetOrderCosts(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, costs)
}

// RemoveTestOrders remove ordens de simulação/teste do sistema
// Requer perfil de administrador
func (h *MaintenanceOrderHandler) RemoveTestOrders(c *gin.Context) {
	// Verificar se o usuário tem permissão (apenas administradores)
	userRole := c.GetString("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Apenas administradores podem executar esta operação"})
		return
	}

	// Executar a remoção de ordens de teste
	if err := h.service.RemoveTestOrders(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Erro ao remover ordens de teste: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Ordens de teste removidas com sucesso"})
}
