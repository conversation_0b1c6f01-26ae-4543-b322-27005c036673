package routes

import (
	"net/http"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/models"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// SetupUnifiedOrderRoutes configura todas as rotas relacionadas a ordens de serviço
func SetupUnifiedOrderRoutes(router *gin.Engine, orderHandler *handlers.OrderHandler, authMiddleware gin.HandlerFunc) {
	// API Routes - Grupo de rotas para a API de ordens de serviço
	apiOrders := router.Group("/api/orders")
	apiOrders.Use(authMiddleware)                // Middleware de autenticação
	apiOrders.Use(middleware.FilialMiddleware()) // Middleware de filial para contexto

	// Rotas para listagem e criação
	apiOrders.GET("", orderHandler.ListOrders)
	apiOrders.POST("", orderHandler.CreateOrder)

	// Rotas para uma ordem específica
	apiOrder := apiOrders.Group("/:id")
	{
		apiOrder.GET("", orderHandler.GetOrder)
		apiOrder.PUT("", orderHandler.UpdateOrder)
		apiOrder.PATCH("/status", orderHandler.UpdateOrderStatus)
		apiOrder.POST("/assign", orderHandler.AssignProvider)

		// Rotas para custos
		apiOrder.GET("/costs", orderHandler.GetCosts)
		apiOrder.POST("/costs", orderHandler.AddCost)

		// Rotas para interações
		apiOrder.POST("/interactions", orderHandler.AddInteraction)

		// Rotas para anexos
		apiOrder.POST("/attachments", orderHandler.UploadOrderAttachment)
		apiOrder.GET("/attachments", orderHandler.GetOrderAttachments)
	}

	// Adicionar rotas para os cards de manutenção no formato /api/ordens/:id/...
	// Estas rotas são usadas pelo frontend
	apiOrdens := router.Group("/api/ordens")
	apiOrdens.Use(authMiddleware)                // Middleware de autenticação
	apiOrdens.Use(middleware.FilialMiddleware()) // Middleware de filial para contexto

	// Rota para listar ordens do técnico
	apiOrdens.GET("/tecnico", handlers.GetOrdemTecnicoHandler)

	// Rota para obter ordens para o calendário
	apiOrdens.GET("/calendario", handlers.GetCalendarOrdersHandler)

	// Rota para atribuir uma ordem a um técnico específico
	apiOrdens.GET("/atribuir", handlers.AtribuirOrdemHandler)

	// API Routes - Grupo de rotas para a API de documentos
	apiDocumentos := router.Group("/api/documentos")
	apiDocumentos.Use(authMiddleware)
	{
		// Rota para obter um documento específico
		apiDocumentos.GET("/:id", handlers.GetDocumentoHandler)
	}

	// Rotas para uma ordem específica
	apiOrdem := apiOrdens.Group("/:id")
	{
		// Rotas para os cards de manutenção
		apiOrdem.POST("/manutencao", middleware.OrderUpdateMiddleware(), handlers.SaveManutencaoHandler)
		apiOrdem.POST("/custos", middleware.OrderUpdateMiddleware(), handlers.SaveCustosHandler)
		apiOrdem.POST("/cronograma", middleware.OrderUpdateMiddleware(), handlers.SaveCronogramaHandler)
		apiOrdem.POST("/chat", middleware.OrderUpdateMiddleware(), handlers.SaveChatMessageHandler)
		// Removido middleware de verificação de permissão para permitir que técnicos
		// vejam todas as ordens listadas para eles
		apiOrdem.GET("", handlers.GetOrdemHandler)
		// Rota para atualização de dados da ordem
		apiOrdem.POST("/update", middleware.OrderUpdateMiddleware(), handlers.UpdateOrdemHandler)
	}

	// Web Routes - Grupo de rotas para a interface web de ordens de serviço
	webOrders := router.Group("/orders")
	webOrders.Use(authMiddleware)
	webOrders.Use(middleware.FilialMiddleware()) // Middleware de filial para contexto
	{
		// Listagem de ordens
		webOrders.GET("", orderHandler.ListOrdersPage)

		// Criação de ordens
		webOrders.GET("/create", orderHandler.ShowCreateOrderForm)

		// Detalhes de uma ordem específica
		webOrders.GET("/:id", orderHandler.ShowOrderDetail)

		// Edição de uma ordem específica
		webOrders.GET("/:id/edit", orderHandler.ShowEditOrderForm)

		// Calendário de ordens
		webOrders.GET("/calendar", orderHandler.ShowOrderCalendar)
	}

	// Rota específica para visualização detalhada de ordens para admins, gerentes e financeiro
	// Esta rota não tem verificação de vinculação com filial ou equipamento
	detailOrders := router.Group("/ordens/detalhes")
	detailOrders.Use(authMiddleware)
	detailOrders.Use(permissions.RoleMiddleware("admin", "gerente", "financeiro", "technician", "provider"))
	{
		detailOrders.GET("/:id", func(c *gin.Context) {
			id := c.Param("id")
			userRole := c.GetString("userRole")

			// Se for admin, gerente ou financeiro, permite acesso direto
			// Outros perfis passarão pela verificação normal no handler original
			if userRole == string(models.RoleAdmin) || userRole == string(models.RoleGerente) || userRole == string(models.RoleFinanceiro) {
				c.Set("bypass_permission_check", true)
				c.Set("admin_access", true)
			}

			// Redirecionamos para o handler padrão
			c.Request.URL.Path = "/orders/" + id
			router.HandleContext(c)
		})
	}

	// Configurar redirecionamentos para manter compatibilidade com código existente
	router.GET("/ordens", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/orders")
	})

	router.GET("/ordens/:id", func(c *gin.Context) {
		id := c.Param("id")
		c.Redirect(http.StatusMovedPermanently, "/orders/"+id)
	})

	router.GET("/ordens/create", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/orders/create")
	})

	router.GET("/ordens/:id/edit", func(c *gin.Context) {
		id := c.Param("id")
		c.Redirect(http.StatusMovedPermanently, "/orders/"+id+"/edit")
	})

	router.GET("/ordens/calendar", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/orders/calendar")
	})
}
