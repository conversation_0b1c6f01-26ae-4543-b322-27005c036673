package handlers

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"
	"tradicao/internal/db"
	"tradicao/internal/models" // Ajuste para o caminho real dos seus modelos
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	// Exemplo, se usar GORM para erros específicos como RecordNotFound
)

// Placeholder para a interface do serviço de ordens
// Você precisará definir essa interface e sua implementação
type OrderService interface {
	GetOrderDetails(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error) // Exemplo de retorno completo
	GetCostsByOrderID(orderID uint) ([]models.CostItem, error)
	AddCost(orderID uint, userID uint, cost models.CostItem) (*models.CostItem, error)
	UpdateStatus(orderID uint, userID uint, newStatus models.OrderStatus, reason string) (*models.MaintenanceOrder, error)
	AssignProvider(orderID uint, userID uint, providerID uint) (*models.MaintenanceOrder, error)
	SubmitForApproval(orderID uint, userID uint) (*models.MaintenanceOrder, error)
	Approve(orderID uint, userID uint) (*models.MaintenanceOrder, error)
	Reject(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error)
	Cancel(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error)
	AddInteraction(orderID uint, userID uint, message string) (*models.Interaction, error)
	UploadInvoice(orderID uint, userID uint, invoiceData models.Invoice, attachment models.Attachment) (*models.Invoice, error)
	GetOrdersWithPagination(offset, limit int, status, branchID, startDate, endDate string) ([]models.MaintenanceOrder, int, error)
	GetOrderCountsByStatus() (pendingCount, inProgressCount, completedCount int)
	GetOrderInteractions(orderID, offset, limit uint) ([]models.Interaction, error)
	GetOrderCosts(orderID uint) ([]models.CostItem, error)
}

// OrderHandler contém as dependências para os handlers de ordem
type OrderHandler struct {
	Service services.OrderServiceInterface
}

// NewOrderHandler cria uma nova instância de OrderHandler
func NewOrderHandler(service services.OrderServiceInterface) *OrderHandler {
	return &OrderHandler{Service: service}
}

// Helper para obter o ID do usuário do contexto
func getUserIDFromContext(c *gin.Context) (uint, error) {
	userIDAny, exists := c.Get("userID")
	if !exists {
		return 0, errors.New("ID do usuário não encontrado no contexto")
	}
	userID, ok := userIDAny.(uint)
	if !ok {
		switch v := userIDAny.(type) {
		case float64:
			userID = uint(v)
			ok = true
		case int:
			userID = uint(v)
			ok = true
		case int64:
			userID = uint(v)
			ok = true
		}
		if !ok {
			return 0, errors.New("ID do usuário no contexto não é um tipo numérico conversível para uint")
		}
	}
	if userID == 0 {
		return 0, errors.New("ID do usuário inválido (0)")
	}
	return userID, nil
}

// Helper para obter o Role do usuário do contexto
func getUserRoleFromContext(c *gin.Context) (models.UserRole, error) {
	userRoleAny, exists := c.Get("userRole")
	if !exists {
		return "", errors.New("Role do usuário não encontrado no contexto")
	}
	userRoleStr, ok := userRoleAny.(string)
	if !ok {
		return "", errors.New("Role do usuário no contexto não é string")
	}
	switch models.UserRole(userRoleStr) {
	case models.RoleFilial, models.RolePrestador, models.RoleGerente, models.RoleFinanceiro, models.RoleAdmin:
		return models.UserRole(userRoleStr), nil
	default:
		return "", errors.New("Role do usuário inválido no contexto")
	}
}

// ListOrders retorna a lista de ordens de serviço com paginação e cache
// GET /api/orders
func (h *OrderHandler) ListOrders(c *gin.Context) {
	// Parâmetros de paginação
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// Filtros
	status := c.Query("status")
	branchID := c.Query("branch_id")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// Validar parâmetros
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Calcular offset
	offset := (page - 1) * limit

	// Obter ordens com paginação e filtros
	orders, total, err := h.Service.GetOrdersWithPagination(offset, limit, status, branchID, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calcular métricas
	pendingCount, inProgressCount, completedCount := h.Service.GetOrderCountsByStatus()

	c.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"pagination": gin.H{
			"total": total,
			"page":  page,
			"limit": limit,
			"pages": (total + limit - 1) / limit,
		},
		"metrics": gin.H{
			"pending":     pendingCount,
			"in_progress": inProgressCount,
			"completed":   completedCount,
			"total":       total,
		},
	})
}

// GetOrder retorna os detalhes de uma ordem de serviço com cache
// GET /api/orders/:id
func (h *OrderHandler) GetOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Obter detalhes da ordem com cache
	order, branch, equipment, createdBy, assignedTo, provider, err := h.Service.GetOrderDetailsByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Obter interações e custos com paginação
	interactions, _ := h.Service.GetOrderInteractions(uint(id), 0, 5)
	costs, _ := h.Service.GetOrderCosts(uint(id))

	c.JSON(http.StatusOK, gin.H{
		"order":        order,
		"branch":       branch,
		"equipment":    equipment,
		"created_by":   createdBy,
		"assigned_to":  assignedTo,
		"provider":     provider,
		"interactions": interactions,
		"costs":        costs,
	})
}

// CreateOrder cria uma nova ordem de serviço com validações
// POST /api/orders
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	var order models.MaintenanceOrder
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validações básicas
	if order.Title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Título é obrigatório"})
		return
	}
	if order.BranchID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Filial é obrigatória"})
		return
	}
	if order.EquipmentID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Equipamento é obrigatório"})
		return
	}

	// Definir status inicial
	order.Status = models.StatusPending
	order.CreatedAt = time.Now()

	// Criar a ordem
	err := h.Service.CreateOrder(c.Request.Context(), &order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// UpdateOrder atualiza uma ordem de serviço existente
// PUT /api/orders/:id
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var order models.MaintenanceOrder
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Definir o ID da ordem
	order.ID = uint(id)
	err = h.Service.UpdateOrder(c.Request.Context(), &order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// UpdateOrderStatus atualiza o status de uma ordem de serviço
// PATCH /api/orders/:id/status
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
		Reason string `json:"reason"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	err = h.Service.UpdateStatus(c.Request.Context(), uint(id), models.OrderStatus(req.Status), userID, req.Reason)
	var order *models.MaintenanceOrder
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// AssignProvider atribui um prestador a uma ordem de serviço
// POST /api/orders/:id/assign
func (h *OrderHandler) AssignProvider(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var req struct {
		ProviderID uint `json:"provider_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	order, err := h.Service.AssignProvider(uint(id), userID, req.ProviderID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// AddCost adiciona um item de custo a uma ordem de serviço
// POST /api/orders/:id/costs
func (h *OrderHandler) AddCost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var cost models.CostItem
	if err := c.ShouldBindJSON(&cost); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	addedCost, err := h.Service.AddCost(uint(id), userID, cost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, addedCost)
}

// GetCosts retorna os itens de custo de uma ordem de serviço
// GET /api/orders/:id/costs
func (h *OrderHandler) GetCosts(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	costs, err := h.Service.GetCostsByOrderID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, costs)
}

// AddInteraction adiciona uma interação a uma ordem de serviço
// POST /api/orders/:id/interactions
func (h *OrderHandler) AddInteraction(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var req struct {
		Message string `json:"message" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	interaction, err := h.Service.AddInteraction(uint(id), userID, req.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, interaction)
}

// --- Handlers para a página HTML ---

// ShowOrderDetail renderiza a página de detalhes da ordem.
// GET /orders/:id
func (h *OrderHandler) ShowOrderDetail(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")

	// Converter para uint
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "ID inválido: " + err.Error(),
		})
		return
	}

	// Verificar se o acesso foi liberado por ser admin/gerente/financeiro
	bypassAccess, _ := c.Get("bypass_access_check")
	bypassAccessBool, _ := bypassAccess.(bool)

	// Obter detalhes da ordem usando o ID
	order, branch, equipment, createdBy, assignedTo, provider, err := h.Service.GetOrderDetailsByID(uint(id))
	if err != nil {
		// Se o acesso foi liberado e o erro for de permissão, tente buscar os detalhes sem verificação de permissão
		if bypassAccessBool && strings.Contains(err.Error(), "permissão") {
			// Tentar obter detalhes da ordem diretamente do banco
			db := c.MustGet("db").(*gorm.DB)
			var maintenanceOrder models.MaintenanceOrder

			if dbErr := db.First(&maintenanceOrder, id).Error; dbErr != nil {
				c.HTML(http.StatusInternalServerError, "error.html", gin.H{
					"error": "Erro ao carregar detalhes da ordem: " + dbErr.Error(),
				})
				return
			}

			// Buscar detalhes relacionados
			var branchModel models.Branch
			db.First(&branchModel, maintenanceOrder.BranchID)

			var equipmentModel models.Equipment
			db.First(&equipmentModel, maintenanceOrder.EquipmentID)

			var createdByUser, assignedToUser, providerUser models.User
			db.First(&createdByUser, maintenanceOrder.CreatedByUserID)

			if maintenanceOrder.AssignedToUserID > 0 {
				db.First(&assignedToUser, maintenanceOrder.AssignedToUserID)
			}

			if maintenanceOrder.ServiceProviderID != nil {
				db.First(&providerUser, *maintenanceOrder.ServiceProviderID)
			}

			// Usar os dados obtidos diretamente
			order = &maintenanceOrder
			branch = &branchModel
			equipment = &equipmentModel
			createdBy = &createdByUser
			assignedTo = &assignedToUser
			provider = &providerUser
		} else {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": "Erro ao carregar detalhes da ordem: " + err.Error(),
			})
			return
		}
	}

	// Obter usuário atual
	userID, _ := getUserIDFromContext(c)
	userRole, _ := c.Get("userRole")
	userName, _ := c.Get("userName")

	// Renderizar a página de detalhes
	c.HTML(http.StatusOK, "ordens/order_detail_service.html", gin.H{
		"title":       "Detalhes da Ordem #" + idStr,
		"page":        "order_detail",
		"ActivePage":  "orders",
		"Order":       order,
		"Branch":      branch,
		"Equipment":   equipment,
		"CreatedUser": createdBy,
		"AssignedTo":  assignedTo,
		"Provider":    provider,
		"CurrentUser": gin.H{
			"ID":   userID,
			"Role": userRole,
			"Name": userName,
		},
	})
}

// --- Handlers de Upload (Placeholders) ---

func (h *OrderHandler) UploadOrderInvoice(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Handler UploadOrderInvoice não implementado", "orderId": c.Param("id")})
}

func (h *OrderHandler) GetOrderInvoice(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Handler GetOrderInvoice não implementado", "orderId": c.Param("id")})
}

func (h *OrderHandler) GetOrderAttachments(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Handler GetOrderAttachments não implementado", "orderId": c.Param("id")})
}

func (h *OrderHandler) UploadOrderAttachment(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Obter ID do usuário
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	// Obter arquivo do formulário
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Arquivo não fornecido ou inválido"})
		return
	}

	// Verificar tamanho máximo (5MB)
	if file.Size > 5*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tamanho máximo de arquivo excedido (5MB)"})
		return
	}

	// Obter descrição
	description := c.PostForm("description")
	if description == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Descrição é obrigatória"})
		return
	}

	// Obter serviço de upload
	uploadService := services.NewFileUploadService("uploads")

	// Fazer upload do arquivo
	// Usar o tipo de upload para documentos (como não existe UploadTypeDocument, usamos UploadTypeLogo)
	filePath, err := uploadService.UploadFile(file, services.UploadTypeLogo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao fazer upload do arquivo", "details": err.Error()})
		return
	}

	// Criar objeto de anexo
	attachment := models.Attachment{
		File:               filePath,
		Type:               models.AttachmentTypeDocument,
		Description:        description,
		MaintenanceOrderID: uint(id),
		UploadedBy:         userID,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Salvar anexo no banco de dados
	db := db.GetDB()
	if err := db.Create(&attachment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao salvar anexo no banco de dados", "details": err.Error()})
		return
	}

	// Adicionar interação informando sobre o anexo
	interaction := models.Interaction{
		MaintenanceOrderID: uint(id),
		UserID:             userID,
		Message:            fmt.Sprintf("Anexo adicionado: %s", description),
		Timestamp:          time.Now(),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	if err := db.Create(&interaction).Error; err != nil {
		log.Printf("Erro ao criar interação para anexo: %v", err)
		// Não retornar erro, pois o anexo já foi salvo
	}

	// Retornar sucesso
	c.JSON(http.StatusCreated, gin.H{
		"message":    "Anexo enviado com sucesso",
		"attachment": attachment,
	})
}
