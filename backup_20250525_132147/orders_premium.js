// JavaScript Premium para a página de Ordens de Serviço

document.addEventListener('DOMContentLoaded', function() {
    // Detectar dispositivo móvel
    const isMobile = window.innerWidth <= 768;

    // Ajustes para dispositivos móveis
    if (isMobile) {
        // Simplificar a tabela em dispositivos móveis
        const tableHeaders = document.querySelectorAll('.orders-table th');
        const tableRows = document.querySelectorAll('.orders-table tr');

        // Ajustar tamanho da fonte e padding em dispositivos móveis
        document.querySelectorAll('.orders-table td, .orders-table th').forEach(cell => {
            cell.style.padding = '10px 8px';
        });

        // Ajustar tamanho dos botões de ação em dispositivos móveis
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.style.width = '32px';
            btn.style.height = '32px';
        });
    }

    // Adicionar listener para redimensionamento da janela
    window.addEventListener('resize', function() {
        const currentIsMobile = window.innerWidth <= 768;
        if (currentIsMobile !== isMobile) {
            // Recarregar a página se mudar entre desktop e mobile
            window.location.reload();
        }
    });

    // Botões de visualização (Tabela/Cards)
    const tableViewBtn = document.getElementById('tableViewBtn');
    const cardViewBtn = document.getElementById('cardViewBtn');

    if (tableViewBtn && cardViewBtn) {
        tableViewBtn.addEventListener('click', function() {
            tableViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
            // Implementar a lógica para mostrar a visualização em tabela
            document.querySelector('.table-responsive').style.display = 'block';
            // Aqui você pode adicionar código para esconder a visualização em cards
            // quando ela for implementada
        });

        cardViewBtn.addEventListener('click', function() {
            cardViewBtn.classList.add('active');
            tableViewBtn.classList.remove('active');
            // Implementar a lógica para mostrar a visualização em cards
            // Por enquanto, apenas mostrar um alerta
            alert('Visualização em cards será implementada em breve!');
        });
    }
    // Inicializar tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            boundary: document.body
        });
    });

    // Elementos da página
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    const dateFilter = document.getElementById('dateFilter');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    const orderRows = document.querySelectorAll('.order-row');
    const filteredCount = document.getElementById('filteredCount');
    const totalCount = document.getElementById('totalCount');

    // Função para aplicar filtros
    function applyFilters() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const statusValue = statusFilter ? statusFilter.value : 'all';
        const priorityValue = priorityFilter ? priorityFilter.value : 'all';
        const dateValue = dateFilter ? dateFilter.value : '';

        let visibleCount = 0;

        orderRows.forEach(row => {
            const title = row.querySelector('.order-title').textContent.toLowerCase();
            const description = row.querySelector('.order-description') ?
                row.querySelector('.order-description').textContent.toLowerCase() : '';
            const status = row.getAttribute('data-status');
            const priority = row.getAttribute('data-priority');
            const date = row.getAttribute('data-date');

            // Verificar se a ordem atende a todos os filtros
            const matchesSearch = searchTerm === '' ||
                title.includes(searchTerm) ||
                description.includes(searchTerm);

            const matchesStatus = statusValue === 'all' || status === statusValue;
            const matchesPriority = priorityValue === 'all' || priority === priorityValue;
            const matchesDate = dateValue === '' || date === dateValue;

            // Mostrar ou ocultar a linha com base nos filtros
            if (matchesSearch && matchesStatus && matchesPriority && matchesDate) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Atualizar contador de ordens filtradas
        if (filteredCount) {
            filteredCount.textContent = visibleCount;
        }

        // Mostrar ou ocultar o estado vazio
        const emptyState = document.querySelector('.empty-state-container');
        if (emptyState) {
            if (visibleCount === 0) {
                emptyState.style.display = 'block';
            } else {
                emptyState.style.display = 'none';
            }
        }
    }

    // Adicionar event listeners para os filtros
    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    if (priorityFilter) {
        priorityFilter.addEventListener('change', applyFilters);
    }

    if (dateFilter) {
        dateFilter.addEventListener('change', applyFilters);
    }

    // Limpar filtros
    document.addEventListener('click', function(e) {
        if (e.target.closest('#clearFiltersBtn')) {
            console.log('Limpando filtros...');
            if (searchInput) searchInput.value = '';
            if (statusFilter) statusFilter.value = 'all';
            if (priorityFilter) priorityFilter.value = 'all';
            if (dateFilter) dateFilter.value = '';
            applyFilters();
        }
    });

    // Botão de atualizar - implementado via delegação de eventos abaixo

    // Garantir que os botões funcionem corretamente
    document.addEventListener('click', function(e) {
        // Botão de atualizar
        if (e.target.closest('#refreshBtn')) {
            console.log('Atualizando página...');
            const button = e.target.closest('#refreshBtn');
            const icon = button.querySelector('i');
            icon.classList.add('fa-spin');

            // Recarregar a página após um pequeno delay para mostrar a animação
            setTimeout(() => {
                window.location.reload();
            }, 500);

            // Prevenir comportamento padrão do link
            e.preventDefault();
        }

        // Botão de nova ordem - implementado diretamente no HTML

        // Botões de visualização
        if (e.target.closest('.btn-view')) {
            const button = e.target.closest('.btn-view');
            const orderId = button.getAttribute('data-id') || button.closest('tr').getAttribute('data-id');
            console.log('Visualizando ordem:', orderId);
            // O redirecionamento acontece automaticamente pelo link
        }

        // Botões de edição
        if (e.target.closest('.btn-edit')) {
            const button = e.target.closest('.btn-edit');
            const orderId = button.getAttribute('data-id') || button.closest('tr').getAttribute('data-id');
            console.log('Editando ordem:', orderId);
            // O redirecionamento acontece automaticamente pelo link
        }

        // Botões de exclusão
        if (e.target.closest('.btn-delete')) {
            const button = e.target.closest('.btn-delete');
            const orderId = button.getAttribute('data-id') || button.closest('tr').getAttribute('data-id');
            console.log('Excluindo ordem:', orderId);
            // O redirecionamento acontece automaticamente pelo link
        }
    });

    // Botões de exclusão
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.getAttribute('data-id');
            const orderTitle = this.closest('tr').querySelector('.order-title').textContent;

            // Criar e mostrar modal de confirmação
            const modalHtml = `
                <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content bg-dark text-light">
                            <div class="modal-header border-bottom border-secondary">
                                <h5 class="modal-title text-warning">Confirmar Exclusão</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Tem certeza que deseja excluir a ordem <strong>#${orderId}</strong>?</p>
                                <p class="text-muted">${orderTitle}</p>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Esta ação não pode ser desfeita.
                                </div>
                            </div>
                            <div class="modal-footer border-top border-secondary">
                                <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">Cancelar</button>
                                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                                    <i class="fas fa-trash me-2"></i>Excluir
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Adicionar modal ao DOM
            const modalContainer = document.createElement('div');
            modalContainer.innerHTML = modalHtml;
            document.body.appendChild(modalContainer);

            // Inicializar e mostrar o modal
            const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
            modal.show();

            // Configurar botão de confirmação
            document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
                // Aqui seria feita a requisição para excluir a ordem
                console.log(`Excluindo ordem #${orderId}`);

                // Fechar o modal
                modal.hide();

                // Remover a linha da tabela com animação
                const row = button.closest('tr');
                row.style.transition = 'all 0.5s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(20px)';

                setTimeout(() => {
                    row.remove();

                    // Atualizar contadores
                    if (totalCount) {
                        totalCount.textContent = parseInt(totalCount.textContent) - 1;
                    }

                    // Mostrar notificação de sucesso
                    showNotification('Ordem excluída com sucesso!', 'success');

                    // Remover o modal do DOM
                    modalContainer.remove();
                }, 500);
            });

            // Remover o modal do DOM quando for fechado
            document.getElementById('deleteConfirmModal').addEventListener('hidden.bs.modal', function() {
                modalContainer.remove();
            });
        });
    });

    // Função para mostrar notificações
    function showNotification(message, type = 'info') {
        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            </div>
            <div class="notification-content">
                <p>${message}</p>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Adicionar ao DOM
        document.body.appendChild(notification);

        // Mostrar com animação
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Configurar botão de fechar
        notification.querySelector('.notification-close').addEventListener('click', function() {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Auto-fechar após 5 segundos
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }

    // Inicializar animações
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach(element => {
        element.style.opacity = '0';
        setTimeout(() => {
            element.style.opacity = '1';
        }, 100);
    });
});
