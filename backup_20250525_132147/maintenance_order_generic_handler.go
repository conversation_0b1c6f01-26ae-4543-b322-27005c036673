package handlers

import (
	"errors"
	"net/http"
	"strconv"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// MaintenanceOrderGenericHandler implementa um handler para ordens de manutenção
// usando o serviço genérico
type MaintenanceOrderGenericHandler struct {
	service *services.MaintenanceOrderGenericService
}

// NewMaintenanceOrderGenericHandler cria um novo handler para ordens de manutenção
func NewMaintenanceOrderGenericHandler(
	service *services.MaintenanceOrderGenericService,
) *MaintenanceOrderGenericHandler {
	return &MaintenanceOrderGenericHandler{
		service: service,
	}
}

// GetOrder retorna uma ordem pelo ID
// GET /api/orders/:id
func (h *MaintenanceOrderGenericHandler) GetOrder(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")

	// Buscar ordem pelo ID
	order, err := h.service.GetOrderByID(c.Request.Context(), idStr)
	if err != nil {
		if errors.Is(err, services.ErrNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Ordem não encontrada"})
			return
		}
		if errors.Is(err, services.ErrInvalidInput) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// CreateOrder cria uma nova ordem
// POST /api/orders
func (h *MaintenanceOrderGenericHandler) CreateOrder(c *gin.Context) {
	// Decodificar corpo da requisição
	var order models.MaintenanceOrder
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Criar ordem
	if err := h.service.CreateOrder(c.Request.Context(), &order); err != nil {
		if errors.Is(err, services.ErrInvalidInput) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// UpdateOrder atualiza uma ordem existente
// PUT /api/orders/:id
func (h *MaintenanceOrderGenericHandler) UpdateOrder(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")

	// Decodificar corpo da requisição
	var order models.MaintenanceOrder
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Atualizar ordem
	if err := h.service.UpdateOrder(c.Request.Context(), idStr, &order); err != nil {
		if errors.Is(err, services.ErrNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Ordem não encontrada"})
			return
		}
		if errors.Is(err, services.ErrInvalidInput) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// DeleteOrder remove uma ordem
// DELETE /api/orders/:id
func (h *MaintenanceOrderGenericHandler) DeleteOrder(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")

	// Remover ordem
	if err := h.service.DeleteOrder(c.Request.Context(), idStr); err != nil {
		if errors.Is(err, services.ErrNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Ordem não encontrada"})
			return
		}
		if errors.Is(err, services.ErrInvalidInput) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Ordem removida com sucesso"})
}

// ListOrders retorna uma lista de ordens com paginação
// GET /api/orders
func (h *MaintenanceOrderGenericHandler) ListOrders(c *gin.Context) {
	// Obter parâmetros de paginação
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	// Converter para inteiros
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Buscar ordens
	orders, total, err := h.service.ListOrders(c.Request.Context(), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"pagination": gin.H{
			"page":      page,
			"page_size": pageSize,
			"total":     total,
			"pages":     (total + pageSize - 1) / pageSize,
		},
	})
}

// ShowOrderDetail renderiza a página de detalhes da ordem
// GET /orders/:id
func (h *MaintenanceOrderGenericHandler) ShowOrderDetail(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")

	// Buscar ordem pelo ID
	order, err := h.service.GetOrderByID(c.Request.Context(), idStr)
	if err != nil {
		if errors.Is(err, services.ErrNotFound) {
			c.HTML(http.StatusNotFound, "error.html", gin.H{
				"error": "Ordem não encontrada",
			})
			return
		}
		if errors.Is(err, services.ErrInvalidInput) {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "ID inválido",
			})
			return
		}
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Renderizar página de detalhes
	c.HTML(http.StatusOK, "ordens/order_detail_service.html", gin.H{
		"title":      "Detalhes da Ordem #" + idStr,
		"page":       "order_detail",
		"ActivePage": "orders",
		"Order":      order,
	})
}
