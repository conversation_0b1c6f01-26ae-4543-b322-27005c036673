/**
 * Ordens de Serviço - Estilo Galeria - Rede Tradição
 * Script para gerenciar a página de ordens de serviço no estilo galeria
 */

// Variáveis globais
let orders = [];
let filteredOrders = [];

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips do Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Carregar ordens (inicialmente todas)
    loadOrders();

    // Event listeners
    setupEventListeners();
});

/**
 * Configura os event listeners da página
 */
function setupEventListeners() {
    // Alternar entre visualização em grade e lista
    document.getElementById('gridViewBtn').addEventListener('click', function() {
        document.getElementById('gridView').classList.remove('d-none');
        document.getElementById('listView').classList.add('d-none');
        this.classList.add('active');
        document.getElementById('listViewBtn').classList.remove('active');
    });

    document.getElementById('listViewBtn').addEventListener('click', function() {
        document.getElementById('listView').classList.remove('d-none');
        document.getElementById('gridView').classList.add('d-none');
        this.classList.add('active');
        document.getElementById('gridViewBtn').classList.remove('active');
    });

    // Filtro de status
    document.getElementById('statusFilter').addEventListener('change', function() {
        applyFilters();
    });

    // Filtro de prioridade
    document.getElementById('priorityFilter').addEventListener('change', function() {
        applyFilters();
    });

    // Filtro de data
    document.getElementById('dateFilter').addEventListener('change', function() {
        applyFilters();
    });

    // Busca
    document.getElementById('searchBtn').addEventListener('click', function() {
        applyFilters();
    });

    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });

    // Botão de limpar filtros
    document.getElementById('clearFiltersBtn').addEventListener('click', function() {
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('priorityFilter').value = 'all';
        document.getElementById('searchInput').value = '';
        document.getElementById('dateFilter').value = '';
        applyFilters();
    });

    // Botão de atualizar
    document.getElementById('refreshBtn').addEventListener('click', function() {
        loadOrders();
    });

    // Botões de excluir ordem
    document.querySelectorAll('.delete-order').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            confirmDeleteOrder(orderId);
        });
    });
}

/**
 * Carrega a lista de ordens
 */
function loadOrders() {
    // Exibir indicador de carregamento (se existir)
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.classList.remove('d-none');
    }

    // Ocultar mensagem de "nenhuma ordem"
    const noOrdersMessage = document.getElementById('noOrdersMessage');
    if (noOrdersMessage) {
        noOrdersMessage.classList.add('d-none');
    }

    console.log('Iniciando carregamento de ordens...');

    // Fazer requisição para a API com tratamento de erros melhorado
    fetch('/api/orders')
        .then(response => {
            console.log('Resposta recebida:', response.status);
            if (!response.ok) {
                throw new Error(`Falha ao carregar ordens: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Dados recebidos:', data);

            // Verificar se os dados são válidos e contêm a propriedade orders
            if (!data || !data.orders || !Array.isArray(data.orders)) {
                console.error('Dados recebidos não contêm array de ordens:', data);
                throw new Error('Formato de dados inválido');
            }

            // Filtrar a ordem #18 que é inválida/hardcoded
            orders = data.orders.filter(order => {
                if (order.id === 18 || order.id === "18") {
                    console.log('Ordem #18 filtrada da lista de ordens');
                    return false;
                }
                return true;
            });
            filteredOrders = [...orders];

            // Ocultar indicador de carregamento
            if (loadingIndicator) {
                loadingIndicator.classList.add('d-none');
            }

            if (data.orders.length === 0) {
                // Exibir mensagem de "nenhuma ordem"
                if (noOrdersMessage) {
                    noOrdersMessage.classList.remove('d-none');
                }
            } else {
                // Renderizar as ordens
                renderOrders(data.orders);
                // Atualizar contadores
                updateCounters(data.orders);
                // Atualizar métricas se disponíveis
                if (data.metrics) {
                    updateMetrics(data.metrics);
                }
            }
        })
        .catch(error => {
            console.error('Erro ao carregar ordens:', error);

            // Ocultar indicador de carregamento
            if (loadingIndicator) {
                loadingIndicator.classList.add('d-none');
            }

            // Exibir mensagem de erro
            if (noOrdersMessage) {
                noOrdersMessage.classList.remove('d-none');
                noOrdersMessage.querySelector('p').textContent = 'Erro ao carregar ordens: ' + error.message;
            }

            // Mostrar toast de erro
            showToast('Erro ao carregar ordens: ' + error.message, 'error');
        });
}

/**
 * Renderiza as ordens na interface
 * @param {Array} ordersList - Lista de ordens
 */
function renderOrders(ordersList) {
    console.log('Renderizando ordens:', ordersList);

    const gridView = document.getElementById('gridView');
    const tableBody = document.getElementById('orderTableBody');

    // Se não houver elementos para renderizar, não fazer nada
    if (!gridView || !tableBody) {
        console.error('Elementos de visualização não encontrados');
        return;
    }

    // Limpar a grade de ordens (mantendo elementos especiais como mensagens)
    const specialElements = Array.from(gridView.querySelectorAll('#loadingIndicator, #noOrdersMessage'));
    gridView.innerHTML = '';
    specialElements.forEach(el => gridView.appendChild(el));

    // Limpar a tabela
    tableBody.innerHTML = '';

    if (!ordersList || ordersList.length === 0) {
        console.log('Nenhuma ordem para renderizar');

        // Exibir mensagem de "nenhuma ordem"
        const noOrdersMessage = document.getElementById('noOrdersMessage');
        if (noOrdersMessage) {
            noOrdersMessage.classList.remove('d-none');
        } else {
            // Criar mensagem se não existir
            const emptyMessage = document.createElement('div');
            emptyMessage.id = 'noOrdersMessage';
            emptyMessage.className = 'text-center py-5';
            emptyMessage.style.gridColumn = '1 / -1';
            emptyMessage.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-clipboard-list empty-state-icon"></i>
                    <p>Nenhuma ordem de serviço encontrada com os filtros selecionados.</p>
                    <a href="/orders/create" class="btn btn-shell-yellow mt-3">
                        <i class="fas fa-plus"></i> Nova Ordem
                    </a>
                </div>
            `;
            gridView.appendChild(emptyMessage);
        }

        // Adicionar linha vazia na tabela
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-clipboard-list empty-state-icon"></i>
                        <p>Nenhuma ordem de serviço encontrada com os filtros selecionados.</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Ocultar mensagem de "nenhuma ordem"
    const noOrdersMessage = document.getElementById('noOrdersMessage');
    if (noOrdersMessage) {
        noOrdersMessage.classList.add('d-none');
    }

    // Renderizar cada ordem com verificação de dados
    ordersList.forEach(order => {
        try {
            // Verificar se os campos necessários existem
            if (!order || !order.id) {
                console.error('Ordem inválida:', order);
                return;
            }

            // Verificar se não é a ordem #18
            if (order.id === 18 || order.id === "18") {
                console.log('Ordem #18 filtrada da renderização');
                return;
            }

            // Determinar classes de status e prioridade
            const statusClass = `status-${order.status || 'pending'}`;
            const priorityClass = `priority-${order.priority || 'medium'}`;

            // Obter labels de status e prioridade
            const statusLabel = getStatusLabel(order.status);
            const priorityLabel = getPriorityLabel(order.priority);

            // Formatar data
            const formattedDate = formatDate(order.created_at);

            // Adicionar à visualização em grade
            const orderItem = document.createElement('div');
            orderItem.className = 'order-item';
            orderItem.innerHTML = `
                <div class="order-card">
                    <div class="card-header">
                        <div class="order-id">#${order.id}</div>
                        <div class="order-date">${formattedDate}</div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title" title="${order.title || ''}">${order.title || 'Sem título'}</h5>
                        <p class="card-text description">${order.description || 'Sem descrição'}</p>
                        <div class="order-meta">
                            <span class="status-badge ${statusClass}">
                                <i class="fas fa-circle"></i> ${statusLabel}
                            </span>
                            <span class="priority-badge ${priorityClass}">
                                <i class="fas fa-flag"></i> ${priorityLabel}
                            </span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="card-actions">
                            <a href="/orders/${order.id}" class="btn-icon" data-bs-toggle="tooltip" title="Ver Detalhes">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="/orders/${order.id}/edit" class="btn-icon" data-bs-toggle="tooltip" title="Editar">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn-icon delete-order" data-id="${order.id}" data-bs-toggle="tooltip" title="Excluir">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            gridView.appendChild(orderItem);

            // Adicionar à visualização em lista
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>#${order.id}</td>
                <td>${order.title || 'Sem título'}</td>
                <td>${order.description || 'Sem descrição'}</td>
                <td>
                    <span class="status-badge ${statusClass}">
                        <i class="fas fa-circle"></i> ${statusLabel}
                    </span>
                </td>
                <td>
                    <span class="priority-badge ${priorityClass}">
                        <i class="fas fa-flag"></i> ${priorityLabel}
                    </span>
                </td>
                <td>${formattedDate}</td>
                <td>
                    <div class="action-buttons">
                        <a href="/orders/${order.id}" class="btn-icon" data-bs-toggle="tooltip" title="Ver Detalhes">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="/orders/${order.id}/edit" class="btn-icon" data-bs-toggle="tooltip" title="Editar">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn-icon delete-order" data-id="${order.id}" data-bs-toggle="tooltip" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        } catch (error) {
            console.error('Erro ao renderizar ordem:', error, order);
        }
    });

    // Adicionar event listeners para os botões de excluir
    document.querySelectorAll('.delete-order').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            confirmDeleteOrder(orderId);
        });
    });

    // Reinicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Atualiza os contadores de ordens
 * @param {Array} ordersList - Lista de ordens
 */
function updateCounters(ordersList) {
    // Contar ordens por status
    const pendingCount = ordersList.filter(order => order.status === 'pending').length;
    const inProgressCount = ordersList.filter(order => order.status === 'in_progress').length;
    const completedCount = ordersList.filter(order => order.status === 'completed').length;
    const totalCount = ordersList.length;

    // Atualizar os contadores na interface
    document.getElementById('pendingCount').textContent = pendingCount;
    document.getElementById('inProgressCount').textContent = inProgressCount;
    document.getElementById('completedCount').textContent = completedCount;
    document.getElementById('totalCount').textContent = totalCount;
}

/**
 * Aplica os filtros selecionados às ordens
 */
function applyFilters() {
    // Obter valores dos filtros
    const statusFilter = document.getElementById('statusFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;

    // Filtrar ordens
    let filtered = [...orders];

    // Filtrar por status
    if (statusFilter !== 'all') {
        filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Filtrar por prioridade
    if (priorityFilter !== 'all') {
        filtered = filtered.filter(order => order.priority === priorityFilter);
    }

    // Filtrar por data
    if (dateFilter) {
        const filterDate = new Date(dateFilter);
        filtered = filtered.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.toDateString() === filterDate.toDateString();
        });
    }

    // Filtrar por termo de busca
    if (searchTerm) {
        filtered = filtered.filter(order =>
            (order.title && order.title.toLowerCase().includes(searchTerm)) ||
            (order.description && order.description.toLowerCase().includes(searchTerm)) ||
            (order.id && order.id.toString().includes(searchTerm))
        );
    }

    // Atualizar a lista filtrada
    filteredOrders = filtered;

    // Renderizar as ordens filtradas
    renderOrders(filtered);
    // Atualizar contadores
    updateCounters(filtered);
}

/**
 * Confirma a exclusão de uma ordem
 * @param {number} orderId - ID da ordem
 */
function confirmDeleteOrder(orderId) {
    // Verificar se não é a ordem #18
    if (orderId === 18 || orderId === "18") {
        console.error('Tentativa de excluir a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível para exclusão.', 'warning');
        return;
    }

    if (confirm(`Tem certeza que deseja excluir a ordem #${orderId}?`)) {
        deleteOrder(orderId);
    }
}

/**
 * Exclui uma ordem
 * @param {number} orderId - ID da ordem
 */
function deleteOrder(orderId) {
    // Verificar se não é a ordem #18
    if (orderId === 18 || orderId === "18") {
        console.error('Tentativa de excluir a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível para exclusão.', 'warning');
        return;
    }

    fetch(`/api/maintenance-orders/${orderId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Falha ao excluir ordem');
        }
        return response.json();
    })
    .then(response => {
        console.log('Resposta da exclusão:', response);
        showToast('Ordem excluída com sucesso!', 'success');
        // Remover a ordem da lista
        orders = orders.filter(order => order.id !== parseInt(orderId));
        filteredOrders = filteredOrders.filter(order => order.id !== parseInt(orderId));
        // Renderizar as ordens atualizadas
        renderOrders(filteredOrders);
        // Atualizar contadores
        updateCounters(filteredOrders);
    })
    .catch(error => {
        console.error('Erro ao excluir ordem:', error);
        showToast('Erro ao excluir ordem: ' + error.message, 'error');
    });
}

/**
 * Exibe uma notificação toast
 * @param {string} message - Mensagem a ser exibida
 * @param {string} type - Tipo de notificação (success, error)
 */
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastPlacement');

    // Criar o elemento toast
    const toastElement = document.createElement('div');
    toastElement.className = `toast toast-shell ${type} show`;
    toastElement.setAttribute('role', 'alert');
    toastElement.setAttribute('aria-live', 'assertive');
    toastElement.setAttribute('aria-atomic', 'true');

    // Ícone baseado no tipo
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';

    // Conteúdo do toast
    toastElement.innerHTML = `
        <div class="toast-header">
            <i class="fas fa-${icon} me-2"></i>
            <strong class="me-auto">${type === 'success' ? 'Sucesso' : type === 'error' ? 'Erro' : 'Informação'}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Fechar"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    // Adicionar ao container
    toastContainer.appendChild(toastElement);

    // Configurar para fechar automaticamente após 5 segundos
    setTimeout(() => {
        toastElement.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toastElement);
        }, 300);
    }, 5000);

    // Adicionar evento para fechar ao clicar no botão
    toastElement.querySelector('.btn-close').addEventListener('click', () => {
        toastElement.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toastElement);
        }, 300);
    });
}

/**
 * Formata uma data para exibição
 * @param {string} dateString - String de data
 * @returns {string} Data formatada
 */
function formatDate(dateString) {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Data inválida';

    return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

/**
 * Obtém o label de um status
 * @param {string} status - Código do status
 * @returns {string} Label do status
 */
function getStatusLabel(status) {
    const statusMap = {
        'pending': 'Pendente',
        'in_progress': 'Em Andamento',
        'completed': 'Concluída',
        'cancelled': 'Cancelada'
    };

    return statusMap[status] || status;
}

/**
 * Obtém o label de uma prioridade
 * @param {string} priority - Código da prioridade
 * @returns {string} Label da prioridade
 */
function getPriorityLabel(priority) {
    const priorityMap = {
        'low': 'Baixa',
        'medium': 'Média',
        'high': 'Alta',
        'critical': 'Crítica'
    };

    return priorityMap[priority] || priority;
}

/**
 * Atualiza as métricas na interface
 * @param {Object} metrics - Objeto contendo as métricas
 */
function updateMetrics(metrics) {
    // Atualizar contadores de status
    const pendingCount = document.getElementById('pendingCount');
    const inProgressCount = document.getElementById('inProgressCount');
    const completedCount = document.getElementById('completedCount');
    const totalCount = document.getElementById('totalCount');

    if (pendingCount) pendingCount.textContent = metrics.pending || 0;
    if (inProgressCount) inProgressCount.textContent = metrics.in_progress || 0;
    if (completedCount) completedCount.textContent = metrics.completed || 0;
    if (totalCount) totalCount.textContent = metrics.total || 0;
}
