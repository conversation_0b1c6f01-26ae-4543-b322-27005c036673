// Função para obter o ID da filial de forma robusta
function getBranchId() {
    // Primeiro, tenta obter do input hidden
    const branchIdInput = document.getElementById('branch_id_input');
    if (branchIdInput && branchIdInput.value) {
        console.log('[BRANCH_ID] Obtido do input hidden:', branchIdInput.value);
        return branchIdInput.value;
    }

    // Tenta obter do localStorage
    const storedBranchId = localStorage.getItem('branch_id');
    if (storedBranchId) {
        console.log('[BRANCH_ID] Obtido do localStorage:', storedBranchId);
        return storedBranchId;
    }

    // Tenta obter dos dados do usuário
    try {
        const userData = localStorage.getItem('user');
        if (userData) {
            const user = JSON.parse(userData);
            if (user && user.branch_id) {
                const branchId = user.branch_id.toString();
                console.log('[BRANCH_ID] Obtido dos dados do usuário:', branchId);
                return branchId;
            }
        }
    } catch (e) {
        console.error('Erro ao obter branch_id dos dados do usuário:', e);
    }

    // Tenta obter da URL
    const urlParams = new URLSearchParams(window.location.search);
    const urlBranchId = urlParams.get('branch_id');
    if (urlBranchId) {
        console.log('[BRANCH_ID] Obtido da URL:', urlBranchId);
        return urlBranchId;
    }

    // Verificação específica para filiais 54 e 55
    if (window.location.href.includes('filial54') ||
        document.cookie.includes('branch_id=54') ||
        localStorage.getItem('user_email') === '<EMAIL>' ||
        localStorage.getItem('user_email') === '<EMAIL>') {
        console.log('[BRANCH_ID] Usando ID de filial específica: 54');
        return '54';
    }

    // Valor padrão
    console.warn('[BRANCH_ID] Usando ID de filial padrão: 54');
    return '54';
}

// Funções para carregar dados
async function loadEquipments() {
    try {
        const branchId = getBranchId();
        if (!branchId) {
            showToast('Não foi possível obter o ID da filial', 'error');
            return;
        }

        const response = await fetch(`/api/equipments?branch_id=${branchId}`);
        const data = await response.json();
        const equipmentSelect = document.getElementById('equipment');
        if (equipmentSelect) {
            equipmentSelect.innerHTML = '<option value="">Selecione um equipamento</option>';
            data.forEach(equipment => {
                const option = document.createElement('option');
                option.value = equipment.id;
                option.textContent = equipment.name;
                equipmentSelect.appendChild(option);
            });
        }
    } catch (error) {
        showToast('Erro ao carregar equipamentos', 'error');
        console.error('Erro ao carregar equipamentos:', error);
    }
}

async function loadServiceProviders() {
    try {
        const branchId = getBranchId();
        if (!branchId) {
            showToast('Não foi possível obter o ID da filial', 'error');
            return;
        }

        const response = await fetch(`/api/providers?branch_id=${branchId}`);
        const data = await response.json();
        const providerSelect = document.getElementById('providerId');
        if (providerSelect) {
            providerSelect.innerHTML = '<option value="">Selecione um prestador</option>';
            data.forEach(provider => {
                const option = document.createElement('option');
                option.value = provider.id;
                option.textContent = provider.name;
                providerSelect.appendChild(option);
            });
        }
    } catch (error) {
        showToast('Erro ao carregar prestadores', 'error');
        console.error('Erro ao carregar prestadores:', error);
    }
}

async function loadTechnicians(providerId) {
    try {
        const branchId = getBranchId();
        if (!branchId) {
            showToast('Não foi possível obter o ID da filial', 'error');
            return;
        }

        const response = await fetch(`/api/providers/${providerId}/technicians?branch_id=${branchId}`);
        const data = await response.json();
        const technicianSelect = document.getElementById('technicianId');
        if (technicianSelect) {
            technicianSelect.innerHTML = '<option value="">Selecione um técnico</option>';
            data.forEach(technician => {
                const option = document.createElement('option');
                option.value = technician.id;
                option.textContent = technician.name;
                technicianSelect.appendChild(option);
            });
        }
    } catch (error) {
        showToast('Erro ao carregar técnicos', 'error');
        console.error('Erro ao carregar técnicos:', error);
    }
}

// Eventos
const providerSelect = document.getElementById('providerId');
if (providerSelect) {
    providerSelect.addEventListener('change', (e) => {
        const providerId = e.target.value;
        if (providerId) {
            loadTechnicians(providerId);
        }
    });
}

const refreshProviders = document.getElementById('refreshProviders');
if (refreshProviders) {
    refreshProviders.addEventListener('click', () => {
        loadServiceProviders();
    });
}

// Função para mostrar toast
function showToast(message, type = 'success', delay = 5000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type} animate__animated animate__fadeIn`;
    toast.innerHTML = `
        <div class="toast-header">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            <strong class="me-auto">${type === 'success' ? 'Sucesso' : 'Erro'}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    document.body.appendChild(toast);
    
    const toastInstance = new bootstrap.Toast(toast);
    toastInstance.show();
    
    setTimeout(() => {
        toastInstance.hide();
    }, delay);
}

// Carregar dados iniciais
document.addEventListener('DOMContentLoaded', () => {
    loadEquipments();
    loadServiceProviders();
});
