package handlers

import (
	"log"
	"net/http"
	"strconv"
	"time"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// ListOrdersPage renderiza a página de listagem de ordens de serviço
// GET /orders
func (h *OrderHandler) ListOrdersPage(c *gin.Context) {
	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")

	// Verificar se há um filtro por técnico na URL
	technicianIDStr := c.Query("technician_id")

	// Obter ordens do serviço
	allOrders, err := h.Service.GetAllOrders()
	if err != nil {
		// Log detalhado do erro
		log.Printf("Erro ao carregar ordens de serviço: %v", err)

		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar ordens de serviço: " + err.<PERSON>rror(),
		})
		return
	}

	// Log para depuração
	log.Printf("Ordens carregadas: %d", len(allOrders))

	// Filtrar ordens se necessário
	var filteredOrders []models.MaintenanceOrder

	// Se o usuário for técnico ou houver um filtro por técnico, filtrar as ordens
	if userRole == "tecnico" || technicianIDStr != "" {
		// Determinar o ID do técnico a ser usado
		var techIDStr string
		if technicianIDStr != "" {
			techIDStr = technicianIDStr
		} else if userIDStr, ok := userID.(string); ok {
			techIDStr = userIDStr
		}

		// Filtrar ordens pelo ID do técnico
		for _, order := range allOrders {
			// Verificar se há um prestador de serviço atribuído
			if order.ServiceProviderID != nil {
				// Converter para string para comparar
				orderProviderID := strconv.FormatUint(uint64(*order.ServiceProviderID), 10)
				if orderProviderID == techIDStr {
					filteredOrders = append(filteredOrders, order)
				}
			}
		}
	} else {
		// Usar todas as ordens
		filteredOrders = allOrders
	}

	// Log para depuração
	log.Printf("Ordens filtradas: %d", len(filteredOrders))

	// Contar ordens por status
	var pendingCount, inProgressCount, completedCount int
	for _, order := range filteredOrders {
		switch order.Status {
		case "pending":
			pendingCount++
		case "in_progress":
			inProgressCount++
		case "completed":
			completedCount++
		}
	}

	totalCount := len(filteredOrders)

	// Preparar opções de status e prioridade para os filtros
	statusOptions := map[string]string{
		"pending":     "Pendente",
		"in_progress": "Em Andamento",
		"completed":   "Concluída",
		"cancelled":   "Cancelada",
	}

	priorityOptions := map[string]string{
		"low":      "Baixa",
		"medium":   "Média",
		"high":     "Alta",
		"urgent":   "Urgente",
		"critical": "Crítica",
	}

	// Obter email do usuário
	userEmail, _ := c.Get("email")
	// Obter nome do usuário se estiver disponível no contexto, ou usar um valor padrão
	userName, _ := c.Get("userName")
	if userName == nil {
		userName = "Usuário"
	}

	// Renderizar a página com as ordens usando o novo template estilo galeria
	c.HTML(http.StatusOK, "ordens/orders_gallery_style.html", gin.H{
		"title":           "Ordens de Serviço - Rede Tradição",
		"page":            "orders",
		"ActivePage":      "orders",
		"Orders":          filteredOrders,
		"PendingCount":    pendingCount,
		"InProgressCount": inProgressCount,
		"CompletedCount":  completedCount,
		"TotalCount":      totalCount,
		"StatusOptions":   statusOptions,
		"PriorityOptions": priorityOptions,
		"User": gin.H{
			"ID":    userID,
			"Name":  userName,
			"Email": userEmail,
			"Role":  userRole,
		},
	})
}

// ShowCreateOrderForm renderiza o formulário de criação de ordens de serviço
// GET /orders/create
func (h *OrderHandler) ShowCreateOrderForm(c *gin.Context) {
	// Obter ID do usuário logado
	userID, exists := c.Get("userID")
	if !exists {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	// Obter nome do usuário
	userName, _ := c.Get("userName")
	if userName == nil {
		userName = "Usuário"
	}

	// Obter ID e nome da filial do usuário, tentando várias chaves possíveis
	var branchID interface{}
	var branchExists bool

	// Tentar obter o ID da filial de várias chaves possíveis
	branchID, branchExists = c.Get("branchID")
	if !branchExists || branchID == nil {
		branchID, branchExists = c.Get("filialID")
		if !branchExists || branchID == nil {
			// Tentar obter do usuário
			var userObj interface{}
			var userExists bool
			userObj, userExists = c.Get("user")
			if userExists && userObj != nil {
				// Verificar se o objeto do usuário tem um campo BranchID
				if user, ok := userObj.(map[string]interface{}); ok {
					if bID, ok := user["branch_id"]; ok {
						branchID = bID
					}
				}
			}

			// Se ainda não encontrou, usar um valor padrão
			if branchID == nil {
				branchID = uint(0)
			}
		}
	}

	// Log para debug
	log.Printf("[DEBUG] ID da filial obtido para o template: %v (tipo: %T)", branchID, branchID)

	// Obter nome da filial
	var branchName interface{}
	var nameExists bool
	branchName, nameExists = c.Get("branchName")
	if !nameExists || branchName == nil {
		branchName, nameExists = c.Get("filialName")
		if !nameExists || branchName == nil {
			branchName = "Filial"
		}
	}

	// Converter o ID da filial para string para garantir que seja passado corretamente para o template
	var branchIDStr string
	switch v := branchID.(type) {
	case uint:
		branchIDStr = strconv.FormatUint(uint64(v), 10)
	case int:
		branchIDStr = strconv.Itoa(v)
	case int64:
		branchIDStr = strconv.FormatInt(v, 10)
	case float64:
		branchIDStr = strconv.FormatUint(uint64(v), 10)
	case string:
		branchIDStr = v
	default:
		branchIDStr = "0"
	}

	// Log para debug
	log.Printf("[DEBUG] ID da filial convertido para string: %s", branchIDStr)

	// Renderizar o formulário de criação
	c.HTML(http.StatusOK, "ordens/create_order.html", gin.H{
		"title":      "Nova Ordem de Serviço - Rede Tradição",
		"page":       "orders_create",
		"ActivePage": "orders",
		"UserID":     userID,
		"UserName":   userName,
		"BranchID":   branchIDStr, // Usar a string convertida
		"User": gin.H{ // Adicionar objeto User para compatibilidade
			"ID":   branchIDStr,
			"Name": branchName,
		},
		"BranchName": branchName,
	})
}


// ShowEditOrderForm renderiza o formulário de edição de uma ordem de serviço
// GET /orders/:id/edit
func (h *OrderHandler) ShowEditOrderForm(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")

	// Converter para uint
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "ID inválido: " + err.Error(),
		})
		return
	}

	// Obter detalhes da ordem usando o ID
	order, branch, equipment, createdBy, assignedTo, provider, err := h.Service.GetOrderDetailsByID(uint(id))
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar detalhes da ordem: " + err.Error(),
		})
		return
	}

	// Verificar permissões para edição
	userID, _ := getUserIDFromContext(c)
	userRole, _ := c.Get("userRole")
	if userRole == nil {
		userRole = "user"
	}

	// Apenas administrador ou gerente pode editar
	// Não temos mais o campo CreatedByUserID para verificar se é o criador
	if userRole != "admin" && userRole != "gerente" {
		c.HTML(http.StatusForbidden, "error.html", gin.H{
			"error": "Você não tem permissão para editar esta ordem",
		})
		return
	}

	// Obter informações adicionais do usuário do contexto
	userEmail, _ := c.Get("email")
	// Obter nome do usuário se estiver disponível no contexto, ou usar um valor padrão
	userName, _ := c.Get("userName")
	if userName == nil {
		userName = "Usuário"
	}

	// Renderizar o formulário de edição
	c.HTML(http.StatusOK, "ordens/edit_order.html", gin.H{
		"title":      "Editar Ordem #" + idStr,
		"page":       "orders_edit",
		"ActivePage": "orders",
		"Order":      order,
		"Branch":     branch,
		"Equipment":  equipment,
		"CreatedBy":  createdBy,
		"AssignedTo": assignedTo,
		"Provider":   provider,
		"User": gin.H{
			"ID":    userID,
			"Name":  userName,
			"Email": userEmail,
			"Role":  userRole,
		},
	})
}

// ShowOrderCalendar renderiza a página de calendário de ordens de serviço
// GET /orders/calendar
func (h *OrderHandler) ShowOrderCalendar(c *gin.Context) {
	// Obter mês e ano da query, ou usar o mês atual
	mes, _ := strconv.Atoi(c.DefaultQuery("mes", strconv.Itoa(int(time.Now().Month()))))
	ano, _ := strconv.Atoi(c.DefaultQuery("ano", strconv.Itoa(time.Now().Year())))

	// Validar mês e ano
	if mes < 1 || mes > 12 {
		mes = int(time.Now().Month())
	}
	if ano < 2000 || ano > 2100 {
		ano = time.Now().Year()
	}

	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")
	userEmail, _ := c.Get("email")
	// Obter nome do usuário se estiver disponível no contexto, ou usar um valor padrão
	userName, _ := c.Get("userName")
	if userName == nil {
		userName = "Usuário"
	}

	// Renderizar a página de calendário
	c.HTML(http.StatusOK, "ordens/calendar.html", gin.H{
		"title":      "Calendário de Ordens de Serviço - Rede Tradição",
		"page":       "orders_calendar",
		"ActivePage": "orders",
		"Mes":        mes,
		"Ano":        ano,
		"User": gin.H{
			"ID":    userID,
			"Name":  userName,
			"Email": userEmail,
			"Role":  userRole,
		},
	})
}

