package routes

import (
	"tradicao/internal/handlers"

	"github.com/gin-gonic/gin"
)

// SetupOrderRoutes configura as rotas para ordens de serviço
func SetupOrderRoutes(router *gin.Engine, orderHandler *handlers.OrderHandler, authMiddleware gin.HandlerFunc) {
	// Grupo de rotas para ordens de serviço
	orders := router.Group("/api/orders")
	orders.Use(authMiddleware) // Middleware de autenticação

	// Rotas para listagem e criação
	orders.GET("", orderHandler.ListOrders)
	orders.POST("", orderHandler.CreateOrder)

	// Rotas para uma ordem específica
	order := orders.Group("/:id")
	{
		order.GET("", orderHandler.GetOrder)
		order.PUT("", orderHandler.UpdateOrder)
		order.PATCH("/status", orderHandler.UpdateOrderStatus)
		order.POST("/assign", orderHandler.AssignProvider)

		// Rotas para custos
		order.GET("/costs", orderHandler.GetCosts)
		order.POST("/costs", orderHandler.AddCost)

		// Rotas para interações
		order.POST("/interactions", orderHandler.AddInteraction)
	}

	// Rotas para a interface web
	webOrders := router.Group("/orders")
	webOrders.Use(authMiddleware)
	{
		webOrders.GET("/:id", orderHandler.ShowOrderDetail)
	}
}
