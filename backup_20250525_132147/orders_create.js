document.addEventListener('DOMContentLoaded', function() {
    // Inicialização de variáveis
    let uploadedPhotos = 0;
    let photoFiles = {};
    const requiredPhotos = 3;

    // Atualizar contador de fotos
    function updatePhotosCount() {
        document.getElementById('uploaded-photos-count').textContent = uploadedPhotos;
        if (uploadedPhotos >= requiredPhotos) {
            document.getElementById('uploaded-photos-count').style.color = 'green';
        } else {
            document.getElementById('uploaded-photos-count').style.color = '#dc3545';
        }
    }

    // Carregar opções de postos da filial do usuário
    function loadStations() {
        fetch('/api/branches/1/stations') // ID da filial será substituído pelo ID real do usuário
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar postos');
                }
                return response.json();
            })
            .then(stations => {
                const selectElement = document.getElementById('station_id');
                selectElement.innerHTML = '<option value="">Selecione o posto</option>';

                stations.forEach(station => {
                    const option = document.createElement('option');
                    option.value = station.id;
                    option.textContent = `${station.name} (${station.code}) - ${station.city}/${station.state}`;
                    selectElement.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Erro:', error);
                toastr.error('Não foi possível carregar a lista de postos. Tente novamente mais tarde.');
            });
    }

    // Carregar tipos de equipamentos
    function loadEquipmentTypes() {
        fetch('/api/equipment/types')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar tipos de equipamentos');
                }
                return response.json();
            })
            .then(types => {
                const selectElement = document.getElementById('equipment_type');
                selectElement.innerHTML = '<option value="">Selecione o tipo</option>';

                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.value;
                    option.textContent = type.label;
                    option.title = type.description;
                    selectElement.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Erro:', error);
                toastr.error('Não foi possível carregar os tipos de equipamentos. Tente novamente mais tarde.');
            });
    }

    // Carregar equipamentos do tipo selecionado
    function loadEquipmentsByType(type, stationId) {
        // Desabilitar a seleção de equipamento enquanto carrega
        const equipmentSelect = document.getElementById('equipment_id');
        equipmentSelect.disabled = true;
        equipmentSelect.innerHTML = '<option value="">Carregando equipamentos...</option>';

        let url = `/api/equipment/by-type/${type}?simple=true`;
        if (stationId) {
            url += `&station_id=${stationId}`;
        }

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar equipamentos');
                }
                return response.json();
            })
            .then(equipment => {
                equipmentSelect.innerHTML = '<option value="">Selecione o equipamento</option>';

                if (equipment.length === 0) {
                    equipmentSelect.innerHTML += '<option value="" disabled>Nenhum equipamento encontrado</option>';
                } else {
                    equipment.forEach(equip => {
                        const option = document.createElement('option');
                        option.value = equip.id;
                        option.textContent = `${equip.name} (${equip.model} - ${equip.brand})`;
                        if (equip.serial_number) {
                            option.textContent += ` [S/N: ${equip.serial_number}]`;
                        }
                        equipmentSelect.appendChild(option);
                    });
                }

                equipmentSelect.disabled = false;
            })
            .catch(error => {
                console.error('Erro:', error);
                equipmentSelect.innerHTML = '<option value="">Erro ao carregar equipamentos</option>';
                equipmentSelect.disabled = false;
                toastr.error('Não foi possível carregar a lista de equipamentos. Tente novamente mais tarde.');
            });
    }

    // Configurar eventos de seleção de imagens
    for (let i = 1; i <= 5; i++) {
        const preview = document.getElementById(`preview-${i}`);
        const input = document.getElementById(`photo-input-${i}`);
        const removeBtn = preview.querySelector('.remove-photo');

        preview.addEventListener('click', function() {
            input.click();
        });

        input.addEventListener('change', function(event) {
            if (event.target.files && event.target.files[0]) {
                const file = event.target.files[0];
                const reader = new FileReader();

                reader.onload = function(e) {
                    // Limpar o conteúdo atual
                    while (preview.firstChild) {
                        preview.removeChild(preview.firstChild);
                    }

                    // Adicionar a imagem
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    preview.appendChild(img);

                    // Readicionar o botão de remoção
                    preview.appendChild(removeBtn);

                    // Registrar a foto
                    if (!photoFiles[i]) {
                        uploadedPhotos++;
                        updatePhotosCount();
                    }
                    photoFiles[i] = file;
                };

                reader.readAsDataURL(file);
            }
        });

        removeBtn.addEventListener('click', function(event) {
            event.stopPropagation();

            // Limpar o input
            input.value = '';

            // Remover a imagem e restaurar o texto padrão
            while (preview.firstChild) {
                preview.removeChild(preview.firstChild);
            }

            const text = document.createElement('div');
            text.className = 'photo-upload-text';
            text.textContent = i <= 3 ? 'Clique para adicionar foto' : 'Clique para adicionar foto (opcional)';
            preview.appendChild(text);
            preview.appendChild(removeBtn);

            // Atualizar contador
            if (photoFiles[i]) {
                uploadedPhotos--;
                delete photoFiles[i];
                updatePhotosCount();
            }
        });
    }

    // Configurar seleção de prioridade
    const priorityOptions = document.querySelectorAll('.priority-option');
    priorityOptions.forEach(option => {
        option.addEventListener('click', function() {
            priorityOptions.forEach(opt => opt.classList.remove('priority-selected'));
            this.classList.add('priority-selected');
            document.getElementById('priority').value = this.dataset.priority;
        });
    });

    // Selecionar prioridade média por padrão
    document.querySelector('.priority-option[data-priority="media"]').click();

    // Evento de mudança de tipo de equipamento
    document.getElementById('equipment_type').addEventListener('change', function() {
        const type = this.value;
        const stationId = document.getElementById('station_id').value;

        if (type) {
            loadEquipmentsByType(type, stationId);
        } else {
            const equipmentSelect = document.getElementById('equipment_id');
            equipmentSelect.innerHTML = '<option value="">Selecione primeiro um tipo</option>';
            equipmentSelect.disabled = true;
        }
    });

    // Evento de mudança de posto
    document.getElementById('station_id').addEventListener('change', function() {
        const type = document.getElementById('equipment_type').value;
        const stationId = this.value;

        if (type && stationId) {
            loadEquipmentsByType(type, stationId);
        }
    });

    // Manipulação do formulário
    document.getElementById('maintenance-order-form').addEventListener('submit', function(event) {
        event.preventDefault();

        // Validar fotos
        if (uploadedPhotos < requiredPhotos) {
            toastr.error(`É necessário enviar pelo menos ${requiredPhotos} fotos.`);
            return;
        }

        // Construir FormData com os campos
        const formData = new FormData();

        // Adicionar campos de texto
        formData.append('problem', document.getElementById('title').value);
        formData.append('priority', document.getElementById('priority').value);
        formData.append('branch_id', document.getElementById('station_id').value);
        formData.append('equipment_id', document.getElementById('equipment_id').value);

        // Campos opcionais
        const scheduledDate = document.getElementById('scheduled_date').value;
        if (scheduledDate) {
            formData.append('due_date', scheduledDate);
        }

        const notes = document.getElementById('notes').value;
        if (notes) {
            formData.append('notes', notes);
        }

        // Adicionar fotos
        for (const [index, file] of Object.entries(photoFiles)) {
            formData.append(`photo_${index}`, file);
        }

        // Desativar o botão de envio para evitar cliques duplos
        const submitButton = document.getElementById('btn-create-order');
        const originalText = submitButton.textContent;
        submitButton.disabled = true;
        submitButton.textContent = 'Enviando...';

        // Enviar o formulário
        fetch('/api/orders', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => {
                    throw new Error(err.error || 'Erro ao criar ordem de manutenção');
                });
            }
            return response.json();
        })
        .then(data => {
            toastr.success('Ordem de manutenção criada com sucesso!');
            // Redirecionar para a lista de ordens após 2 segundos
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 2000);
        })
        .catch(error => {
            console.error('Erro:', error);
            toastr.error(error.message || 'Não foi possível criar a ordem de manutenção. Tente novamente mais tarde.');
            // Reativar o botão
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        });
    });

    // Botão de cancelar
    document.getElementById('btn-cancel').addEventListener('click', function() {
        if (confirm('Tem certeza que deseja cancelar? Todas as informações serão perdidas.')) {
            window.location.href = '/dashboard';
        }
    });

    // Inicializar
    loadStations();
    loadEquipmentTypes();
});