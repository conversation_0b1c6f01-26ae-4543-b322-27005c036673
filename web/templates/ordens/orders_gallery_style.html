{{ define "ordens/orders_gallery_style.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ordens de Serviço - Rede Tradição</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/sidebar-profile.css">
    <link rel="stylesheet" href="/static/css/orders_gallery.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body class="dark-theme">
    <!-- Incluindo o menu lateral -->
    {{ template "layouts/sidebar.html" . }}

    <!-- Botão flutuante Nova Ordem -->
    <a href="/orders/create" class="floating-action-btn" title="Nova Ordem">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da página -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-clipboard-list text-shell-yellow me-2"></i> Ordens de Serviço
                </h1>
                <div class="page-actions">
                    <button class="btn btn-shell-yellow" id="refreshBtn">
                        <i class="fas fa-sync-alt me-2"></i> Atualizar
                    </button>
                </div>
            </div>

            <!-- Métricas -->
            <div class="metrics-section mb-4">
                <div class="card card-shell">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-chart-bar text-shell-yellow me-2"></i>
                            <h2 class="card-title mb-0">Métricas</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-3">
                                <div class="stat-item pending">
                                    <div class="stat-label">Pendentes</div>
                                    <div class="stat-value" id="pendingCount">{{ .PendingCount }}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item in-progress">
                                    <div class="stat-label">Em Andamento</div>
                                    <div class="stat-value" id="inProgressCount">{{ .InProgressCount }}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item completed">
                                    <div class="stat-label">Concluídas</div>
                                    <div class="stat-value" id="completedCount">{{ .CompletedCount }}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item total">
                                    <div class="stat-label">Total</div>
                                    <div class="stat-value" id="totalCount">{{ .TotalCount }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtros e seleção -->
            <div class="filter-section mb-4">
                <div class="card card-shell">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-filter text-shell-yellow me-2"></i>
                            <h2 class="card-title mb-0">Filtros</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="statusFilter" class="form-label">Status</label>
                                    <select class="form-select" id="statusFilter">
                                        <option value="all">Todos os Status</option>
                                        {{ range $status, $label := .StatusOptions }}
                                            <option value="{{ $status }}">{{ $label }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="priorityFilter" class="form-label">Prioridade</label>
                                    <select class="form-select" id="priorityFilter">
                                        <option value="all">Todas as Prioridades</option>
                                        {{ range $prio, $label := .PriorityOptions }}
                                            <option value="{{ $prio }}">{{ $label }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="searchInput" class="form-label">Buscar</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="searchInput" placeholder="Título, ID, descrição...">
                                        <button class="btn btn-shell-yellow" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="dateFilter" class="form-label">Data de Criação</label>
                                    <div class="input-group">
                                        <input type="date" class="form-control" id="dateFilter">
                                        <button class="btn btn-shell-yellow" type="button" id="clearFiltersBtn">
                                            <i class="fas fa-times"></i> Limpar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Galeria de Ordens -->
            <div class="orders-section">
                <div class="card card-shell">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clipboard-list text-shell-yellow me-2"></i>
                                <h2 class="card-title mb-0">Ordens de Serviço</h2>
                            </div>
                            <div class="card-header-actions">
                                <button class="btn btn-icon active" id="gridViewBtn" title="Visualização em Grade" data-bs-toggle="tooltip">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="btn btn-icon" id="listViewBtn" title="Visualização em Lista" data-bs-toggle="tooltip">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Visualização em Grade (padrão) -->
                        <div id="gridView" class="orders-grid">
                            <!-- Preenchido via JavaScript ou Template -->
                            {{ if .Orders }}
                                {{ range .Orders }}
                                <div class="order-item">
                                    <div class="order-card">
                                        <div class="card-header">
                                            <div class="order-id">#{{ .ID }}</div>
                                            <div class="order-date">{{ formatDate .CreatedAt }}</div>
                                        </div>
                                        <div class="card-body">
                                            <h5 class="card-title" title="{{ .Problem }}">{{ .Problem }}</h5>
                                            <p class="card-text description">{{ .Problem }}</p>
                                            <div class="order-meta">
                                                <span class="status-badge status-{{ .Status }}">
                                                    <i class="fas fa-circle"></i> {{ index $.StatusOptions (printf "%s" .Status) }}
                                                </span>
                                                <span class="priority-badge priority-{{ .Priority }}">
                                                    <i class="fas fa-flag"></i> {{ index $.PriorityOptions (printf "%s" .Priority) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <div class="card-actions">
                                                <a href="/orders/{{ .ID }}" class="btn-icon" data-bs-toggle="tooltip" title="Ver Detalhes">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/orders/{{ .ID }}/edit" class="btn-icon" data-bs-toggle="tooltip" title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn-icon delete-order" data-id="{{ .ID }}" data-bs-toggle="tooltip" title="Excluir">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ end }}
                            {{ else }}
                                <div class="text-center py-5" id="noOrdersMessage" style="grid-column: 1 / -1;">
                                    <div class="empty-state">
                                        <i class="fas fa-clipboard-list empty-state-icon"></i>
                                        <p>Nenhuma ordem de serviço encontrada com os filtros selecionados.</p>
                                        <a href="/orders/create" class="btn btn-shell-yellow mt-3">
                                            <i class="fas fa-plus"></i> Nova Ordem
                                        </a>
                                    </div>
                                </div>
                            {{ end }}
                        </div>

                        <!-- Visualização em Lista (alternativa) -->
                        <div id="listView" class="orders-list d-none">
                            <div class="table-responsive">
                                <table class="table table-dark table-hover table-shell">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Título</th>
                                            <th>Descrição</th>
                                            <th>Status</th>
                                            <th>Prioridade</th>
                                            <th>Data</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="orderTableBody">
                                        {{ if .Orders }}
                                            {{ range .Orders }}
                                            <tr>
                                                <td>#{{ .ID }}</td>
                                                <td>{{ .Problem }}</td>
                                                <td>{{ .Problem }}</td>
                                                <td>
                                                    <span class="status-badge status-{{ .Status }}">
                                                        <i class="fas fa-circle"></i> {{ index $.StatusOptions (printf "%s" .Status) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="priority-badge priority-{{ .Priority }}">
                                                        <i class="fas fa-flag"></i> {{ index $.PriorityOptions (printf "%s" .Priority) }}
                                                    </span>
                                                </td>
                                                <td>{{ formatDate .CreatedAt }}</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="/orders/{{ .ID }}" class="btn-icon" data-bs-toggle="tooltip" title="Ver Detalhes">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="/orders/{{ .ID }}/edit" class="btn-icon" data-bs-toggle="tooltip" title="Editar">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button class="btn-icon delete-order" data-id="{{ .ID }}" data-bs-toggle="tooltip" title="Excluir">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {{ end }}
                                        {{ else }}
                                            <tr>
                                                <td colspan="7" class="text-center">
                                                    <div class="empty-state">
                                                        <i class="fas fa-clipboard-list empty-state-icon"></i>
                                                        <p>Nenhuma ordem de serviço encontrada com os filtros selecionados.</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        {{ end }}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Container de Toasts -->
    <div aria-live="polite" aria-atomic="true" class="position-fixed top-0 end-0 p-3" style="z-index: 1055">
        <div id="toastPlacement" class="toast-container"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/unified_unified_unified_unified_unified_orders.js"></script>

    <script>
        // Objeto User para uso no JavaScript
        const User = {
            ID: {{ .User.ID }},
            Name: "{{ .User.Name }}",
            Email: "{{ .User.Email }}",
            Role: "{{ .User.Role }}",
            BranchID: {{ if .User.BranchID }}{{ .User.BranchID }}{{ else }}0{{ end }}
        };
    </script>
</body>
</html>
{{ end }}
