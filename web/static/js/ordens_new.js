/**
 * Script para a nova página de ordens de serviço
 * Sistema Shell Tradição
 */

// Configurações
const ANIMATION_DURATION = 300; // Duração das animações em ms
const TOAST_DURATION = 5000;    // Duração dos toasts em ms

// Função para mostrar toast de notificação
function showToast(type, message) {
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();

    const toastId = 'toast-' + Date.now();
    const toastHTML = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: TOAST_DURATION });
    toast.show();

    // Auto-remove após esconder
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// Função para criar container de toast se não existir
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}

// Função para atualizar contadores de métricas
function updateMetricCounters(data) {
    if (!data) return;

    const pendingElement = document.getElementById('pendingOrdersCount');
    const inProgressElement = document.getElementById('inProgressOrdersCount');
    const completedElement = document.getElementById('completedOrdersCount');
    const totalElement = document.getElementById('totalOrdersCount');

    if (pendingElement) pendingElement.textContent = data.pendingCount || 0;
    if (inProgressElement) inProgressElement.textContent = data.inProgressCount || 0;
    if (completedElement) completedElement.textContent = data.completedCount || 0;
    if (totalElement) totalElement.textContent = data.totalCount || 0;
}

// Função para mostrar detalhes da ordem
function showOrderDetails(orderId) {
    // Verificação da ordem #18 movida para o unified_order_handler
    // A validação agora é feita no backend

    const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
    const contentElement = document.getElementById('orderDetailsContent');
    const editButton = document.getElementById('editOrderBtnModal');
    const deleteButton = document.getElementById('deleteOrderBtnModal');

    // Mostrar loading
    contentElement.innerHTML = `
        <div class="text-center p-5">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-3 text-warning">Carregando detalhes da ordem...</p>
        </div>
    `;

    modal.show();

    // Buscar detalhes da ordem via API
    fetch(`/api/orders/${orderId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erro ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            // Renderizar detalhes
            renderOrderDetails(data, contentElement);

            // Configurar botões
            if (editButton) {
                editButton.style.display = 'inline-block';
                editButton.onclick = () => window.location.href = `/orders/${orderId}/edit`;
            }

            if (deleteButton) {
                deleteButton.style.display = 'inline-block';
                deleteButton.onclick = () => showDeleteConfirmation(data);
            }
        })
        .catch(error => {
            console.error('Erro ao carregar detalhes:', error);
            contentElement.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erro ao carregar detalhes da ordem. Por favor, tente novamente.
                </div>
            `;
        });
}

// Função para renderizar detalhes da ordem
function renderOrderDetails(order, container) {
    // Formatar status e prioridade
    const statusClass = getStatusClass(order.status);
    const priorityClass = getPriorityClass(order.priority);

    // Construir HTML dos detalhes
    const detailsHTML = `
        <div class="order-detail-header mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="text-warning mb-0">${order.title}</h4>
                <span class="order-id">#${order.id}</span>
            </div>
            <div class="d-flex mt-2 gap-2">
                <span class="status-badge ${statusClass}">${formatStatus(order.status)}</span>
                <span class="priority-badge ${priorityClass}">${formatPriority(order.priority)}</span>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="detail-section">
                    <h6 class="text-warning mb-3">Informações Básicas</h6>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Filial:</span>
                        <span class="detail-value">${order.branch?.name || '-'}</span>
                    </div>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Equipamento:</span>
                        <span class="detail-value">${order.equipment?.name || '-'}</span>
                    </div>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Tipo:</span>
                        <span class="detail-value">${formatType(order.type)}</span>
                    </div>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Solicitante:</span>
                        <span class="detail-value">${order.requester?.name || '-'}</span>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="detail-section">
                    <h6 class="text-warning mb-3">Datas</h6>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Criação:</span>
                        <span class="detail-value">${formatDateTime(order.created_at)}</span>
                    </div>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Atualização:</span>
                        <span class="detail-value">${formatDateTime(order.updated_at)}</span>
                    </div>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Data Desejada:</span>
                        <span class="detail-value">${order.preferred_date ? formatDate(order.preferred_date) : '-'}</span>
                    </div>
                    <div class="detail-item d-flex justify-content-between mb-2">
                        <span class="detail-label text-muted">Conclusão:</span>
                        <span class="detail-value">${order.completed_at ? formatDateTime(order.completed_at) : '-'}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="detail-section">
                    <h6 class="text-warning mb-3">Descrição</h6>
                    <p class="mb-0">${order.description || 'Sem descrição disponível.'}</p>
                </div>
            </div>
        </div>

        ${order.attachments && order.attachments.length > 0 ? `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="detail-section">
                        <h6 class="text-warning mb-3">Anexos</h6>
                        <div class="row">
                            ${order.attachments.map(attachment => `
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="attachment-item">
                                        <img src="${attachment.url}" alt="Anexo" class="img-fluid rounded">
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}

        ${order.interactions && order.interactions.length > 0 ? `
            <div class="row">
                <div class="col-12">
                    <div class="detail-section">
                        <h6 class="text-warning mb-3">Histórico</h6>
                        <div class="timeline">
                            ${order.interactions.map(interaction => `
                                <div class="timeline-item mb-3">
                                    <div class="d-flex">
                                        <div class="timeline-icon me-3">
                                            <i class="fas fa-comment-alt"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="d-flex justify-content-between">
                                                <span class="fw-bold">${interaction.user?.name || 'Sistema'}</span>
                                                <span class="text-muted small">${formatDateTime(interaction.created_at)}</span>
                                            </div>
                                            <p class="mb-0 mt-1">${interaction.message}</p>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}
    `;

    // Atualizar conteúdo
    container.innerHTML = detailsHTML;
}

// Função para mostrar confirmação de exclusão
function showDeleteConfirmation(order) {
    // Verificação da ordem #18 movida para o unified_order_handler
    // A validação agora é feita no backend

    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const orderIdElement = document.getElementById('deleteOrderId');
    const orderTitleElement = document.getElementById('deleteOrderTitle');
    const confirmButton = document.getElementById('confirmDeleteBtn');

    // Atualizar informações da ordem
    if (orderIdElement) orderIdElement.textContent = `#${order.id}`;
    if (orderTitleElement) orderTitleElement.textContent = order.title;

    // Configurar botão de confirmação
    if (confirmButton) {
        confirmButton.onclick = () => {
            deleteOrder(order.id);
            modal.hide();
        };
    }

    // Mostrar modal
    modal.show();
}

// Função para excluir ordem
function deleteOrder(orderId) {
    // Verificação da ordem #18 movida para o unified_order_handler
    // A validação agora é feita no backend

    // Mostrar loading
    const loadingSpinner = document.getElementById('loadingSpinner');
    if (loadingSpinner) loadingSpinner.style.display = 'block';

    // Chamar API para excluir
    fetch(`/api/orders/${orderId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Erro ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // Esconder loading
        if (loadingSpinner) loadingSpinner.style.display = 'none';

        // Mostrar mensagem de sucesso
        showToast('success', 'Ordem excluída com sucesso!');

        // Recarregar página após um breve delay
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    })
    .catch(error => {
        console.error('Erro ao excluir ordem:', error);

        // Esconder loading
        if (loadingSpinner) loadingSpinner.style.display = 'none';

        // Mostrar mensagem de erro
        showToast('danger', 'Erro ao excluir ordem. Por favor, tente novamente.');
    });
}

// Funções auxiliares de formatação
function formatStatus(status) {
    const statusMap = {
        'aberta': 'Aberta',
        'confirmada': 'Confirmada',
        'em_analise': 'Em Análise',
        'em_andamento': 'Em Andamento',
        'aguardando_peca': 'Aguardando Peças',
        'concluida_tecnico': 'Conclusão Técnica',
        'validada_filial': 'Validada pela Filial',
        'aprovada_gerencia': 'Aprovada pela Gerência',
        'concluida': 'Concluída',
        'cancelada': 'Cancelada',
        'rejeitada': 'Rejeitada'
    };
    return statusMap[status] || status;
}

function getStatusClass(status) {
    const classMap = {
        'aberta': 'status-pending',
        'confirmada': 'status-pending',
        'em_analise': 'status-pending',
        'em_andamento': 'status-progress',
        'aguardando_peca': 'status-progress',
        'concluida_tecnico': 'status-progress',
        'validada_filial': 'status-progress',
        'aprovada_gerencia': 'status-progress',
        'concluida': 'status-completed',
        'cancelada': 'status-cancelled',
        'rejeitada': 'status-cancelled'
    };
    return classMap[status] || 'status-pending';
}

function formatPriority(priority) {
    const priorityMap = {
        'baixa': 'Baixa',
        'media': 'Média',
        'alta': 'Alta',
        'urgente': 'Urgente'
    };
    return priorityMap[priority] || priority;
}

function getPriorityClass(priority) {
    const classMap = {
        'baixa': 'priority-baixa',
        'media': 'priority-media',
        'alta': 'priority-alta',
        'urgente': 'priority-urgente'
    };
    return classMap[priority] || 'priority-media';
}

function formatType(type) {
    const typeMap = {
        'preventiva': 'Preventiva',
        'corretiva': 'Corretiva',
        'inspecao': 'Inspeção',
        'calibragem': 'Calibragem',
        'instalacao': 'Instalação'
    };
    return typeMap[type] || type;
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR') + ' ' +
           date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
}

// Inicialização quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Configurar eventos para botões de detalhes
    document.querySelectorAll('.view-details').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            // Verificação da ordem #18 movida para o unified_order_handler
            // A validação agora é feita no backend
            showOrderDetails(orderId);
        });
    });

    // Configurar eventos para botões de exclusão
    document.querySelectorAll('.delete-order').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            // Verificação da ordem #18 movida para o unified_order_handler
            // A validação agora é feita no backend
            // Buscar detalhes da ordem para mostrar confirmação
            fetch(`/api/orders/${orderId}`)
                .then(response => response.json())
                .then(data => showDeleteConfirmation(data))
                .catch(error => {
                    console.error('Erro ao carregar detalhes para exclusão:', error);
                    showToast('danger', 'Erro ao carregar detalhes da ordem. Por favor, tente novamente.');
                });
        });
    });

    // Configurar evento para botão de atualizar
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Mostrar loading
            const loadingSpinner = document.getElementById('loadingSpinner');
            if (loadingSpinner) loadingSpinner.style.display = 'block';

            // Recarregar a página
            window.location.reload();
        });
    }

    // Configurar evento para aplicar filtros
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            // Construir URL com filtros
            const status = document.getElementById('statusFilter').value;
            const priority = document.getElementById('priorityFilter').value;
            const location = document.getElementById('locationFilter').value;
            const date = document.getElementById('dateFilter').value;
            const search = document.getElementById('searchInput').value;

            let url = '/orders?';
            if (status && status !== 'all') url += `status=${status}&`;
            if (priority && priority !== 'all') url += `priority=${priority}&`;
            if (location && location !== 'all') url += `location=${location}&`;
            if (date) url += `date=${date}&`;
            if (search) url += `search=${encodeURIComponent(search)}&`;

            // Remover o último & se existir
            url = url.endsWith('&') ? url.slice(0, -1) : url;

            // Redirecionar para a URL com filtros
            window.location.href = url;
        });
    }

    // Configurar evento para limpar filtros
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            // Resetar todos os campos de filtro
            document.getElementById('statusFilter').value = 'all';
            document.getElementById('priorityFilter').value = 'all';
            document.getElementById('locationFilter').value = 'all';
            document.getElementById('dateFilter').value = '';
            document.getElementById('searchInput').value = '';
        });
    }
});
