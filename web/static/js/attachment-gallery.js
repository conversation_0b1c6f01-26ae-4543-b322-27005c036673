/**
 * Sistema de Anexos - Galeria
 * Gerenciamento completo de anexos com upload, visualização e organização
 */

class AttachmentGallery {
    constructor() {
        this.attachments = [];
        this.filteredAttachments = [];
        this.selectedFiles = [];
        this.uploadModal = null;
        this.currentFilters = {};
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeModal();
        this.setupDragAndDrop();
    }

    setupEventListeners() {
        // Upload zone click
        document.getElementById('uploadZone').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        // File input change
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startUpload();
        });
    }

    initializeModal() {
        this.uploadModal = new bootstrap.Modal(document.getElementById('uploadModal'));
    }

    setupDragAndDrop() {
        const uploadZone = document.getElementById('uploadZone');

        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            this.handleFileSelection(e.dataTransfer.files);
        });
    }

    handleFileSelection(files) {
        this.selectedFiles = Array.from(files);
        
        if (this.selectedFiles.length > 0) {
            document.getElementById('uploadForm').style.display = 'block';
            this.updateUploadPreview();
        }
    }

    updateUploadPreview() {
        const uploadZone = document.getElementById('uploadZone');
        const fileCount = this.selectedFiles.length;
        const totalSize = this.selectedFiles.reduce((sum, file) => sum + file.size, 0);
        
        uploadZone.innerHTML = `
            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
            <h5>${fileCount} arquivo(s) selecionado(s)</h5>
            <p class="text-muted">Tamanho total: ${this.formatFileSize(totalSize)}</p>
            <small class="text-muted">Clique para selecionar outros arquivos</small>
        `;
    }

    async loadAttachments() {
        try {
            this.showLoading(true);
            
            const params = new URLSearchParams({
                entity_type: window.attachmentConfig.entityType,
                entity_id: window.attachmentConfig.entityId,
                ...this.currentFilters
            });

            const response = await fetch(`/api/attachments?${params}`);
            const data = await response.json();

            if (response.ok) {
                this.attachments = data.data || [];
                this.filteredAttachments = [...this.attachments];
                this.renderGallery();
            } else {
                this.showError('Erro ao carregar anexos: ' + data.error);
            }
        } catch (error) {
            this.showError('Erro de conexão: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async loadStats() {
        try {
            const params = new URLSearchParams({
                entity_type: window.attachmentConfig.entityType,
                entity_id: window.attachmentConfig.entityId
            });

            const response = await fetch(`/api/attachments/stats?${params}`);
            const data = await response.json();

            if (response.ok) {
                this.updateStatsDisplay(data.data);
            }
        } catch (error) {
            console.error('Erro ao carregar estatísticas:', error);
        }
    }

    updateStatsDisplay(stats) {
        document.getElementById('totalFiles').textContent = stats.total_files || 0;
        document.getElementById('totalSize').textContent = stats.total_size_formatted || '0 B';
        document.getElementById('imageCount').textContent = stats.image_count || 0;
        document.getElementById('documentCount').textContent = stats.document_count || 0;
    }

    renderGallery() {
        const gallery = document.getElementById('attachmentGallery');
        const emptyState = document.getElementById('emptyState');

        if (this.filteredAttachments.length === 0) {
            gallery.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        
        gallery.innerHTML = this.filteredAttachments.map(attachment => 
            this.renderAttachmentCard(attachment)
        ).join('');
    }

    renderAttachmentCard(attachment) {
        const isImage = attachment.type === 'image';
        const categoryBadge = this.getCategoryBadge(attachment.category);
        const versionBadge = attachment.version > 1 ? 
            `<span class="version-badge">v${attachment.version}</span>` : '';

        return `
            <div class="col-md-4 col-lg-3">
                <div class="attachment-card">
                    ${isImage ? this.renderImageThumbnail(attachment) : this.renderFileIcon(attachment)}
                    
                    <div class="attachment-info">
                        <div class="attachment-title" title="${attachment.original_name}">
                            ${attachment.title || attachment.original_name}
                        </div>
                        
                        <div class="attachment-meta">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                ${categoryBadge}
                                ${versionBadge}
                            </div>
                            <small class="text-muted">
                                ${attachment.file_size_formatted} • ${this.formatDate(attachment.created_at)}
                            </small>
                            <br>
                            <small class="text-muted">
                                Por: ${attachment.uploaded_by_name}
                            </small>
                        </div>

                        ${attachment.description ? `
                            <div class="mt-2">
                                <small class="text-muted">${attachment.description}</small>
                            </div>
                        ` : ''}

                        ${attachment.tags && attachment.tags.length > 0 ? `
                            <div class="mt-2">
                                ${attachment.tags.map(tag => 
                                    `<span class="badge bg-light text-dark me-1">${tag}</span>`
                                ).join('')}
                            </div>
                        ` : ''}
                    </div>

                    <div class="attachment-actions">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-primary btn-sm" onclick="downloadAttachment(${attachment.id})">
                                <i class="fas fa-download"></i>
                            </button>
                            ${isImage ? `
                                <button class="btn btn-outline-info btn-sm" onclick="viewAttachment(${attachment.id})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-outline-secondary btn-sm" onclick="showAttachmentDetails(${attachment.id})">
                                <i class="fas fa-info"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteAttachment(${attachment.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderImageThumbnail(attachment) {
        const thumbnailUrl = attachment.thumbnail_url || attachment.download_url;
        const previewUrl = attachment.preview_url || attachment.download_url;
        
        return `
            <a href="${previewUrl}" data-lightbox="gallery" data-title="${attachment.title || attachment.original_name}">
                <img src="${thumbnailUrl}" alt="${attachment.original_name}" class="attachment-thumbnail">
            </a>
        `;
    }

    renderFileIcon(attachment) {
        const icon = this.getFileIcon(attachment.type, attachment.original_name);
        
        return `
            <div class="attachment-icon">
                <i class="${icon}"></i>
            </div>
        `;
    }

    getFileIcon(type, filename) {
        const ext = filename.split('.').pop().toLowerCase();
        
        const iconMap = {
            // Documentos
            'pdf': 'fas fa-file-pdf text-danger',
            'doc': 'fas fa-file-word text-primary',
            'docx': 'fas fa-file-word text-primary',
            'xls': 'fas fa-file-excel text-success',
            'xlsx': 'fas fa-file-excel text-success',
            'ppt': 'fas fa-file-powerpoint text-warning',
            'pptx': 'fas fa-file-powerpoint text-warning',
            'txt': 'fas fa-file-alt text-secondary',
            
            // Vídeos
            'mp4': 'fas fa-file-video text-info',
            'avi': 'fas fa-file-video text-info',
            'mov': 'fas fa-file-video text-info',
            
            // Áudios
            'mp3': 'fas fa-file-audio text-purple',
            'wav': 'fas fa-file-audio text-purple',
            
            // Compactados
            'zip': 'fas fa-file-archive text-dark',
            'rar': 'fas fa-file-archive text-dark',
            '7z': 'fas fa-file-archive text-dark',
        };

        return iconMap[ext] || 'fas fa-file text-secondary';
    }

    getCategoryBadge(category) {
        const badges = {
            'technical': '<span class="category-badge bg-primary text-white">Técnico</span>',
            'evidence': '<span class="category-badge bg-success text-white">Evidência</span>',
            'invoice': '<span class="category-badge bg-warning text-dark">Nota Fiscal</span>',
            'contract': '<span class="category-badge bg-info text-white">Contrato</span>',
            'certificate': '<span class="category-badge bg-secondary text-white">Certificado</span>',
            'manual': '<span class="category-badge bg-dark text-white">Manual</span>',
            'other': '<span class="category-badge bg-light text-dark">Outros</span>'
        };

        return badges[category] || badges['other'];
    }

    async startUpload() {
        if (this.selectedFiles.length === 0) {
            alert('Selecione pelo menos um arquivo');
            return;
        }

        const category = document.getElementById('uploadCategory').value;
        if (!category) {
            alert('Selecione uma categoria');
            return;
        }

        try {
            document.getElementById('uploadProgress').style.display = 'block';
            document.getElementById('uploadButton').disabled = true;

            for (let i = 0; i < this.selectedFiles.length; i++) {
                const file = this.selectedFiles[i];
                await this.uploadSingleFile(file, i + 1);
            }

            this.uploadModal.hide();
            this.resetUploadForm();
            await this.loadAttachments();
            await this.loadStats();
            
            this.showSuccess('Upload concluído com sucesso!');
        } catch (error) {
            this.showError('Erro no upload: ' + error.message);
        } finally {
            document.getElementById('uploadProgress').style.display = 'none';
            document.getElementById('uploadButton').disabled = false;
        }
    }

    async uploadSingleFile(file, index) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('entity_type', window.attachmentConfig.entityType);
        formData.append('entity_id', window.attachmentConfig.entityId);
        formData.append('category', document.getElementById('uploadCategory').value);
        formData.append('title', document.getElementById('uploadTitle').value);
        formData.append('description', document.getElementById('uploadDescription').value);
        formData.append('tags', document.getElementById('uploadTags').value);
        formData.append('is_public', document.getElementById('uploadPublic').checked);

        const response = await fetch('/api/attachments/upload', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Erro no upload');
        }

        // Atualizar progresso
        const progress = (index / this.selectedFiles.length) * 100;
        document.querySelector('.progress-bar').style.width = progress + '%';
        document.getElementById('uploadStatus').textContent = 
            `Arquivo ${index} de ${this.selectedFiles.length} enviado`;
    }

    resetUploadForm() {
        this.selectedFiles = [];
        document.getElementById('uploadForm').style.display = 'none';
        document.getElementById('uploadForm').reset();
        
        const uploadZone = document.getElementById('uploadZone');
        uploadZone.innerHTML = `
            <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
            <h5>Arraste arquivos aqui ou clique para selecionar</h5>
            <p class="text-muted">Máximo 50MB por arquivo</p>
        `;
    }

    applyFilters() {
        this.currentFilters = {
            type: document.getElementById('filterType').value,
            category: document.getElementById('filterCategory').value,
            tags: document.getElementById('filterTags').value,
            only_latest: document.getElementById('onlyLatest').checked
        };

        // Remover filtros vazios
        Object.keys(this.currentFilters).forEach(key => {
            if (!this.currentFilters[key] && this.currentFilters[key] !== true) {
                delete this.currentFilters[key];
            }
        });

        this.loadAttachments();
    }

    showLoading(show) {
        document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
    }

    showError(message) {
        // Implementar sistema de notificações
        alert('Erro: ' + message);
    }

    showSuccess(message) {
        // Implementar sistema de notificações
        alert('Sucesso: ' + message);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }
}

// Instância global
let attachmentGallery;

// Funções globais para os botões
function openUploadModal() {
    attachmentGallery.uploadModal.show();
}

function refreshGallery() {
    attachmentGallery.loadAttachments();
    attachmentGallery.loadStats();
}

function applyFilters() {
    attachmentGallery.applyFilters();
}

function startUpload() {
    attachmentGallery.startUpload();
}

function downloadAttachment(id) {
    window.open(`/api/attachments/${id}/download`, '_blank');
}

function viewAttachment(id) {
    window.open(`/api/attachments/${id}/preview`, '_blank');
}

function showAttachmentDetails(id) {
    // TODO: Implementar modal de detalhes
    alert('Detalhes do anexo ' + id);
}

function deleteAttachment(id) {
    if (confirm('Tem certeza que deseja remover este anexo?')) {
        fetch(`/api/attachments/${id}`, { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    attachmentGallery.showSuccess(data.message);
                    attachmentGallery.loadAttachments();
                    attachmentGallery.loadStats();
                } else {
                    attachmentGallery.showError(data.error);
                }
            })
            .catch(error => {
                attachmentGallery.showError('Erro ao deletar: ' + error.message);
            });
    }
}

// Inicializar quando DOM estiver pronto
function loadAttachments() {
    if (!attachmentGallery) {
        attachmentGallery = new AttachmentGallery();
    }
    attachmentGallery.loadAttachments();
}

function loadStats() {
    if (!attachmentGallery) {
        attachmentGallery = new AttachmentGallery();
    }
    attachmentGallery.loadStats();
}
