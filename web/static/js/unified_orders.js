/**
 * <PERSON><PERSON><PERSON>lo JavaScript Unificado para Ordens de Manutenção
 * Sistema Shell Tradição
 * 
 * Este arquivo substitui e consolida:
 * - orders.js
 * - orders_gallery.js
 * - dashboard_orders.js
 * - Ordermtecnico.js
 * - calendar-flip.js
 */

// Configuração da API unificada
const UnifiedOrdersAPI = {
    baseURL: '',
    endpoints: {
        orders: '/api/orders',
        calendar: '/api/orders/calendar',
        technician: '/api/orders/technician',
        metrics: '/api/orders/metrics'
    },
    
    // Headers padrão para todas as requisições
    getHeaders() {
        const authToken = localStorage.getItem('auth_token');
        return {
            'Content-Type': 'application/json',
            'Authorization': authToken ? `Bearer ${authToken}` : ''
        };
    }
};

// Cache unificado
const UnifiedCache = {
    data: new Map(),
    lastUpdate: new Date(),
    ttl: 5 * 60 * 1000, // 5 minutos
    
    get(key) {
        const now = new Date();
        if (this.data.has(key) && (now - this.lastUpdate) < this.ttl) {
            return this.data.get(key);
        }
        return null;
    },
    
    set(key, value) {
        this.data.set(key, value);
        this.lastUpdate = new Date();
    },
    
    clear() {
        this.data.clear();
        this.lastUpdate = new Date();
    }
};

// Classe principal para gerenciamento de ordens
class UnifiedOrderManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.filters = {};
        this.isLoading = false;
    }

    // Método principal para listar ordens
    async listOrders(page = 1, filters = {}, forceUpdate = false) {
        try {
            this.isLoading = true;
            this.showLoading();

            const cacheKey = `orders-${page}-${JSON.stringify(filters)}`;
            
            // Verificar cache
            if (!forceUpdate) {
                const cached = UnifiedCache.get(cacheKey);
                if (cached) {
                    this.renderOrders(cached);
                    return cached;
                }
            }

            // Construir parâmetros da query
            const params = new URLSearchParams({
                page: page,
                page_size: this.pageSize,
                ...filters
            });

            // Fazer requisição
            const response = await fetch(`${UnifiedOrdersAPI.endpoints.orders}?${params}`, {
                method: 'GET',
                headers: UnifiedOrdersAPI.getHeaders()
            });

            if (!response.ok) {
                throw new Error(`Erro ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Erro ao carregar ordens');
            }

            // Atualizar cache
            UnifiedCache.set(cacheKey, data);

            // Renderizar dados
            this.renderOrders(data);
            this.updatePagination(data.meta);
            
            return data;

        } catch (error) {
            console.error('Erro ao carregar ordens:', error);
            this.showError('Erro ao carregar ordens: ' + error.message);
            throw error;
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    // Método para obter uma ordem específica
    async getOrder(id) {
        try {
            // Verificar se é a ordem problemática #18
            if (id === 18 || id === "18") {
                throw new Error('Ordem #18 não está disponível');
            }

            const cacheKey = `order-${id}`;
            
            // Verificar cache
            const cached = UnifiedCache.get(cacheKey);
            if (cached) {
                return cached;
            }

            const response = await fetch(`${UnifiedOrdersAPI.endpoints.orders}/${id}`, {
                method: 'GET',
                headers: UnifiedOrdersAPI.getHeaders()
            });

            if (!response.ok) {
                throw new Error(`Erro ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Ordem não encontrada');
            }

            // Atualizar cache
            UnifiedCache.set(cacheKey, data);
            
            return data;

        } catch (error) {
            console.error(`Erro ao obter ordem ${id}:`, error);
            throw error;
        }
    }

    // Método para ordens do calendário
    async getCalendarOrders(month, year, filters = {}) {
        try {
            const params = new URLSearchParams({
                month: month,
                year: year,
                ...filters
            });

            const response = await fetch(`${UnifiedOrdersAPI.endpoints.calendar}?${params}`, {
                method: 'GET',
                headers: UnifiedOrdersAPI.getHeaders()
            });

            if (!response.ok) {
                throw new Error(`Erro ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Erro ao carregar ordens do calendário');
            }

            return data;

        } catch (error) {
            console.error('Erro ao carregar ordens do calendário:', error);
            throw error;
        }
    }

    // Método para ordens do técnico
    async getTechnicianOrders(date = null, filters = {}) {
        try {
            const params = new URLSearchParams(filters);
            
            if (date) {
                params.append('date', date);
            }

            const response = await fetch(`${UnifiedOrdersAPI.endpoints.technician}?${params}`, {
                method: 'GET',
                headers: UnifiedOrdersAPI.getHeaders()
            });

            if (!response.ok) {
                throw new Error(`Erro ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Erro ao carregar ordens do técnico');
            }

            return data;

        } catch (error) {
            console.error('Erro ao carregar ordens do técnico:', error);
            throw error;
        }
    }

    // Métodos de renderização
    renderOrders(data) {
        const container = document.getElementById('orders-container') || 
                         document.getElementById('gridView') ||
                         document.getElementById('orders-grid');
        
        if (!container) {
            console.warn('Container de ordens não encontrado');
            return;
        }

        // Limpar container
        container.innerHTML = '';

        // Verificar se há ordens
        if (!data.data || data.data.length === 0) {
            container.innerHTML = `
                <div class="no-orders-message">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>Nenhuma ordem encontrada</h3>
                    <p>Não há ordens de manutenção para os filtros selecionados.</p>
                </div>
            `;
            return;
        }

        // Renderizar ordens
        data.data.forEach(order => {
            const orderElement = this.createOrderElement(order);
            container.appendChild(orderElement);
        });
    }

    createOrderElement(order) {
        const div = document.createElement('div');
        div.className = 'order-card';
        div.innerHTML = `
            <div class="order-header">
                <span class="order-id">#${order.id}</span>
                <span class="order-status status-${order.status}">${order.status}</span>
            </div>
            <div class="order-body">
                <h5 class="order-title">${order.title || order.problem || 'Sem título'}</h5>
                <p class="order-description">${order.description || order.problem || ''}</p>
                <div class="order-meta">
                    <span class="priority priority-${order.priority}">
                        <i class="fas fa-flag"></i> ${order.priority}
                    </span>
                    <span class="branch">
                        <i class="fas fa-building"></i> ${order.station_name || order.branch_name || ''}
                    </span>
                </div>
            </div>
            <div class="order-footer">
                <button class="btn btn-primary btn-sm" onclick="unifiedOrderManager.viewOrder(${order.id})">
                    <i class="fas fa-eye"></i> Ver Detalhes
                </button>
            </div>
        `;
        return div;
    }

    updatePagination(meta) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer || !meta) return;

        paginationContainer.innerHTML = `
            <button class="btn btn-outline-primary" 
                    ${meta.page <= 1 ? 'disabled' : ''} 
                    onclick="unifiedOrderManager.goToPage(${meta.page - 1})">
                <i class="fas fa-chevron-left"></i> Anterior
            </button>
            <span class="pagination-info">
                Página ${meta.page} de ${meta.total_pages} (${meta.total} ordens)
            </span>
            <button class="btn btn-outline-primary" 
                    ${meta.page >= meta.total_pages ? 'disabled' : ''} 
                    onclick="unifiedOrderManager.goToPage(${meta.page + 1})">
                Próxima <i class="fas fa-chevron-right"></i>
            </button>
        `;
    }

    // Métodos de navegação
    goToPage(page) {
        this.currentPage = page;
        this.listOrders(page, this.filters);
    }

    viewOrder(id) {
        window.location.href = `/orders/${id}`;
    }

    // Métodos de UI
    showLoading() {
        const loader = document.getElementById('loading-indicator');
        if (loader) loader.style.display = 'block';
    }

    hideLoading() {
        const loader = document.getElementById('loading-indicator');
        if (loader) loader.style.display = 'none';
    }

    showError(message) {
        const errorContainer = document.getElementById('error-container');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
            `;
        } else {
            alert(message);
        }
    }
}

// Instância global
const unifiedOrderManager = new UnifiedOrderManager();

// Inicialização automática
document.addEventListener('DOMContentLoaded', function() {
    // Detectar página atual e inicializar adequadamente
    const currentPath = window.location.pathname;
    
    if (currentPath.includes('/orders') && !currentPath.includes('/calendar')) {
        // Página de listagem de ordens
        unifiedOrderManager.listOrders();
    } else if (currentPath.includes('/calendar')) {
        // Página de calendário
        const now = new Date();
        unifiedOrderManager.getCalendarOrders(now.getMonth() + 1, now.getFullYear());
    }
});

// Exportar para uso global
window.UnifiedOrderManager = UnifiedOrderManager;
window.unifiedOrderManager = unifiedOrderManager;
