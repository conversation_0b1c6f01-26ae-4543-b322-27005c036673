/**
 * Arquivo principal de JavaScript para a página de ordem técnico
 */

// Classe para gerenciar as ordens do técnico
class OrdemTecnicoManager {
    constructor() {
        this.currentOrderId = null;
        this.setupEventListeners();
    }

    // Configura os listeners de eventos
    setupEventListeners() {
        // Flip do card
        const flipButtons = document.querySelectorAll('.flip-trigger');
        flipButtons.forEach(button => {
            button.addEventListener('click', () => this.toggleCardFlip());
        });

        // Botão para voltar ao calendário
        const backToCalendarBtn = document.getElementById('back-to-calendar');
        if (backToCalendarBtn) {
            backToCalendarBtn.addEventListener('click', () => this.toggleCardFlip());
        }

        // Configuração dos cards de manutenção
        this.setupMaintenanceCards();

        // Inicializa o calendário
        this.initCalendar();
    }

    // Alterna o estado do card (frente/verso)
    toggleCardFlip() {
        const flipCard = document.getElementById('flip-card');
        if (flipCard) {
            flipCard.classList.toggle('flipped');

            // Se virou para o verso, configura os cards
            if (flipCard.classList.contains('flipped')) {
                setTimeout(() => {
                    this.setupMaintenanceCards();
                }, 500);
            }
        }
    }

    // Configura os cards de manutenção
    setupMaintenanceCards() {
        const cards = document.querySelectorAll('.maintenance-card');

        cards.forEach(card => {
            // Efeito de hover
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
                card.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.3)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
            });

            // Configuração de formulários
            const form = card.querySelector('form');
            if (form) {
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleFormSubmit(form);
                });
            }
        });
    }

    // Inicializa o calendário
    initCalendar() {
        const calendarDays = document.getElementById('calendar-days');
        const calendarTitle = document.querySelector('.calendar-title');
        const prevMonthBtn = document.querySelector('.prev-month');
        const nextMonthBtn = document.querySelector('.next-month');

        if (!calendarDays || !calendarTitle || !prevMonthBtn || !nextMonthBtn) return;

        const currentDate = new Date();
        let currentMonth = currentDate.getMonth();
        let currentYear = currentDate.getFullYear();

        // Renderiza o calendário inicial
        this.renderCalendar(currentMonth, currentYear);

        // Configura os botões de navegação
        prevMonthBtn.addEventListener('click', () => {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            this.renderCalendar(currentMonth, currentYear);
        });

        nextMonthBtn.addEventListener('click', () => {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            this.renderCalendar(currentMonth, currentYear);
        });
    }

    // Renderiza o calendário para o mês e ano especificados
    renderCalendar(month, year) {
        const calendarDays = document.getElementById('calendar-days');
        const calendarTitle = document.querySelector('.calendar-title');

        if (!calendarDays || !calendarTitle) return;

        // Limpa o calendário
        calendarDays.innerHTML = '';

        // Define o título do calendário
        const monthNames = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
        calendarTitle.textContent = `${monthNames[month]} ${year}`;

        // Obtém o primeiro dia do mês
        const firstDay = new Date(year, month, 1);
        const startingDay = firstDay.getDay();

        // Obtém o número de dias no mês
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        // Adiciona células vazias para os dias antes do primeiro dia do mês
        for (let i = 0; i < startingDay; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.className = 'date empty';
            calendarDays.appendChild(emptyCell);
        }

        // Adiciona células para cada dia do mês
        for (let i = 1; i <= daysInMonth; i++) {
            const dateCell = document.createElement('div');
            dateCell.className = 'date';
            dateCell.textContent = i;

            // Armazena a data no atributo data-date para uso posterior
            const dateStr = `${year}-${(month + 1).toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`;
            dateCell.setAttribute('data-date', dateStr);

            // Não adicionamos evento de clique aqui - será adicionado apenas para dias com ordens
            // durante o processamento em markCalendarDaysWithOrders

            calendarDays.appendChild(dateCell);
        }

        // Busca as ordens do técnico para este mês
        this.fetchTechnicianOrdersForMonth(year, month);
    }

    // Busca as ordens do técnico para um mês específico
    fetchTechnicianOrdersForMonth(year, month) {
        console.log(`Buscando ordens para ${month + 1}/${year}`);

        // Busca as ordens do técnico
        fetch('/api/orders/technician')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Erro HTTP: ${response.status}`);
                }
                return response.json();
            })
            .then(response => {
                console.log('Resposta da API:', response);

                if (response.success && response.data && response.data.length > 0) {
                    console.log(`Encontradas ${response.data.length} ordens para o técnico`);

                    // Processa as ordens e marca os dias no calendário
                    this.markCalendarDaysWithOrders(response.data, year, month);

                    // Também carrega as ordens para a lista lateral
                    this.renderTechnicianOrders(response.data);
                } else {
                    console.log('Nenhuma ordem encontrada para este técnico.');

                    // Limpa o container de ordens
                    const ordersContainer = document.getElementById('service-orders-container');
                    if (ordersContainer) {
                        ordersContainer.innerHTML = '<div class="alert alert-info">Nenhuma ordem encontrada para este técnico.</div>';
                    }

                    // Exibe uma notificação para o usuário
                    this.showNotification('Nenhuma ordem encontrada para este técnico. Verifique se você tem ordens atribuídas.', 'info');
                }
            })
            .catch(error => {
                console.error('Erro ao carregar ordens do técnico:', error);
                this.showNotification('Erro ao carregar ordens do técnico: ' + error.message, 'error');
            });
    }

    // Marca os dias no calendário que têm ordens
    markCalendarDaysWithOrders(orders, year, month) {
        // Cria um mapa de ordens por data para facilitar a busca
        const ordersByDate = {};

        console.log(`Processando ${orders.length} ordens para marcar no calendário (${month + 1}/${year})`);

        // Filtrar ordens inválidas antes de processá-las
        const validOrders = orders.filter(order => {
            // Verificar se a ordem tem ID numérico
            const hasValidId = !isNaN(parseInt(order.id));

            // Verificar se a ordem tem data válida
            let hasValidDate = false;
            try {
                if (order.data) {
                    const orderDate = new Date(order.data);
                    hasValidDate = !isNaN(orderDate.getTime());
                }
            } catch (e) {
                console.error('Erro ao validar data da ordem:', e);
            }

            // Retornar true apenas se o ID for válido e a data for válida
            return hasValidId && hasValidDate;
        });

        console.log(`Após filtragem, restaram ${validOrders.length} ordens válidas para processar`);

        // Para cada ordem válida, verifica se está no mês atual e adiciona ao mapa
        validOrders.forEach(order => {
            try {
                const orderDate = new Date(order.data);

                // Verifica se a ordem está no mês e ano atuais
                if (orderDate.getFullYear() === year && orderDate.getMonth() === month) {
                    const day = orderDate.getDate();
                    const dateStr = `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

                    // Inicializa o array para esta data se não existir
                    if (!ordersByDate[dateStr]) {
                        ordersByDate[dateStr] = [];
                    }

                    // Adiciona a ordem ao array desta data
                    ordersByDate[dateStr].push(order);
                    console.log(`Ordem #${order.id} adicionada para a data ${dateStr}`);
                }
            } catch (error) {
                console.error('Erro ao processar data da ordem:', error);
            }
        });

        // Agora marca os dias no calendário que têm ordens
        for (const dateStr in ordersByDate) {
            // Encontra a célula do dia correspondente usando o atributo data-date
            const dateCell = document.querySelector(`.date[data-date="${dateStr}"]`);

            if (dateCell) {
                // Marca o dia como tendo eventos
                dateCell.classList.add('has-events');

                // Obtém a primeira ordem para esta data (para definir o status visual)
                const firstOrder = ordersByDate[dateStr][0];
                const status = this.mapOrderStatusToEventType(firstOrder.status);

                // Define os atributos de dados
                dateCell.setAttribute('data-status', status);
                dateCell.setAttribute('data-events', ordersByDate[dateStr].length.toString());

                // Armazena os IDs das ordens como um array JSON no atributo data-order-ids
                dateCell.setAttribute('data-order-ids', JSON.stringify(ordersByDate[dateStr].map(o => o.id)));

                // Adiciona o indicador de evento
                const indicator = document.createElement('div');
                indicator.className = `event-indicator ${status}`;
                dateCell.appendChild(indicator);

                // Adiciona evento de clique para mostrar as ordens desta data
                dateCell.addEventListener('click', () => {
                    const day = new Date(dateStr).getDate();
                    this.showDayEvents(year, month, day, ordersByDate[dateStr]);
                });
            }
        }
    }

    // Mapeia o status da ordem para o tipo de evento no calendário
    mapOrderStatusToEventType(status) {
        if (!status) return 'waiting';

        status = status.toLowerCase();
        if (status === 'pendente') return 'waiting';
        if (status === 'aprovada') return 'approved';
        if (status === 'em_andamento' || status === 'em andamento') return 'scheduled';
        if (status === 'concluida' || status === 'concluído') return 'completed';
        if (status === 'urgente') return 'urgent';

        return 'waiting';
    }

    // Exibe os eventos de um dia específico
    showDayEvents(year, month, day, orders) {
        // Verifica se existem ordens para esta data
        if (!orders || orders.length === 0) {
            // Se não houver ordens, exibe uma notificação e não faz nada
            this.showNotification(`Não há ordens agendadas para ${day}/${month + 1}/${year}`, 'info');
            return;
        }

        console.log(`Processando ${orders.length} ordens para o dia ${day}/${month + 1}/${year}:`, orders);

        // Filtrar ordens inválidas
        const validOrders = orders.filter(order => {
            // Verificar se a ordem tem ID numérico
            const hasValidId = !isNaN(parseInt(order.id));

            // Verificar se a ordem tem data válida
            let hasValidDate = false;
            try {
                if (order.data) {
                    const orderDate = new Date(order.data);
                    hasValidDate = !isNaN(orderDate.getTime());
                }
            } catch (e) {
                console.error('Erro ao validar data da ordem:', e);
            }

            // Retornar true apenas se o ID for válido e a data for válida
            return hasValidId && hasValidDate;
        });

        console.log(`Após filtragem, restaram ${validOrders.length} ordens válidas`);

        // Verifica se existem ordens válidas após a filtragem
        if (validOrders.length === 0) {
            this.showNotification(`Não há ordens válidas para ${day}/${month + 1}/${year}`, 'warning');
            return;
        }

        // Se houver ordens válidas, carrega os detalhes da primeira ordem
        const orderId = validOrders[0].id;
        console.log(`Carregando detalhes da ordem #${orderId}`);

        // Carrega os detalhes da ordem
        this.loadOrderDetails(orderId);

        // Vira o card para mostrar os detalhes
        this.toggleCardFlip();

        // Se houver mais de uma ordem válida, exibe uma notificação informando
        if (validOrders.length > 1) {
            this.showNotification(`Existem ${validOrders.length} ordens para ${day}/${month + 1}/${year}. Mostrando a primeira.`, 'info');
        }
    }

    // Manipula o envio de formulários
    handleFormSubmit(form) {
        // Verificar se temos um ID de ordem válido
        if (!this.currentOrderId || isNaN(parseInt(this.currentOrderId))) {
            console.error('Tentativa de enviar formulário sem um ID de ordem válido');
            this.showNotification('ID de ordem inválido. Por favor, selecione uma ordem válida.', 'warning');
            return;
        }

        const formData = new FormData(form);
        const formObject = {};

        formData.forEach((value, key) => {
            formObject[key] = value;
        });

        // Enviar dados para o servidor via API
        fetch(`/api/orders/${this.currentOrderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formObject)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Dados salvos com sucesso!', 'success');
            } else {
                this.showNotification('Erro ao salvar dados: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Erro ao enviar dados:', error);
            this.showNotification('Erro ao enviar dados: ' + error.message, 'error');
        });
    }

    // Exibe uma notificação
    showNotification(message, type = 'info') {
        // Remove notificações existentes
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => {
            notification.remove();
        });

        // Cria a nova notificação
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Define o ícone com base no tipo
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'exclamation-circle';
        if (type === 'warning') icon = 'exclamation-triangle';

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${icon}"></i>
            </div>
            <div class="notification-content">
                ${message}
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Adiciona ao corpo do documento
        document.body.appendChild(notification);

        // Configura o botão de fechar
        const closeButton = notification.querySelector('.notification-close');
        closeButton.addEventListener('click', () => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 500);
        });

        // Remove automaticamente após 5 segundos
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.remove();
                    }
                }, 500);
            }
        }, 5000);
    }

    // Define o ID da ordem atual
    setCurrentOrderId(id) {
        this.currentOrderId = id;
        console.log(`Ordem atual definida: ${id}`);
    }

    // Carrega os detalhes da ordem
    loadOrderDetails(orderId) {
        // Verificar se temos um ID de ordem válido
        if (!orderId || isNaN(parseInt(orderId))) {
            console.error('Tentativa de carregar uma ordem com ID inválido');

            // Mostrar notificação de erro
            this.showNotification('ID de ordem inválido. Por favor, selecione uma ordem válida.', 'warning');
            return;
        }

        this.setCurrentOrderId(orderId);
        console.log(`Carregando detalhes da ordem ID: ${orderId}`);

        // Busca os dados da ordem via API
        fetch(`/api/orders/${orderId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Erro HTTP: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Resposta da API (loadOrderDetails):', data);

                if (data.success) {
                    // Verificar se a ordem retornada tem um ID válido
                    if (!data.data || !data.data.id || isNaN(parseInt(data.data.id))) {
                        throw new Error('Ordem com ID inválido');
                    }

                    // Preencher os dados com os valores reais da API
                    this.updateOrderDetails(data.data);
                    this.showNotification('Detalhes da ordem carregados com sucesso', 'success');
                } else {
                    this.showNotification('Erro ao carregar dados da ordem: ' + data.message, 'error');

                    // Verificar se é um problema de permissão
                    if (data.message && data.message.includes('permissão')) {
                        this.showNotification('Você não tem permissão para acessar esta ordem. Verifique se a ordem está atribuída a você ou ao seu prestador de serviço.', 'warning');
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao carregar dados da ordem:', error);
                this.showNotification('Erro ao carregar dados da ordem: ' + error.message, 'error');
            });
    }

    // Carrega as ordens do técnico atual
    loadTechnicianOrders() {
        console.log('Carregando ordens do técnico...');

        const ordersContainer = document.getElementById('service-orders-container');
        if (ordersContainer) {
            ordersContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Carregando ordens...</div>';
        }

        // Busca as ordens do técnico
        fetch('/api/orders/technician')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Erro HTTP: ${response.status}`);
                }
                return response.json();
            })
            .then(response => {
                console.log('Resposta da API (loadTechnicianOrders):', response);

                if (response.success && response.data && response.data.length > 0) {
                    console.log(`Encontradas ${response.data.length} ordens para o técnico`);
                    this.renderTechnicianOrders(response.data);
                } else {
                    if (ordersContainer) {
                        ordersContainer.innerHTML = `
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Nenhuma ordem encontrada para este técnico.
                                <hr>
                                <small>Verifique se você tem ordens atribuídas ou se seu prestador de serviço tem ordens pendentes.</small>
                            </div>`;
                    }
                    this.showNotification('Nenhuma ordem encontrada para este técnico. Verifique se você tem ordens atribuídas.', 'info');
                }
            })
            .catch(error => {
                console.error('Erro ao carregar ordens do técnico:', error);
                if (ordersContainer) {
                    ordersContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar ordens: ${error.message}
                            <hr>
                            <small>Tente novamente mais tarde ou entre em contato com o suporte.</small>
                        </div>`;
                }
                this.showNotification('Erro ao carregar ordens do técnico: ' + error.message, 'error');
            });
    }

    // Renderiza as ordens do técnico na interface
    renderTechnicianOrders(orders) {
        console.log('Renderizando ordens do técnico:', orders);

        const ordersContainer = document.getElementById('service-orders-container');
        if (!ordersContainer) return;

        // Limpa o container
        ordersContainer.innerHTML = '';

        // Filtrar ordens inválidas
        const validOrders = orders.filter(order => {
            // Verificar se a ordem tem ID numérico
            const hasValidId = !isNaN(parseInt(order.id));

            // Verificar se a ordem tem data válida
            let hasValidDate = false;
            try {
                if (order.data) {
                    const orderDate = new Date(order.data);
                    hasValidDate = !isNaN(orderDate.getTime());
                }
            } catch (e) {
                console.error('Erro ao validar data da ordem:', e);
            }

            // Verificar se a ordem tem título
            const hasTitle = !!order.titulo;

            // Verificar se a ordem tem filial
            const hasBranch = !!order.filial;

            // Retornar true apenas se todos os campos essenciais forem válidos
            return hasValidId && hasValidDate && hasTitle && hasBranch;
        });

        console.log(`Após filtragem, restaram ${validOrders.length} ordens válidas para exibição`);

        // Se não houver ordens válidas após a filtragem
        if (validOrders.length === 0) {
            ordersContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Nenhuma ordem válida encontrada para este técnico.
                    <hr>
                    <small>Verifique se você tem ordens atribuídas ou se seu prestador de serviço tem ordens pendentes.</small>
                </div>`;
            return;
        }

        // Renderiza cada ordem válida
        validOrders.forEach(order => {
            const orderElement = document.createElement('div');
            orderElement.className = 'service-order-item';
            orderElement.setAttribute('data-id', order.id);
            orderElement.setAttribute('data-status', (order.status || 'pendente').toLowerCase());
            orderElement.setAttribute('data-date', this.formatarData(order.data));

            // Define a classe de prioridade
            let priorityClass = 'bg-primary';
            const priority = (order.prioridade || '').toLowerCase();
            if (priority === 'alta' || priority === 'urgente') {
                priorityClass = 'bg-danger';
            } else if (priority === 'média' || priority === 'media') {
                priorityClass = 'bg-warning';
            } else if (priority === 'baixa') {
                priorityClass = 'bg-success';
            }

            orderElement.innerHTML = `
                <div class="order-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="order-details">
                    <h6 class="order-title">Ordem #${order.id} - ${order.titulo}</h6>
                    <div class="order-info">
                        <span class="order-location"><i class="fas fa-map-marker-alt"></i> ${order.filial}</span>
                        <span class="order-equipment"><i class="fas fa-cog"></i> ${order.equipamento}</span>
                    </div>
                    <div class="order-priority">
                        <span class="badge ${priorityClass}">${order.prioridade}</span>
                    </div>
                </div>
            `;

            // Adiciona evento de clique
            orderElement.addEventListener('click', () => {
                this.loadOrderDetails(order.id);

                // Virar o cartão do calendário
                const flipCard = document.getElementById('flip-card');
                if (flipCard) {
                    flipCard.classList.add('flipped');
                }

                // Configurar os cards após o flip
                setTimeout(() => {
                    this.setupMaintenanceCards();
                }, 500);
            });

            ordersContainer.appendChild(orderElement);
        });
    }

    // Atualiza os detalhes da ordem na interface
    updateOrderDetails(order) {
        // Atualiza o título da ordem
        const orderTitle = document.getElementById('order-title');
        if (orderTitle) {
            orderTitle.textContent = `Ordem #${order.id} - ${order.titulo || 'Manutenção'}`;
        }

        // Atualiza o tipo de manutenção
        const maintenanceType = document.getElementById('order-maintenance-type');
        if (maintenanceType) {
            maintenanceType.textContent = order.tipo || 'Manutenção Preventiva';
        }

        // Atualiza os detalhes da ordem
        const orderDetails = document.getElementById('order-details');
        if (orderDetails) {
            orderDetails.innerHTML = `
                <div class="order-details-item">
                    <div class="order-details-label">Equipamento:</div>
                    <div class="order-details-value">${order.equipamento || 'Não especificado'}</div>
                </div>
                <div class="order-details-item">
                    <div class="order-details-label">Filial:</div>
                    <div class="order-details-value">${order.filial || 'Não especificada'}</div>
                </div>
                <div class="order-details-item">
                    <div class="order-details-label">Status:</div>
                    <div class="order-details-value">
                        <span class="badge ${this.getStatusClass(order.status)}">${order.status || 'Pendente'}</span>
                    </div>
                </div>
                <div class="order-details-item">
                    <div class="order-details-label">Prioridade:</div>
                    <div class="order-details-value">
                        <span class="badge ${this.getPriorityClass(order.prioridade)}">${order.prioridade || 'Média'}</span>
                    </div>
                </div>
                <div class="order-details-item">
                    <div class="order-details-label">Data de Abertura:</div>
                    <div class="order-details-value">${this.formatarData(order.data) || 'Não especificada'}</div>
                </div>
            `;
        }

        // Atualiza a descrição do problema
        const problemDescription = document.getElementById('problem-description');
        if (problemDescription) {
            problemDescription.textContent = order.problema || 'Nenhuma descrição disponível.';
        }
    }

    // Obtém a classe CSS para o status da ordem
    getStatusClass(status) {
        if (!status) return 'bg-secondary';

        status = status.toLowerCase();
        if (status === 'pendente') return 'bg-warning';
        if (status === 'em_andamento' || status === 'em andamento') return 'bg-primary';
        if (status === 'concluido' || status === 'concluído') return 'bg-success';
        if (status === 'cancelado') return 'bg-secondary';
        if (status === 'urgente') return 'bg-danger';

        return 'bg-secondary';
    }

    // Obtém a classe CSS para a prioridade da ordem
    getPriorityClass(priority) {
        if (!priority) return 'bg-primary';

        priority = priority.toLowerCase();
        if (priority === 'baixa') return 'bg-success';
        if (priority === 'media' || priority === 'média') return 'bg-primary';
        if (priority === 'alta') return 'bg-warning';
        if (priority === 'urgente') return 'bg-danger';

        return 'bg-primary';
    }

    // Formata uma data para exibição
    formatarData(dataString) {
        if (!dataString) return '';

        try {
            const data = new Date(dataString);
            return data.toLocaleDateString('pt-BR');
        } catch (error) {
            console.error('Erro ao formatar data:', error);
            return dataString;
        }
    }
}

// Inicializa o gerenciador quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    window.OrdemTecnicoDetails = new OrdemTecnicoManager();

    // Carrega as ordens do técnico atual
    if (window.OrdemTecnicoDetails) {
        window.OrdemTecnicoDetails.loadTechnicianOrders();
    }
});

// Função para configurar os cards de serviço
function setupServiceBoxes() {
    const serviceBoxes = document.querySelectorAll('.service-box');

    serviceBoxes.forEach(box => {
        box.addEventListener('click', function() {
            // Remove a classe 'active' de todas as caixas
            serviceBoxes.forEach(b => b.classList.remove('active'));

            // Adiciona a classe 'active' à caixa clicada
            this.classList.add('active');

            // Obtém o tipo de serviço da caixa clicada
            const serviceType = this.getAttribute('data-service-type');

            // Atualiza o tipo de serviço no título
            document.getElementById('service-type').textContent = serviceType;
        });
    });
}

// Função para configurar os itens de documento
function setupDocumentItems() {
    const documentItems = document.querySelectorAll('.document-item');

    documentItems.forEach(item => {
        item.addEventListener('click', function() {
            // Abre o modal de detalhes do documento
            const modal = document.getElementById('service-detail-modal');
            if (modal) {
                modal.classList.add('active');

                // Obtém o título do documento
                const title = this.querySelector('.document-title').textContent;

                // Atualiza o título do modal
                document.getElementById('modal-title').textContent = title;

                // Mostra indicador de carregamento
                document.getElementById('modal-content').innerHTML = `
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Carregando documento...</span>
                    </div>
                `;

                // Carrega o conteúdo real do documento do servidor
                fetch(`/api/documentos/${this.getAttribute('data-document-id')}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('modal-content').innerHTML = `
                                <div class="document-preview">
                                    <div class="document-header">
                                        <h4>${data.title}</h4>
                                        <p>${data.description || ''}</p>
                                    </div>
                                    <div class="document-body">
                                        ${data.content || 'Nenhum conteúdo disponível.'}
                                    </div>
                                </div>
                            `;
                        } else {
                            document.getElementById('modal-content').innerHTML = `
                                <div class="alert alert-danger">
                                    Erro ao carregar documento: ${data.message || 'Erro desconhecido'}
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        document.getElementById('modal-content').innerHTML = `
                            <div class="alert alert-danger">
                                Erro ao carregar documento: ${error.message || 'Erro desconhecido'}
                            </div>
                        `;
                    });
            }
        });
    });

    // Configura o botão de fechar o modal
    const closeModalBtn = document.getElementById('close-modal');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', function() {
            const modal = document.getElementById('service-detail-modal');
            if (modal) {
                modal.classList.remove('active');
            }
        });
    }
}
