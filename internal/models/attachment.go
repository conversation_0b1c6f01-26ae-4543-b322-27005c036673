package models

import (
	"fmt"
	"strings"
	"time"
)

// AttachmentType define os tipos de anexo suportados
type AttachmentType string

const (
	AttachmentTypeImage    AttachmentType = "image"
	AttachmentTypeDocument AttachmentType = "document"
	AttachmentTypeVideo    AttachmentType = "video"
	AttachmentTypeAudio    AttachmentType = "audio"
	AttachmentTypeOther    AttachmentType = "other"
)

// AttachmentCategory define categorias de anexos
type AttachmentCategory string

const (
	CategoryTechnical   AttachmentCategory = "technical"   // Documentos técnicos
	CategoryEvidence    AttachmentCategory = "evidence"    // Evidências (fotos antes/depois)
	CategoryInvoice     AttachmentCategory = "invoice"     // Notas fiscais
	CategoryContract    AttachmentCategory = "contract"    // Contratos
	CategoryCertificate AttachmentCategory = "certificate" // Certificados
	CategoryManual      AttachmentCategory = "manual"      // Manuais
	CategoryOther       AttachmentCategory = "other"       // Outros

	// Categorias legadas (compatibilidade)
	CategoryEquipment       = "equipment"        // Foto do equipamento
	CategoryEquipmentBefore = "equipment_before" // Foto do equipamento antes do reparo
	CategoryEquipmentAfter  = "equipment_after"  // Foto do equipamento após o reparo
	CategoryPartOld         = "part_old"         // Foto da peça antiga/com defeito
	CategoryPartNew         = "part_new"         // Foto da peça nova/substituição
	CategoryReport          = "report"           // Relatório técnico
)

// Attachment representa um arquivo anexado ao sistema
type Attachment struct {
	ID        uint       `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"index"`

	// Informações básicas do arquivo
	OriginalName string `json:"original_name" gorm:"not null"`    // Nome original do arquivo
	FileName     string `json:"file_name" gorm:"not null;unique"` // Nome único no sistema
	FilePath     string `json:"file_path" gorm:"not null"`        // Caminho completo do arquivo
	FileSize     int64  `json:"file_size" gorm:"not null"`        // Tamanho em bytes
	MimeType     string `json:"mime_type" gorm:"not null"`        // Tipo MIME
	FileHash     string `json:"file_hash" gorm:"not null;index"`  // Hash MD5 para detecção de duplicatas

	// Classificação
	Type     AttachmentType     `json:"type" gorm:"not null"`     // Tipo do anexo
	Category AttachmentCategory `json:"category" gorm:"not null"` // Categoria do anexo

	// Metadados
	Title       string `json:"title"`                          // Título personalizado
	Description string `json:"description"`                    // Descrição do arquivo
	Tags        string `json:"tags"`                           // Tags separadas por vírgula
	IsPublic    bool   `json:"is_public" gorm:"default:false"` // Se é público ou privado
	IsActive    bool   `json:"is_active" gorm:"default:true"`  // Se está ativo

	// Informações de imagem (se aplicável)
	ImageWidth   *int `json:"image_width,omitempty"`              // Largura da imagem
	ImageHeight  *int `json:"image_height,omitempty"`             // Altura da imagem
	HasThumbnail bool `json:"has_thumbnail" gorm:"default:false"` // Se tem thumbnail

	// Versionamento
	Version         int    `json:"version" gorm:"default:1"`              // Versão do arquivo
	ParentID        *uint  `json:"parent_id,omitempty"`                   // ID do arquivo pai (para versionamento)
	VersionNotes    string `json:"version_notes"`                         // Notas da versão
	IsLatestVersion bool   `json:"is_latest_version" gorm:"default:true"` // Se é a versão mais recente

	// Relacionamentos
	EntityType string `json:"entity_type" gorm:"not null"` // Tipo da entidade (order, equipment, user, etc.)
	EntityID   uint   `json:"entity_id" gorm:"not null"`   // ID da entidade

	// Auditoria
	UploadedByID   uint   `json:"uploaded_by_id" gorm:"not null"`   // ID do usuário que fez upload
	UploadedByName string `json:"uploaded_by_name" gorm:"not null"` // Nome do usuário que fez upload
	IPAddress      string `json:"ip_address"`                       // IP de onde foi feito o upload
	UserAgent      string `json:"user_agent"`                       // User agent do navegador

	// Relacionamentos GORM
	UploadedByUser User         `json:"uploaded_by_user,omitempty" gorm:"foreignKey:UploadedByID"`
	Parent         *Attachment  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Versions       []Attachment `json:"versions,omitempty" gorm:"foreignKey:ParentID"`

	// Campos legados (compatibilidade)
	File                  string `json:"file" gorm:"-"`                        // Compatibilidade
	MaintenanceOrderID    uint   `json:"maintenance_order_id" gorm:"index"`    // Compatibilidade
	MaintenanceActivityID uint   `json:"maintenance_activity_id" gorm:"index"` // Compatibilidade
	MaterialID            uint   `json:"material_id" gorm:"index"`             // Compatibilidade
}

// TableName especifica o nome da tabela
func (Attachment) TableName() string {
	return "attachments"
}

// AttachmentUploadRequest representa uma requisição de upload
type AttachmentUploadRequest struct {
	EntityType  string             `json:"entity_type" binding:"required"`
	EntityID    uint               `json:"entity_id" binding:"required"`
	Category    AttachmentCategory `json:"category" binding:"required"`
	Title       string             `json:"title"`
	Description string             `json:"description"`
	Tags        string             `json:"tags"`
	IsPublic    bool               `json:"is_public"`
}

// AttachmentResponse representa a resposta de um anexo
type AttachmentResponse struct {
	ID                uint               `json:"id"`
	OriginalName      string             `json:"original_name"`
	FileName          string             `json:"file_name"`
	FileSize          int64              `json:"file_size"`
	FileSizeFormatted string             `json:"file_size_formatted"`
	MimeType          string             `json:"mime_type"`
	Type              AttachmentType     `json:"type"`
	Category          AttachmentCategory `json:"category"`
	Title             string             `json:"title"`
	Description       string             `json:"description"`
	Tags              []string           `json:"tags"`
	IsPublic          bool               `json:"is_public"`
	ImageWidth        *int               `json:"image_width,omitempty"`
	ImageHeight       *int               `json:"image_height,omitempty"`
	HasThumbnail      bool               `json:"has_thumbnail"`
	Version           int                `json:"version"`
	IsLatestVersion   bool               `json:"is_latest_version"`
	VersionNotes      string             `json:"version_notes"`
	UploadedByName    string             `json:"uploaded_by_name"`
	CreatedAt         time.Time          `json:"created_at"`
	UpdatedAt         time.Time          `json:"updated_at"`
	DownloadURL       string             `json:"download_url"`
	ThumbnailURL      string             `json:"thumbnail_url,omitempty"`
	PreviewURL        string             `json:"preview_url,omitempty"`
}

// AttachmentFilter representa filtros para busca de anexos
type AttachmentFilter struct {
	EntityType string             `json:"entity_type"`
	EntityID   uint               `json:"entity_id"`
	Type       AttachmentType     `json:"type"`
	Category   AttachmentCategory `json:"category"`
	Tags       string             `json:"tags"`
	IsPublic   *bool              `json:"is_public"`
	IsActive   *bool              `json:"is_active"`
	OnlyLatest bool               `json:"only_latest"` // Apenas versões mais recentes
}

// AttachmentStats representa estatísticas de anexos
type AttachmentStats struct {
	TotalFiles         int64  `json:"total_files"`
	TotalSize          int64  `json:"total_size"`
	TotalSizeFormatted string `json:"total_size_formatted"`
	ImageCount         int64  `json:"image_count"`
	DocumentCount      int64  `json:"document_count"`
	VideoCount         int64  `json:"video_count"`
	AudioCount         int64  `json:"audio_count"`
	OtherCount         int64  `json:"other_count"`
}

// AttachmentRequest representa a estrutura legada para compatibilidade
type AttachmentRequest struct {
	Type                  string `json:"type" binding:"required"`
	Description           string `json:"description"`
	MaintenanceOrderID    uint   `json:"maintenance_order_id"`
	MaintenanceActivityID uint   `json:"maintenance_activity_id"`
	MaterialID            uint   `json:"material_id"`
}

// GetFileExtension retorna a extensão do arquivo
func (a *Attachment) GetFileExtension() string {
	if len(a.OriginalName) == 0 {
		return ""
	}

	for i := len(a.OriginalName) - 1; i >= 0; i-- {
		if a.OriginalName[i] == '.' {
			return a.OriginalName[i+1:]
		}
	}
	return ""
}

// IsImage verifica se o anexo é uma imagem
func (a *Attachment) IsImage() bool {
	return a.Type == AttachmentTypeImage
}

// IsDocument verifica se o anexo é um documento
func (a *Attachment) IsDocument() bool {
	return a.Type == AttachmentTypeDocument
}

// GetTagsSlice retorna as tags como slice
func (a *Attachment) GetTagsSlice() []string {
	if a.Tags == "" {
		return []string{}
	}

	tags := strings.Split(a.Tags, ",")
	result := make([]string, 0, len(tags))

	for _, tag := range tags {
		tag = strings.TrimSpace(tag)
		if tag != "" {
			result = append(result, tag)
		}
	}

	return result
}

// FormatFileSize formata o tamanho do arquivo
func (a *Attachment) FormatFileSize() string {
	size := float64(a.FileSize)
	units := []string{"B", "KB", "MB", "GB", "TB"}

	for i, unit := range units {
		if size < 1024 || i == len(units)-1 {
			if i == 0 {
				return fmt.Sprintf("%.0f %s", size, unit)
			}
			return fmt.Sprintf("%.1f %s", size, unit)
		}
		size /= 1024
	}

	return fmt.Sprintf("%.1f %s", size, units[len(units)-1])
}

// GetAttachmentTypeLabel retorna um rótulo legível para o tipo de anexo (compatibilidade)
func GetAttachmentTypeLabel(attachmentType string) string {
	switch attachmentType {
	case string(CategoryEquipment):
		return "Foto do Equipamento"
	case string(CategoryEquipmentBefore):
		return "Antes do Reparo"
	case string(CategoryEquipmentAfter):
		return "Após o Reparo"
	case string(CategoryPartOld):
		return "Peça Antiga"
	case string(CategoryPartNew):
		return "Peça Nova"
	case string(AttachmentTypeDocument):
		return "Documento"
	case string(CategoryInvoice):
		return "Nota Fiscal"
	case string(CategoryReport):
		return "Relatório Técnico"
	default:
		return "Anexo"
	}
}
