package models

import "time"

// AuditLog representa um log de auditoria de segurança
type AuditLog struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    *uint     `json:"user_id" gorm:"column:user_id"`
	Action    string    `json:"action" gorm:"size:100;not null"`
	Resource  string    `json:"resource" gorm:"size:100"`
	Details   string    `json:"details" gorm:"type:text"`
	IP        string    `json:"ip" gorm:"size:50"`
	UserAgent string    `json:"user_agent" gorm:"size:255"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// TableName especifica o nome da tabela para o modelo AuditLog
func (AuditLog) TableName() string {
	return "audit_logs"
}
