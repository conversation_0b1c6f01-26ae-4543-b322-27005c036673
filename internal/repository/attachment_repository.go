package repository

import (
	"fmt"
	"log"
	"time"

	"tradicao/internal/models"

	"gorm.io/gorm"
)

// AttachmentRepository interface para operações de anexos
type AttachmentRepository interface {
	Create(attachment *models.Attachment) error
	GetByID(id uint) (*models.Attachment, error)
	GetByEntity(entityType string, entityID uint, filter *models.AttachmentFilter) ([]models.Attachment, error)
	GetByFilter(filter *models.AttachmentFilter) ([]models.Attachment, error)
	Update(attachment *models.Attachment) error
	Delete(id uint) error
	SoftDelete(id uint) error
	GetStats(entityType string, entityID uint) (*models.AttachmentStats, error)
	GetVersions(parentID uint) ([]models.Attachment, error)
	CreateVersion(attachment *models.Attachment) error
	MarkAsLatestVersion(id uint) error
	CheckDuplicate(fileHash string) (*models.Attachment, error)
	GetByFileHash(fileHash string) ([]models.Attachment, error)
}

// GormAttachmentRepository implementação GORM do repositório de anexos
type GormAttachmentRepository struct {
	db *gorm.DB
}

// NewGormAttachmentRepository cria uma nova instância do repositório
func NewGormAttachmentRepository(db *gorm.DB) AttachmentRepository {
	return &GormAttachmentRepository{db: db}
}

// Create cria um novo anexo
func (r *GormAttachmentRepository) Create(attachment *models.Attachment) error {
	if attachment == nil {
		return fmt.Errorf("attachment não pode ser nil")
	}

	// Definir timestamps
	now := time.Now()
	attachment.CreatedAt = now
	attachment.UpdatedAt = now

	// Criar o anexo
	if err := r.db.Create(attachment).Error; err != nil {
		log.Printf("Erro ao criar anexo: %v", err)
		return fmt.Errorf("erro ao criar anexo: %w", err)
	}

	log.Printf("Anexo criado com sucesso: ID=%d, Nome=%s", attachment.ID, attachment.OriginalName)
	return nil
}

// GetByID busca um anexo por ID
func (r *GormAttachmentRepository) GetByID(id uint) (*models.Attachment, error) {
	var attachment models.Attachment

	err := r.db.Preload("UploadedByUser").
		Preload("Parent").
		Preload("Versions").
		First(&attachment, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("anexo não encontrado")
		}
		return nil, fmt.Errorf("erro ao buscar anexo: %w", err)
	}

	return &attachment, nil
}

// GetByEntity busca anexos por entidade
func (r *GormAttachmentRepository) GetByEntity(entityType string, entityID uint, filter *models.AttachmentFilter) ([]models.Attachment, error) {
	var attachments []models.Attachment

	query := r.db.Where("entity_type = ? AND entity_id = ? AND is_active = ?", entityType, entityID, true)

	// Aplicar filtros adicionais
	if filter != nil {
		query = r.applyFilters(query, filter)
	}

	// Ordenar por data de criação (mais recente primeiro)
	query = query.Order("created_at DESC")

	// Preload relacionamentos
	query = query.Preload("UploadedByUser").
		Preload("Parent").
		Preload("Versions")

	err := query.Find(&attachments).Error
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar anexos: %w", err)
	}

	return attachments, nil
}

// GetByFilter busca anexos por filtros
func (r *GormAttachmentRepository) GetByFilter(filter *models.AttachmentFilter) ([]models.Attachment, error) {
	var attachments []models.Attachment

	query := r.db.Where("is_active = ?", true)

	if filter != nil {
		query = r.applyFilters(query, filter)
	}

	query = query.Order("created_at DESC").
		Preload("UploadedByUser").
		Preload("Parent").
		Preload("Versions")

	err := query.Find(&attachments).Error
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar anexos: %w", err)
	}

	return attachments, nil
}

// Update atualiza um anexo
func (r *GormAttachmentRepository) Update(attachment *models.Attachment) error {
	if attachment == nil {
		return fmt.Errorf("attachment não pode ser nil")
	}

	attachment.UpdatedAt = time.Now()

	err := r.db.Save(attachment).Error
	if err != nil {
		log.Printf("Erro ao atualizar anexo ID=%d: %v", attachment.ID, err)
		return fmt.Errorf("erro ao atualizar anexo: %w", err)
	}

	log.Printf("Anexo atualizado com sucesso: ID=%d", attachment.ID)
	return nil
}

// Delete remove um anexo permanentemente
func (r *GormAttachmentRepository) Delete(id uint) error {
	err := r.db.Unscoped().Delete(&models.Attachment{}, id).Error
	if err != nil {
		log.Printf("Erro ao deletar anexo ID=%d: %v", id, err)
		return fmt.Errorf("erro ao deletar anexo: %w", err)
	}

	log.Printf("Anexo deletado permanentemente: ID=%d", id)
	return nil
}

// SoftDelete marca um anexo como inativo
func (r *GormAttachmentRepository) SoftDelete(id uint) error {
	err := r.db.Model(&models.Attachment{}).
		Where("id = ?", id).
		Update("is_active", false).Error

	if err != nil {
		log.Printf("Erro ao desativar anexo ID=%d: %v", id, err)
		return fmt.Errorf("erro ao desativar anexo: %w", err)
	}

	log.Printf("Anexo desativado: ID=%d", id)
	return nil
}

// GetStats retorna estatísticas de anexos
func (r *GormAttachmentRepository) GetStats(entityType string, entityID uint) (*models.AttachmentStats, error) {
	var stats models.AttachmentStats

	baseQuery := r.db.Model(&models.Attachment{}).Where("is_active = ?", true)

	if entityType != "" && entityID > 0 {
		baseQuery = baseQuery.Where("entity_type = ? AND entity_id = ?", entityType, entityID)
	}

	// Total de arquivos
	baseQuery.Count(&stats.TotalFiles)

	// Total de tamanho
	baseQuery.Select("COALESCE(SUM(file_size), 0)").Scan(&stats.TotalSize)

	// Contagem por tipo
	baseQuery.Where("type = ?", models.AttachmentTypeImage).Count(&stats.ImageCount)
	baseQuery.Where("type = ?", models.AttachmentTypeDocument).Count(&stats.DocumentCount)
	baseQuery.Where("type = ?", models.AttachmentTypeVideo).Count(&stats.VideoCount)
	baseQuery.Where("type = ?", models.AttachmentTypeAudio).Count(&stats.AudioCount)
	baseQuery.Where("type = ?", models.AttachmentTypeOther).Count(&stats.OtherCount)

	// Formatar tamanho total
	stats.TotalSizeFormatted = formatFileSize(stats.TotalSize)

	return &stats, nil
}

// GetVersions retorna todas as versões de um arquivo
func (r *GormAttachmentRepository) GetVersions(parentID uint) ([]models.Attachment, error) {
	var versions []models.Attachment

	err := r.db.Where("parent_id = ? AND is_active = ?", parentID, true).
		Order("version DESC").
		Preload("UploadedByUser").
		Find(&versions).Error

	if err != nil {
		return nil, fmt.Errorf("erro ao buscar versões: %w", err)
	}

	return versions, nil
}

// CreateVersion cria uma nova versão de um arquivo
func (r *GormAttachmentRepository) CreateVersion(attachment *models.Attachment) error {
	if attachment == nil {
		return fmt.Errorf("attachment não pode ser nil")
	}

	// Iniciar transação
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Marcar versões anteriores como não sendo a mais recente
	if attachment.ParentID != nil {
		err := tx.Model(&models.Attachment{}).
			Where("parent_id = ? OR id = ?", *attachment.ParentID, *attachment.ParentID).
			Update("is_latest_version", false).Error
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("erro ao atualizar versões anteriores: %w", err)
		}
	}

	// Criar nova versão
	attachment.IsLatestVersion = true
	if err := tx.Create(attachment).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao criar nova versão: %w", err)
	}

	// Commit da transação
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("erro ao confirmar transação: %w", err)
	}

	log.Printf("Nova versão criada: ID=%d, Versão=%d", attachment.ID, attachment.Version)
	return nil
}

// MarkAsLatestVersion marca uma versão específica como a mais recente
func (r *GormAttachmentRepository) MarkAsLatestVersion(id uint) error {
	// Buscar o anexo
	var attachment models.Attachment
	if err := r.db.First(&attachment, id).Error; err != nil {
		return fmt.Errorf("anexo não encontrado: %w", err)
	}

	// Iniciar transação
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Marcar todas as versões como não sendo a mais recente
	parentID := attachment.ParentID
	if parentID == nil {
		parentID = &attachment.ID
	}

	err := tx.Model(&models.Attachment{}).
		Where("parent_id = ? OR id = ?", *parentID, *parentID).
		Update("is_latest_version", false).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao atualizar versões: %w", err)
	}

	// Marcar a versão específica como a mais recente
	err = tx.Model(&models.Attachment{}).
		Where("id = ?", id).
		Update("is_latest_version", true).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao marcar como versão mais recente: %w", err)
	}

	// Commit da transação
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("erro ao confirmar transação: %w", err)
	}

	log.Printf("Versão marcada como mais recente: ID=%d", id)
	return nil
}

// CheckDuplicate verifica se existe um arquivo com o mesmo hash
func (r *GormAttachmentRepository) CheckDuplicate(fileHash string) (*models.Attachment, error) {
	var attachment models.Attachment

	err := r.db.Where("file_hash = ? AND is_active = ?", fileHash, true).
		First(&attachment).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // Não é duplicata
		}
		return nil, fmt.Errorf("erro ao verificar duplicata: %w", err)
	}

	return &attachment, nil
}

// GetByFileHash busca anexos por hash do arquivo
func (r *GormAttachmentRepository) GetByFileHash(fileHash string) ([]models.Attachment, error) {
	var attachments []models.Attachment

	err := r.db.Where("file_hash = ? AND is_active = ?", fileHash, true).
		Preload("UploadedByUser").
		Find(&attachments).Error

	if err != nil {
		return nil, fmt.Errorf("erro ao buscar por hash: %w", err)
	}

	return attachments, nil
}

// applyFilters aplica filtros à query
func (r *GormAttachmentRepository) applyFilters(query *gorm.DB, filter *models.AttachmentFilter) *gorm.DB {
	if filter.EntityType != "" {
		query = query.Where("entity_type = ?", filter.EntityType)
	}

	if filter.EntityID > 0 {
		query = query.Where("entity_id = ?", filter.EntityID)
	}

	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}

	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}

	if filter.Tags != "" {
		query = query.Where("tags LIKE ?", "%"+filter.Tags+"%")
	}

	if filter.IsPublic != nil {
		query = query.Where("is_public = ?", *filter.IsPublic)
	}

	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}

	if filter.OnlyLatest {
		query = query.Where("is_latest_version = ?", true)
	}

	return query
}

// formatFileSize formata o tamanho do arquivo
func formatFileSize(size int64) string {
	sizeFloat := float64(size)
	units := []string{"B", "KB", "MB", "GB", "TB"}

	for i, unit := range units {
		if sizeFloat < 1024 || i == len(units)-1 {
			if i == 0 {
				return fmt.Sprintf("%.0f %s", sizeFloat, unit)
			}
			return fmt.Sprintf("%.1f %s", sizeFloat, unit)
		}
		sizeFloat /= 1024
	}

	return fmt.Sprintf("%.1f %s", sizeFloat, units[len(units)-1])
}
