package services

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"gorm.io/gorm"
)

// OrderServiceInterface define a interface para o serviço de ordens
type OrderServiceInterface interface {
	// Métodos originais
	CreateOrder(ctx context.Context, order *models.MaintenanceOrder) error
	UpdateOrder(ctx context.Context, order *models.MaintenanceOrder) error
	GetAllOrders() ([]models.MaintenanceOrder, error)
	UpdateStatus(ctx context.Context, orderID uint, status models.OrderStatus, userID uint, reason string) error
	GetOrderDetails(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error)
	GetOrderDetailsByID(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error)
	GetCostsByOrderID(orderID uint) ([]models.CostItem, error)
	AddCost(orderID uint, userID uint, cost models.CostItem) (*models.CostItem, error)
	AssignProvider(orderID uint, userID uint, providerID uint) (*models.MaintenanceOrder, error)
	SubmitForApproval(orderID uint, userID uint) (*models.MaintenanceOrder, error)
	Approve(orderID uint, userID uint) (*models.MaintenanceOrder, error)
	Reject(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error)
	Cancel(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error)
	AddInteraction(orderID uint, userID uint, message string) (*models.Interaction, error)
	UploadInvoice(orderID uint, userID uint, invoiceData models.Invoice, attachment models.Attachment) (*models.Invoice, error)
	GetOrderCosts(orderID uint) ([]models.CostItem, error)
	GetOrdersWithPagination(offset, limit int, status, branchID, startDate, endDate string) ([]models.MaintenanceOrder, int, error)
	GetOrderCountsByStatus() (pendingCount, inProgressCount, completedCount int)
	GetOrderInteractions(orderID, offset, limit uint) ([]models.Interaction, error)

	// Métodos adicionais para compatibilidade com OrderServiceAdapted
	CreateOrderAdapted(ctx context.Context, order *models.MaintenanceOrderAdapted) error
	UpdateOrderAdapted(ctx context.Context, order *models.MaintenanceOrderAdapted) error
	DeleteOrder(ctx context.Context, id models.ID) error
	ListOrders(ctx context.Context, page, pageSize int) ([]models.MaintenanceOrderAdapted, int, error)
}

// isValidOrderStatusTransition verifica se a transição de status é válida para ordens de manutenção
// Esta função usa as constantes padronizadas e funções de compatibilidade para verificar transições
func isValidOrderStatusTransition(atual models.OrderStatus, novo models.OrderStatus, _ uint) bool {
	// Normalizar os status para garantir compatibilidade com valores legados
	atualNormalizado := models.NormalizeOrderStatus(string(atual))
	novoNormalizado := models.NormalizeOrderStatus(string(novo))

	// Transições permitidas usando constantes padronizadas
	switch atualNormalizado {
	case models.StatusPending:
		return novoNormalizado == models.StatusInProgress || novoNormalizado == models.StatusCancelled
	case models.StatusInProgress:
		return novoNormalizado == models.StatusCompleted || novoNormalizado == models.StatusCancelled
	case models.StatusCompleted:
		return novoNormalizado == models.StatusInProgress // Reabrir uma ordem concluída
	case models.StatusCancelled:
		return novoNormalizado == models.StatusPending // Reabrir uma ordem cancelada
	default:
		return false
	}
}

// OrderServiceAdapter adapta o OrderService para a interface MaintenanceOrderService
type OrderServiceAdapter struct {
	service *OrderService
	DB      *gorm.DB
}

// NewOrderServiceAdapter cria um novo adaptador para o serviço de ordens
func NewOrderServiceAdapter(service *OrderService, db *gorm.DB) OrderServiceInterface {
	return &OrderServiceAdapter{
		service: service,
		DB:      db,
	}
}

// CreateOrder implementa o método CreateOrder da interface MaintenanceOrderService
func (a *OrderServiceAdapter) CreateOrder(ctx context.Context, order *models.MaintenanceOrder) error {
	// Verificar se os campos obrigatórios estão preenchidos
	if order.BranchID == 0 {
		return fmt.Errorf("branch_id é obrigatório")
	}

	if order.Problem == "" {
		return fmt.Errorf("problema é obrigatório")
	}

	// Configurar campos padrão
	// ID será gerado automaticamente pelo banco de dados

	if order.CreatedAt.IsZero() {
		order.CreatedAt = time.Now()
	}

	order.UpdatedAt = time.Now()

	if order.Status == "" {
		order.Status = "pending"
	}

	// Iniciar transação para garantir atomicidade entre ordem e equipamento
	tx := a.DB.Begin()
	if tx.Error != nil {
		log.Printf("Erro ao iniciar transação para criação de ordem: %v", tx.Error)
		return fmt.Errorf("erro ao iniciar transação: %v", tx.Error)
	}

	// Validar se o equipamento existe antes de criar a ordem
	if order.EquipmentID != 0 {
		var equipment models.Equipment
		if err := tx.First(&equipment, order.EquipmentID).Error; err != nil {
			tx.Rollback()
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Printf("Equipamento ID %d não encontrado ao criar ordem", order.EquipmentID)
				return fmt.Errorf("equipamento com ID %d não encontrado", order.EquipmentID)
			}
			log.Printf("Erro ao buscar equipamento ID %d: %v", order.EquipmentID, err)
			return fmt.Errorf("erro ao validar equipamento: %v", err)
		}

		log.Printf("Equipamento ID %d encontrado: %s (Status atual: %s)", equipment.ID, equipment.Name, equipment.Status)
	}

	// Salvar a ordem no banco de dados
	result := tx.Create(order)
	if result.Error != nil {
		tx.Rollback()
		log.Printf("Erro ao criar ordem de manutenção: %v", result.Error)
		return result.Error
	}

	log.Printf("Ordem de manutenção criada com sucesso: ID=%d", order.ID)

	// Atualizar status do equipamento para "maintenance" se EquipmentID estiver definido
	if order.EquipmentID != 0 {
		updateResult := tx.Model(&models.Equipment{}).
			Where("id = ?", order.EquipmentID).
			Update("status", "maintenance")

		if updateResult.Error != nil {
			tx.Rollback()
			log.Printf("Erro ao atualizar status do equipamento ID %d para 'maintenance': %v", order.EquipmentID, updateResult.Error)
			return fmt.Errorf("erro ao atualizar status do equipamento: %v", updateResult.Error)
		}

		if updateResult.RowsAffected == 0 {
			tx.Rollback()
			log.Printf("Nenhum equipamento foi atualizado com ID %d", order.EquipmentID)
			return fmt.Errorf("equipamento com ID %d não foi encontrado para atualização", order.EquipmentID)
		}

		log.Printf("Status do equipamento ID %d atualizado para 'maintenance' com sucesso", order.EquipmentID)
	}

	// Se a ordem não tiver um prestador de serviço atribuído, tentar atribuir automaticamente
	if order.ServiceProviderID == nil || *order.ServiceProviderID == 0 {
		autoAssignmentService := NewAutoAssignmentService(tx)
		if err := autoAssignmentService.AssignOrderToProvider(order); err != nil {
			log.Printf("Aviso: Não foi possível atribuir automaticamente a ordem %d a um prestador: %v",
				order.ID, err)
			// Continuar mesmo sem atribuição automática
		} else {
			log.Printf("Ordem %d atribuída automaticamente ao prestador %d",
				order.ID, *order.ServiceProviderID)
		}
	}

	// Atribuir permissões automaticamente
	// Inicializar repositório e serviço de atribuição de permissões
	technicianOrderRepo := repository.NewTechnicianOrderRepository(tx)
	technicianOrderService := NewTechnicianOrderService(technicianOrderRepo)
	permissionAssignmentService := NewPermissionAssignmentService(
		tx,
		technicianOrderRepo,
		technicianOrderService,
	)

	// Atribuir permissões para a nova ordem
	if err := permissionAssignmentService.AssignPermissionsForNewOrder(order); err != nil {
		log.Printf("Aviso: Erro ao atribuir permissões automaticamente para ordem %d: %v",
			order.ID, err)
		// Continuar mesmo com erro na atribuição de permissões
	}

	// Commit da transação
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		log.Printf("Erro ao fazer commit da transação: %v", err)
		return err
	}

	// Log final de sucesso incluindo informação sobre equipamento
	if order.EquipmentID != 0 {
		log.Printf("Ordem de manutenção ID %d criada com sucesso e equipamento ID %d atualizado para status 'maintenance'", order.ID, order.EquipmentID)
	} else {
		log.Printf("Ordem de manutenção ID %d criada com sucesso (sem equipamento associado)", order.ID)
	}

	// Se houver um prestador de serviço atribuído, enviar notificação
	if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
		log.Printf("Notificação: Ordem %d atribuída ao prestador de serviço ID %d", order.ID, *order.ServiceProviderID)
		// Aqui seria implementado o código para enviar a notificação
	}

	return nil
}

// UpdateOrder implementa o método UpdateOrder da interface MaintenanceOrderService
func (a *OrderServiceAdapter) UpdateOrder(ctx context.Context, order *models.MaintenanceOrder) error {
	// Iniciar transação para garantir atomicidade
	tx := a.DB.Begin()
	if tx.Error != nil {
		log.Printf("Erro ao iniciar transação: %v", tx.Error)
		return tx.Error
	}

	// Verificar se a ordem existe
	var existingOrder models.MaintenanceOrder
	if err := tx.First(&existingOrder, order.ID).Error; err != nil {
		tx.Rollback()
		log.Printf("Erro ao buscar ordem com ID %d: %v", order.ID, err)
		return err
	}

	// Atualizar campos
	order.UpdatedAt = time.Now()

	// Preservar campos que não devem ser alterados
	if order.CreatedAt.IsZero() {
		order.CreatedAt = existingOrder.CreatedAt
	}

	// Salvar as alterações
	result := tx.Save(order)
	if result.Error != nil {
		tx.Rollback()
		log.Printf("Erro ao atualizar ordem de manutenção %d: %v", order.ID, result.Error)
		return result.Error
	}

	// Verificar se houve mudança no técnico ou prestador de serviço
	technicianChanged := (existingOrder.TechnicianID == nil && order.TechnicianID != nil) ||
		(existingOrder.TechnicianID != nil && order.TechnicianID == nil) ||
		(existingOrder.TechnicianID != nil && order.TechnicianID != nil && *existingOrder.TechnicianID != *order.TechnicianID)

	providerChanged := (existingOrder.ServiceProviderID == nil && order.ServiceProviderID != nil) ||
		(existingOrder.ServiceProviderID != nil && order.ServiceProviderID == nil) ||
		(existingOrder.ServiceProviderID != nil && order.ServiceProviderID != nil && *existingOrder.ServiceProviderID != *order.ServiceProviderID)

	// Se houve mudança no técnico ou prestador, atualizar permissões
	if technicianChanged || providerChanged {
		// Inicializar repositório e serviço de atribuição de permissões
		technicianOrderRepo := repository.NewTechnicianOrderRepository(tx)
		technicianOrderService := NewTechnicianOrderService(technicianOrderRepo)
		permissionAssignmentService := NewPermissionAssignmentService(
			tx,
			technicianOrderRepo,
			technicianOrderService,
		)

		// Atualizar permissões
		if err := permissionAssignmentService.AssignPermissionsForUpdatedOrder(&existingOrder, order); err != nil {
			log.Printf("Aviso: Erro ao atualizar permissões para ordem %d: %v", order.ID, err)
			// Continuar mesmo com erro na atribuição de permissões
		} else {
			log.Printf("Permissões atualizadas com sucesso para ordem %d", order.ID)
		}
	}

	// Commit da transação
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		log.Printf("Erro ao fazer commit da transação: %v", err)
		return err
	}

	log.Printf("Ordem de manutenção atualizada com sucesso: ID=%d", order.ID)
	return nil
}

// GetAllOrders implementa o método GetAllOrders da interface MaintenanceOrderService
func (a *OrderServiceAdapter) GetAllOrders() ([]models.MaintenanceOrder, error) {
	// Buscar ordens reais do banco de dados
	var orders []models.MaintenanceOrder

	// Consultar o banco de dados para obter as ordens reais
	result := a.DB.Find(&orders)
	if result.Error != nil {
		log.Printf("Erro ao buscar ordens no banco de dados: %v", result.Error)
		return nil, result.Error
	}

	log.Printf("Encontradas %d ordens no banco de dados", len(orders))

	// Se não houver ordens, retornar uma lista vazia
	if len(orders) == 0 {
		log.Printf("Nenhuma ordem encontrada no banco de dados")
		return []models.MaintenanceOrder{}, nil
	}

	return orders, nil
}

// UpdateStatus implementa o método UpdateStatus da interface MaintenanceOrderService
func (a *OrderServiceAdapter) UpdateStatus(ctx context.Context, orderID uint, status models.OrderStatus, userID uint, reason string) error {
	// Buscar a ordem no banco de dados
	var order models.MaintenanceOrder
	if err := a.DB.First(&order, orderID).Error; err != nil {
		log.Printf("Erro ao buscar ordem com ID %d: %v", orderID, err)
		return err
	}

	// Verificar se a transição de status é válida
	oldStatus := order.Status
	if !isValidOrderStatusTransition(oldStatus, status, userID) {
		log.Printf("Transição de status inválida: %s -> %s", oldStatus, status)
		return fmt.Errorf("transição de status inválida: %s -> %s", oldStatus, status)
	}

	// Atualizar o status
	order.Status = status
	order.UpdatedAt = time.Now()

	// Atualizar campos específicos com base no status
	if status == "completed" {
		now := time.Now()
		order.CompletionDate = &now
	}

	// Salvar as alterações
	if err := a.DB.Save(&order).Error; err != nil {
		log.Printf("Erro ao atualizar status da ordem %d: %v", orderID, err)
		return err
	}

	// Registrar a interação
	interaction := models.Interaction{
		MaintenanceOrderID: orderID,
		UserID:             userID,
		Message:            fmt.Sprintf("Status alterado de %s para %s. Motivo: %s", oldStatus, status, reason),
		Timestamp:          time.Now(),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}
	a.DB.Create(&interaction)

	log.Printf("Status da ordem %d atualizado com sucesso: %s -> %s", orderID, oldStatus, status)
	return nil
}

// GetOrderDetails implementa o método GetOrderDetails da interface OrderService
func (a *OrderServiceAdapter) GetOrderDetails(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error) {
	// Buscar a ordem no banco de dados
	var order models.MaintenanceOrder

	// Buscar a ordem pelo ID
	result := a.DB.First(&order, orderID)
	if result.Error != nil {
		log.Printf("Erro ao buscar ordem com ID %d: %v", orderID, result.Error)
		return nil, nil, nil, nil, nil, nil, result.Error
	}

	// Buscar a filial
	var branch models.Branch
	if order.BranchID > 0 {
		if err := a.DB.First(&branch, order.BranchID).Error; err != nil {
			log.Printf("Erro ao buscar filial com ID %d: %v", order.BranchID, err)
		}
	}

	// Buscar o equipamento
	var equipment models.Equipment
	if order.EquipmentID > 0 {
		if err := a.DB.First(&equipment, order.EquipmentID).Error; err != nil {
			log.Printf("Erro ao buscar equipamento com ID %d: %v", order.EquipmentID, err)
		}
	}

	// Usuário criador não está mais disponível no modelo
	var createdByUser models.User

	// Técnico não está mais disponível no modelo
	var assignedUser models.User

	// Buscar o prestador de serviço
	var providerUser models.User
	if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
		if err := a.DB.First(&providerUser, *order.ServiceProviderID).Error; err != nil {
			log.Printf("Erro ao buscar prestador com ID %d: %v", *order.ServiceProviderID, err)
		}
	}

	return &order, &branch, &equipment, &createdByUser, &assignedUser, &providerUser, nil
}

// GetOrderDetailsByID implementa o método GetOrderDetailsByID da interface OrderService
func (a *OrderServiceAdapter) GetOrderDetailsByID(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error) {
	// Este método é um alias para GetOrderDetails
	return a.GetOrderDetails(orderID)
}

// GetCostsByOrderID implementa o método GetCostsByOrderID da interface OrderService
func (a *OrderServiceAdapter) GetCostsByOrderID(orderID uint) ([]models.CostItem, error) {
	// Buscar os custos no banco de dados
	var costs []models.CostItem

	// Buscar os custos pelo ID da ordem
	result := a.DB.Where("order_id = ?", orderID).Find(&costs)
	if result.Error != nil {
		log.Printf("Erro ao buscar custos para ordem com ID %d: %v", orderID, result.Error)
		return nil, result.Error
	}

	log.Printf("Encontrados %d custos para a ordem %d", len(costs), orderID)
	return costs, nil
}

// AddCost implementa o método AddCost da interface OrderService
func (a *OrderServiceAdapter) AddCost(orderID uint, userID uint, cost models.CostItem) (*models.CostItem, error) {
	// Configurar os campos da estrutura de custo
	cost.MaintenanceOrderID = orderID
	cost.AddedByUserID = userID
	cost.CreatedAt = time.Now()
	cost.UpdatedAt = time.Now()

	// Calcular o custo total do item
	cost.TotalItemCost = cost.Quantity * cost.UnitPrice

	// Salvar o custo no banco de dados
	result := a.DB.Create(&cost)
	if result.Error != nil {
		log.Printf("Erro ao adicionar custo para ordem %d: %v", orderID, result.Error)
		return nil, result.Error
	}

	// Atualizar o custo total da ordem
	var order models.MaintenanceOrder
	if err := a.DB.First(&order, orderID).Error; err == nil {
		// Calcular o novo custo total
		var totalCost float64
		a.DB.Model(&models.CostItem{}).Where("maintenance_order_id = ?", orderID).Select("SUM(total_item_cost)").Row().Scan(&totalCost)

		// Atualizar a ordem
		a.DB.Model(&order).Update("total_cost", totalCost)
	}

	log.Printf("Custo adicionado com sucesso para ordem %d: %v", orderID, cost.TotalItemCost)
	return &cost, nil
}

// AssignProvider implementa o método AssignProvider da interface OrderService
func (a *OrderServiceAdapter) AssignProvider(orderID uint, userID uint, providerID uint) (*models.MaintenanceOrder, error) {
	// Iniciar transação para garantir atomicidade
	tx := a.DB.Begin()
	if tx.Error != nil {
		log.Printf("Erro ao iniciar transação: %v", tx.Error)
		return nil, tx.Error
	}

	// Buscar a ordem no banco de dados
	var order models.MaintenanceOrder
	if err := tx.First(&order, orderID).Error; err != nil {
		tx.Rollback()
		log.Printf("Erro ao buscar ordem com ID %d: %v", orderID, err)
		return nil, err
	}

	// Verificar se o usuário tem permissão para atribuir um prestador
	// (Isso seria implementado com base nas regras de negócio)

	// Guardar o prestador anterior para verificar se houve mudança
	oldProviderID := order.ServiceProviderID

	// Atualizar o prestador atribuído
	providerIDPtr := &providerID
	order.ServiceProviderID = providerIDPtr
	order.UpdatedAt = time.Now()

	// Salvar as alterações
	if err := tx.Save(&order).Error; err != nil {
		tx.Rollback()
		log.Printf("Erro ao atribuir prestador %d à ordem %d: %v", providerID, orderID, err)
		return nil, err
	}

	// Verificar se houve mudança no prestador
	providerChanged := (oldProviderID == nil && providerIDPtr != nil) ||
		(oldProviderID != nil && providerIDPtr == nil) ||
		(oldProviderID != nil && providerIDPtr != nil && *oldProviderID != *providerIDPtr)

	// Se houve mudança no prestador, atualizar permissões
	if providerChanged {
		// Inicializar repositório e serviço de atribuição de permissões
		technicianOrderRepo := repository.NewTechnicianOrderRepository(tx)
		technicianOrderService := NewTechnicianOrderService(technicianOrderRepo)
		permissionAssignmentService := NewPermissionAssignmentService(
			tx,
			technicianOrderRepo,
			technicianOrderService,
		)

		// Criar uma cópia da ordem antes da mudança para passar ao serviço
		oldOrder := models.MaintenanceOrder{
			ID:                order.ID,
			ServiceProviderID: oldProviderID,
			TechnicianID:      order.TechnicianID,
		}

		// Atualizar permissões
		if err := permissionAssignmentService.AssignPermissionsForUpdatedOrder(&oldOrder, &order); err != nil {
			log.Printf("Aviso: Erro ao atualizar permissões para ordem %d após atribuição de prestador: %v",
				orderID, err)
			// Continuar mesmo com erro na atribuição de permissões
		} else {
			log.Printf("Permissões atualizadas com sucesso para ordem %d após atribuição de prestador %d",
				orderID, providerID)
		}
	}

	// Registrar a interação
	interaction := models.Interaction{
		MaintenanceOrderID: orderID,
		UserID:             userID,
		Message:            "Prestador atribuído à ordem",
		Timestamp:          time.Now(),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}
	tx.Create(&interaction)

	// Commit da transação
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		log.Printf("Erro ao fazer commit da transação: %v", err)
		return nil, err
	}

	log.Printf("Prestador %d atribuído com sucesso à ordem %d", providerID, orderID)
	return &order, nil
}

// SubmitForApproval implementa o método SubmitForApproval da interface OrderService
func (a *OrderServiceAdapter) SubmitForApproval(orderID uint, userID uint) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// Approve implementa o método Approve da interface OrderService
func (a *OrderServiceAdapter) Approve(orderID uint, userID uint) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// Reject implementa o método Reject da interface OrderService
func (a *OrderServiceAdapter) Reject(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// Cancel implementa o método Cancel da interface OrderService
func (a *OrderServiceAdapter) Cancel(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// AddInteraction implementa o método AddInteraction da interface OrderService
func (a *OrderServiceAdapter) AddInteraction(orderID uint, userID uint, message string) (*models.Interaction, error) {
	// Verificar se a ordem existe
	var order models.MaintenanceOrder
	if err := a.DB.First(&order, orderID).Error; err != nil {
		log.Printf("Erro ao buscar ordem com ID %d: %v", orderID, err)
		return nil, err
	}

	// Criar a interação
	interaction := models.Interaction{
		MaintenanceOrderID: orderID,
		UserID:             userID,
		Message:            message,
		Timestamp:          time.Now(),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Salvar a interação no banco de dados
	if err := a.DB.Create(&interaction).Error; err != nil {
		log.Printf("Erro ao adicionar interação para ordem %d: %v", orderID, err)
		return nil, err
	}

	// Atualizar a data de atualização da ordem
	a.DB.Model(&order).Update("updated_at", time.Now())

	log.Printf("Interação adicionada com sucesso para ordem %d", orderID)
	return &interaction, nil
}

// UploadInvoice implementa o método UploadInvoice da interface OrderService
func (a *OrderServiceAdapter) UploadInvoice(orderID uint, userID uint, invoiceData models.Invoice, attachment models.Attachment) (*models.Invoice, error) {
	// Verificar se a ordem existe
	var order models.MaintenanceOrder
	if err := a.DB.First(&order, orderID).Error; err != nil {
		log.Printf("Erro ao buscar ordem com ID %d: %v", orderID, err)
		return nil, err
	}

	// Configurar campos da nota fiscal
	invoiceData.Description = "Nota fiscal para ordem #" + fmt.Sprintf("%d", orderID)
	invoiceData.Date = time.Now()
	invoiceData.IssueDate = time.Now()
	invoiceData.Status = models.InvoiceStatusPending
	invoiceData.CreatedAt = time.Now()
	invoiceData.UpdatedAt = time.Now()

	// Salvar a nota fiscal
	if err := a.DB.Create(&invoiceData).Error; err != nil {
		log.Printf("Erro ao salvar nota fiscal para ordem %d: %v", orderID, err)
		return nil, err
	}

	// Salvar o anexo, se houver
	if attachment.File != "" {
		newAttachment := models.Attachment{
			FilePath:           attachment.File,
			OriginalName:       "nota_fiscal.pdf",
			FileName:           attachment.File,
			FileSize:           0, // TODO: Obter tamanho real do arquivo
			MimeType:           "application/pdf",
			FileHash:           "", // TODO: Calcular hash do arquivo
			Type:               models.AttachmentTypeDocument,
			Category:           models.AttachmentCategory(models.CategoryInvoice),
			Description:        "Nota fiscal para ordem #" + fmt.Sprintf("%d", orderID),
			EntityType:         "order",
			EntityID:           orderID,
			UploadedByID:       userID,
			UploadedByName:     "Sistema",
			MaintenanceOrderID: orderID, // Compatibilidade
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		}

		if err := a.DB.Create(&newAttachment).Error; err != nil {
			log.Printf("Erro ao salvar anexo para ordem %d: %v", orderID, err)
		}
	}

	// Registrar a interação
	interaction := models.Interaction{
		MaintenanceOrderID: orderID,
		UserID:             userID,
		Message:            "Nota fiscal enviada",
		Timestamp:          time.Now(),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}
	a.DB.Create(&interaction)

	log.Printf("Nota fiscal enviada com sucesso para ordem %d", orderID)
	return &invoiceData, nil
}

// GetOrderCosts implementa o método GetOrderCosts da interface OrderServiceInterface
func (a *OrderServiceAdapter) GetOrderCosts(orderID uint) ([]models.CostItem, error) {
	// Este método é um alias para GetCostsByOrderID
	return a.GetCostsByOrderID(orderID)
}

// GetOrdersWithPagination implementa o método GetOrdersWithPagination da interface OrderServiceInterface
func (a *OrderServiceAdapter) GetOrdersWithPagination(offset, limit int, status, branchID, startDate, endDate string) ([]models.MaintenanceOrder, int, error) {
	var orders []models.MaintenanceOrder
	var total int64

	query := a.DB.Model(&models.MaintenanceOrder{})

	// Aplicar filtros
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	}
	if startDate != "" {
		query = query.Where("created_at >= ?", startDate)
	}
	if endDate != "" {
		query = query.Where("created_at <= ?", endDate)
	}

	// Contar total de registros
	query.Count(&total)

	// Aplicar paginação
	result := query.Offset(offset).Limit(limit).Find(&orders)
	if result.Error != nil {
		log.Printf("Erro ao buscar ordens com paginação: %v", result.Error)
		return nil, 0, result.Error
	}

	return orders, int(total), nil
}

// GetOrderCountsByStatus implementa o método GetOrderCountsByStatus da interface OrderServiceInterface
func (a *OrderServiceAdapter) GetOrderCountsByStatus() (pendingCount, inProgressCount, completedCount int) {
	var pending, inProgress, completed int64

	a.DB.Model(&models.MaintenanceOrder{}).Where("status = ?", "pending").Count(&pending)
	a.DB.Model(&models.MaintenanceOrder{}).Where("status = ?", "in_progress").Count(&inProgress)
	a.DB.Model(&models.MaintenanceOrder{}).Where("status = ?", "completed").Count(&completed)

	return int(pending), int(inProgress), int(completed)
}

// GetOrderInteractions implementa o método GetOrderInteractions da interface OrderServiceInterface
func (a *OrderServiceAdapter) GetOrderInteractions(orderID, offset, limit uint) ([]models.Interaction, error) {
	var interactions []models.Interaction

	result := a.DB.Where("maintenance_order_id = ?", orderID).
		Offset(int(offset)).
		Limit(int(limit)).
		Order("created_at DESC").
		Find(&interactions)

	if result.Error != nil {
		log.Printf("Erro ao buscar interações para ordem %d: %v", orderID, result.Error)
		return nil, result.Error
	}

	return interactions, nil
}

// CreateOrderAdapted implementa o método CreateOrderAdapted da interface OrderServiceInterface
func (a *OrderServiceAdapter) CreateOrderAdapted(ctx context.Context, order *models.MaintenanceOrderAdapted) error {
	// Validar a ordem
	if order.Problem == "" {
		return fmt.Errorf("problema é obrigatório")
	}

	// Configurar campos padrão
	if order.CreatedAt.IsZero() {
		order.CreatedAt = time.Now()
	}
	order.UpdatedAt = time.Now()

	if order.Status == "" {
		order.Status = "pending"
	}

	// Salvar a ordem no banco de dados
	result := a.DB.Create(order)
	if result.Error != nil {
		log.Printf("Erro ao criar ordem de manutenção adaptada: %v", result.Error)
		return result.Error
	}

	log.Printf("Ordem de manutenção adaptada criada com sucesso: ID=%d", order.ID)
	return nil
}

// UpdateOrderAdapted implementa o método UpdateOrderAdapted da interface OrderServiceInterface
func (a *OrderServiceAdapter) UpdateOrderAdapted(ctx context.Context, order *models.MaintenanceOrderAdapted) error {
	// Verificar se a ordem existe
	var existingOrder models.MaintenanceOrderAdapted
	if err := a.DB.First(&existingOrder, order.ID).Error; err != nil {
		log.Printf("Erro ao buscar ordem adaptada com ID %d: %v", order.ID, err)
		return err
	}

	// Atualizar campos
	order.UpdatedAt = time.Now()

	// Preservar campos que não devem ser alterados
	if order.CreatedAt.IsZero() {
		order.CreatedAt = existingOrder.CreatedAt
	}

	// Salvar as alterações
	result := a.DB.Save(order)
	if result.Error != nil {
		log.Printf("Erro ao atualizar ordem de manutenção adaptada %d: %v", order.ID, result.Error)
		return result.Error
	}

	log.Printf("Ordem de manutenção adaptada atualizada com sucesso: ID=%d", order.ID)
	return nil
}

// DeleteOrder implementa o método DeleteOrder da interface OrderServiceInterface
func (a *OrderServiceAdapter) DeleteOrder(ctx context.Context, id models.ID) error {
	// Converter ID para uint
	numericID, err := models.ConvertToNumericID(id)
	if err != nil {
		return fmt.Errorf("ID inválido: %v", err)
	}

	// Verificar se a ordem existe
	var order models.MaintenanceOrder
	if err := a.DB.First(&order, numericID.Value).Error; err != nil {
		log.Printf("Erro ao buscar ordem com ID %d: %v", numericID.Value, err)
		return err
	}

	// Remover a ordem (soft delete)
	result := a.DB.Delete(&order)
	if result.Error != nil {
		log.Printf("Erro ao remover ordem %d: %v", numericID.Value, result.Error)
		return result.Error
	}

	log.Printf("Ordem %d removida com sucesso", numericID.Value)
	return nil
}

// ListOrders implementa o método ListOrders da interface OrderServiceInterface
func (a *OrderServiceAdapter) ListOrders(ctx context.Context, page, pageSize int) ([]models.MaintenanceOrderAdapted, int, error) {
	var orders []models.MaintenanceOrderAdapted
	var total int64

	// Validar parâmetros
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Calcular offset
	offset := (page - 1) * pageSize

	// Contar total de registros
	a.DB.Model(&models.MaintenanceOrderAdapted{}).Count(&total)

	// Buscar ordens com paginação
	result := a.DB.Offset(offset).Limit(pageSize).Find(&orders)
	if result.Error != nil {
		log.Printf("Erro ao buscar ordens adaptadas: %v", result.Error)
		return nil, 0, result.Error
	}

	return orders, int(total), nil
}
