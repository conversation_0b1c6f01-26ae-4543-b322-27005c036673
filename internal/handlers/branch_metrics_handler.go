package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"tradicao/internal/services"
)

// BranchMetricsHandler handler para APIs de métricas por filial
type BranchMetricsHandler struct {
	metricsService *services.BranchMetricsService
}

// NewBranchMetricsHandler cria uma nova instância do handler
func NewBranchMetricsHandler(metricsService *services.BranchMetricsService) *BranchMetricsHandler {
	return &BranchMetricsHandler{
		metricsService: metricsService,
	}
}

// GetBranchMetrics retorna métricas de uma filial específica
// @Summary Obter métricas de uma filial
// @Description Retorna métricas detalhadas de uma filial específica
// @Tags Métricas
// @Accept json
// @Produce json
// @Param id path int true "ID da filial"
// @Success 200 {object} services.BranchMetrics
// @Failure 400 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/branches/{id}/metrics [get]
func (h *BranchMetricsHandler) GetBranchMetrics(c *gin.Context) {
	// Obter ID da filial da URL
	branchIDStr := c.Param("id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID da filial inválido",
			"code":  "INVALID_BRANCH_ID",
		})
		return
	}

	// Verificar se o usuário tem acesso à filial (se o filtro estiver ativo)
	if branchFilter, exists := c.Get("branchFilter"); exists && branchFilter == "enabled" {
		if userBranchID, exists := c.Get("userBranchID"); exists {
			if userBranchID != uint(branchID) {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "Acesso negado à filial especificada",
					"code":  "BRANCH_ACCESS_DENIED",
				})
				return
			}
		}
	}

	// Buscar métricas
	metrics, err := h.metricsService.GetBranchMetrics(uint(branchID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar métricas da filial",
			"code":  "METRICS_FETCH_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetAllBranchesMetrics retorna métricas de todas as filiais
// @Summary Obter métricas de todas as filiais
// @Description Retorna métricas de todas as filiais (apenas para administradores)
// @Tags Métricas
// @Accept json
// @Produce json
// @Success 200 {array} services.BranchMetrics
// @Failure 403 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/branches/metrics [get]
func (h *BranchMetricsHandler) GetAllBranchesMetrics(c *gin.Context) {
	// Verificar se o usuário tem permissão para ver todas as filiais
	if branchFilter, exists := c.Get("branchFilter"); exists && branchFilter == "enabled" {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Acesso negado. Apenas administradores podem ver métricas de todas as filiais",
			"code":  "ADMIN_REQUIRED",
		})
		return
	}

	// Buscar métricas de todas as filiais
	metrics, err := h.metricsService.GetAllBranchesMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar métricas das filiais",
			"code":  "METRICS_FETCH_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"branches": metrics,
		"total":    len(metrics),
	})
}

// GetEquipmentTypeMetrics retorna métricas por tipo de equipamento
// @Summary Obter métricas por tipo de equipamento
// @Description Retorna métricas detalhadas por tipo de equipamento para uma filial
// @Tags Métricas
// @Accept json
// @Produce json
// @Param id path int true "ID da filial"
// @Success 200 {array} services.EquipmentTypeMetrics
// @Failure 400 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/branches/{id}/equipment-metrics [get]
func (h *BranchMetricsHandler) GetEquipmentTypeMetrics(c *gin.Context) {
	// Obter ID da filial da URL
	branchIDStr := c.Param("id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID da filial inválido",
			"code":  "INVALID_BRANCH_ID",
		})
		return
	}

	// Verificar acesso à filial
	if branchFilter, exists := c.Get("branchFilter"); exists && branchFilter == "enabled" {
		if userBranchID, exists := c.Get("userBranchID"); exists {
			if userBranchID != uint(branchID) {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "Acesso negado à filial especificada",
					"code":  "BRANCH_ACCESS_DENIED",
				})
				return
			}
		}
	}

	// Buscar métricas por tipo de equipamento
	metrics, err := h.metricsService.GetEquipmentTypeMetrics(uint(branchID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar métricas de equipamentos",
			"code":  "EQUIPMENT_METRICS_FETCH_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"branch_id":         branchID,
		"equipment_metrics": metrics,
		"total":             len(metrics),
	})
}

// GetTopProblematicEquipment retorna os equipamentos mais problemáticos
// @Summary Obter equipamentos mais problemáticos
// @Description Retorna os tipos de equipamento que mais geram ordens de manutenção
// @Tags Métricas
// @Accept json
// @Produce json
// @Param limit query int false "Limite de resultados (padrão: 10)"
// @Success 200 {array} services.EquipmentTypeMetrics
// @Failure 403 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/equipment/problematic [get]
func (h *BranchMetricsHandler) GetTopProblematicEquipment(c *gin.Context) {
	// Verificar se o usuário tem permissão para ver dados globais
	if branchFilter, exists := c.Get("branchFilter"); exists && branchFilter == "enabled" {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Acesso negado. Apenas administradores podem ver dados globais",
			"code":  "ADMIN_REQUIRED",
		})
		return
	}

	// Obter limite da query string
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 10
	}

	// Buscar equipamentos mais problemáticos
	metrics, err := h.metricsService.GetTopProblematicEquipmentTypes(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar equipamentos problemáticos",
			"code":  "PROBLEMATIC_EQUIPMENT_FETCH_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"problematic_equipment": metrics,
		"total":                 len(metrics),
		"limit":                 limit,
	})
}

// GetMyBranchMetrics retorna métricas da filial do usuário logado
// @Summary Obter métricas da minha filial
// @Description Retorna métricas da filial associada ao usuário logado
// @Tags Métricas
// @Accept json
// @Produce json
// @Success 200 {object} services.BranchMetrics
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/my-branch/metrics [get]
func (h *BranchMetricsHandler) GetMyBranchMetrics(c *gin.Context) {
	// Obter ID da filial do usuário do contexto
	userBranchID, exists := c.Get("userBranchID")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Usuário não está associado a nenhuma filial",
			"code":  "NO_BRANCH_ASSOCIATED",
		})
		return
	}

	branchID, ok := userBranchID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro interno: ID da filial inválido",
			"code":  "INTERNAL_ERROR",
		})
		return
	}

	// Buscar métricas da filial do usuário
	metrics, err := h.metricsService.GetBranchMetrics(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar métricas da sua filial",
			"code":  "METRICS_FETCH_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetMyBranchEquipmentMetrics retorna métricas de equipamentos da filial do usuário
// @Summary Obter métricas de equipamentos da minha filial
// @Description Retorna métricas por tipo de equipamento da filial do usuário logado
// @Tags Métricas
// @Accept json
// @Produce json
// @Success 200 {array} services.EquipmentTypeMetrics
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/my-branch/equipment-metrics [get]
func (h *BranchMetricsHandler) GetMyBranchEquipmentMetrics(c *gin.Context) {
	// Obter ID da filial do usuário do contexto
	userBranchID, exists := c.Get("userBranchID")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Usuário não está associado a nenhuma filial",
			"code":  "NO_BRANCH_ASSOCIATED",
		})
		return
	}

	branchID, ok := userBranchID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro interno: ID da filial inválido",
			"code":  "INTERNAL_ERROR",
		})
		return
	}

	// Buscar métricas de equipamentos da filial do usuário
	metrics, err := h.metricsService.GetEquipmentTypeMetrics(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar métricas de equipamentos da sua filial",
			"code":  "EQUIPMENT_METRICS_FETCH_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"branch_id":         branchID,
		"equipment_metrics": metrics,
		"total":             len(metrics),
	})
}
