package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/database"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UnifiedOrderHandler - Handler unificado para todas as operações de ordens
type UnifiedOrderHandler struct {
	repo *repository.MaintenanceOrderRepository
}

// NewUnifiedOrderHandler cria uma nova instância do handler unificado
func NewUnifiedOrderHandler() *UnifiedOrderHandler {
	db := database.GetGormDB()
	if db == nil {
		log.Fatal("Erro ao conectar ao banco de dados")
	}

	return &UnifiedOrderHandler{
		repo: repository.NewMaintenanceOrderRepository(db),
	}
}

// StandardResponse - Resposta padronizada para todas as APIs
type StandardResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Meta    interface{} `json:"meta,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// OrderFilters - Filtros padronizados para ordens
type OrderFilters struct {
	Status       string `json:"status"`
	BranchID     uint   `json:"branch_id"`
	TechnicianID uint   `json:"technician_id"`
	ProviderID   uint   `json:"provider_id"`
	StartDate    string `json:"start_date"`
	EndDate      string `json:"end_date"`
	Priority     string `json:"priority"`
	Search       string `json:"search"`
	ExcludeTest  bool   `json:"exclude_test"`
}

// PaginationMeta - Metadados de paginação
type PaginationMeta struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// ListOrders - Endpoint unificado para listar ordens
// GET /api/orders
func (h *UnifiedOrderHandler) ListOrders(c *gin.Context) {
	// Extrair parâmetros de paginação
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	// Validar paginação
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Extrair filtros
	filters := h.extractFilters(c)

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	// Buscar ordens com filtros otimizados
	orders, total, err := h.getOrdersWithOptimizedQuery(c, filters, userID, userRole, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordens",
			Error:   err.Error(),
		})
		return
	}

	// Calcular metadados de paginação
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	meta := PaginationMeta{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontradas %d ordens", total),
		Data:    orders,
		Meta:    meta,
	})
}

// GetOrder - Endpoint unificado para obter uma ordem específica
// GET /api/orders/:id
func (h *UnifiedOrderHandler) GetOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "ID inválido",
			Error:   "ID deve ser um número válido",
		})
		return
	}

	// Verificar se é a ordem problemática #18
	if id == 18 {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Message: "Ordem não disponível",
			Error:   "Esta ordem não está disponível para visualização",
		})
		return
	}

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	// Buscar ordem com dados relacionados
	order, err := h.getOrderWithRelations(c, uint(id), userID, userRole)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, StandardResponse{
				Success: false,
				Message: "Ordem não encontrada",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordem",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Ordem encontrada",
		Data:    order,
	})
}

// GetCalendarOrders - Endpoint unificado para ordens do calendário
// GET /api/orders/calendar
func (h *UnifiedOrderHandler) GetCalendarOrders(c *gin.Context) {
	// Obter parâmetros de data
	monthStr := c.DefaultQuery("month", strconv.Itoa(int(time.Now().Month())))
	yearStr := c.DefaultQuery("year", strconv.Itoa(time.Now().Year()))

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		month = int(time.Now().Month())
	}

	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 2000 || year > 2100 {
		year = time.Now().Year()
	}

	// Extrair filtros
	filters := h.extractFilters(c)

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	// Calcular range de datas
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

	// Buscar ordens do mês
	orders, err := h.getCalendarOrders(c, startDate, endDate, filters, userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordens do calendário",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontradas %d ordens para %s/%d", len(orders), monthStr, year),
		Data:    orders,
	})
}

// GetTechnicianOrders - Endpoint unificado para ordens do técnico
// GET /api/orders/technician
func (h *UnifiedOrderHandler) GetTechnicianOrders(c *gin.Context) {
	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	if userID == 0 {
		c.JSON(http.StatusUnauthorized, StandardResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Extrair filtros
	filters := h.extractFilters(c)

	// Filtro específico por data se fornecido
	dateStr := c.Query("date")

	// Buscar ordens do técnico
	orders, err := h.getTechnicianOrders(c, userID, userRole, dateStr, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordens do técnico",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontradas %d ordens", len(orders)),
		Data:    orders,
	})
}

// Métodos auxiliares

// extractFilters extrai filtros padronizados da requisição
func (h *UnifiedOrderHandler) extractFilters(c *gin.Context) OrderFilters {
	filters := OrderFilters{
		ExcludeTest: true, // Sempre excluir ordens de teste por padrão
	}

	// Status
	if status := c.Query("status"); status != "" && status != "all" {
		filters.Status = status
	}

	// Branch ID
	if branchIDStr := c.Query("branch_id"); branchIDStr != "" && branchIDStr != "all" {
		if branchID, err := strconv.ParseUint(branchIDStr, 10, 32); err == nil {
			filters.BranchID = uint(branchID)
		}
	}

	// Technician ID
	if techIDStr := c.Query("technician_id"); techIDStr != "" {
		if techID, err := strconv.ParseUint(techIDStr, 10, 32); err == nil {
			filters.TechnicianID = uint(techID)
		}
	}

	// Provider ID
	if providerIDStr := c.Query("provider_id"); providerIDStr != "" {
		if providerID, err := strconv.ParseUint(providerIDStr, 10, 32); err == nil {
			filters.ProviderID = uint(providerID)
		}
	}

	// Datas
	filters.StartDate = c.Query("start_date")
	filters.EndDate = c.Query("end_date")

	// Prioridade
	if priority := c.Query("priority"); priority != "" && priority != "all" {
		filters.Priority = priority
	}

	// Busca
	filters.Search = c.Query("search")

	return filters
}

// getUserInfo obtém informações do usuário do contexto
func (h *UnifiedOrderHandler) getUserInfo(c *gin.Context) (uint, string) {
	var userID uint
	var userRole string

	// Tentar obter userID de diferentes chaves possíveis
	if id, exists := c.Get("userID"); exists {
		if idUint, ok := id.(uint); ok {
			userID = idUint
		} else if idStr, ok := id.(string); ok {
			if parsed, err := strconv.ParseUint(idStr, 10, 32); err == nil {
				userID = uint(parsed)
			}
		}
	}

	// Tentar obter userRole
	if role, exists := c.Get("userRole"); exists {
		if roleStr, ok := role.(string); ok {
			userRole = roleStr
		}
	}

	return userID, userRole
}

// getOrdersWithOptimizedQuery busca ordens com query otimizada
func (h *UnifiedOrderHandler) getOrdersWithOptimizedQuery(ctx context.Context, filters OrderFilters, userID uint, userRole string, page, pageSize int) ([]models.MaintenanceOrderDetailed, int64, error) {
	// Usar o repositório existente com filtros padronizados
	repoFilters := make(map[string]interface{})

	// Aplicar filtros
	if filters.Status != "" {
		repoFilters["status"] = filters.Status
	}
	if filters.BranchID > 0 {
		repoFilters["branch_id"] = filters.BranchID
	}
	if filters.StartDate != "" {
		repoFilters["start_date"] = filters.StartDate
	}
	if filters.EndDate != "" {
		repoFilters["end_date"] = filters.EndDate
	}
	if filters.Priority != "" {
		repoFilters["priority"] = filters.Priority
	}
	if filters.Search != "" {
		repoFilters["search"] = filters.Search
	}

	// Buscar ordens usando o repositório (corrigindo assinatura)
	orders, total, err := h.repo.GetAll(ctx, repoFilters, int64(userID), userRole, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	return orders, int64(total), nil
}

// getOrderWithRelations busca uma ordem com todos os relacionamentos
func (h *UnifiedOrderHandler) getOrderWithRelations(ctx context.Context, orderID, userID uint, userRole string) (*models.MaintenanceOrderDetailed, error) {
	// Usar o repositório para buscar ordem específica (corrigindo assinatura)
	order, err := h.repo.GetByID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	// Converter MaintenanceOrder para MaintenanceOrderDetailed
	detailed := &models.MaintenanceOrderDetailed{
		ID:            order.ID,
		OrderNumber:   order.Number,
		Title:         order.Title,
		Description:   order.Description,
		Status:        string(order.Status),
		Priority:      string(order.Priority),
		StationID:     order.BranchID,
		StationName:   order.BranchName,
		CreatedBy:     order.CreatedByUserID,
		CreatedByName: order.CreatedByName,
		AssignedTo:    order.TechnicianID,
		StartDate:     &order.OpenDate,
		CompletedDate: order.CompletionDate,
		CreatedAt:     order.CreatedAt,
		UpdatedAt:     order.UpdatedAt,
		TotalCost:     order.ActualCost,
	}

	return detailed, nil
}

// getCalendarOrders busca ordens para o calendário
func (h *UnifiedOrderHandler) getCalendarOrders(ctx context.Context, startDate, endDate time.Time, filters OrderFilters, userID uint, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	// Criar filtros específicos para calendário
	repoFilters := map[string]interface{}{
		"start_date": startDate.Format("2006-01-02"),
		"end_date":   endDate.Format("2006-01-02"),
	}

	// Aplicar filtros adicionais
	if filters.BranchID > 0 {
		repoFilters["branch_id"] = filters.BranchID
	}
	if filters.Status != "" {
		repoFilters["status"] = filters.Status
	}

	// Buscar ordens do período (corrigindo assinatura)
	orders, _, err := h.repo.GetAll(ctx, repoFilters, int64(userID), userRole, 1, 1000) // Limite alto para calendário
	if err != nil {
		return nil, err
	}

	return orders, nil
}

// getTechnicianOrders busca ordens específicas do técnico
func (h *UnifiedOrderHandler) getTechnicianOrders(ctx context.Context, userID uint, userRole, dateFilter string, filters OrderFilters) ([]models.MaintenanceOrderDetailed, error) {
	// Criar filtros específicos para técnico
	repoFilters := map[string]interface{}{
		"technician_id": userID,
	}

	// Aplicar filtro de data se fornecido
	if dateFilter != "" {
		repoFilters["date"] = dateFilter
	}

	// Aplicar outros filtros
	if filters.Status != "" {
		repoFilters["status"] = filters.Status
	}

	// Buscar ordens do técnico (corrigindo assinatura)
	orders, _, err := h.repo.GetAll(ctx, repoFilters, int64(userID), userRole, 1, 200) // Limite razoável
	if err != nil {
		return nil, err
	}

	return orders, nil
}
