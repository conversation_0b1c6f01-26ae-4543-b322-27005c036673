package handlers

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// AttachmentHandler handler para anexos
type AttachmentHandler struct {
	service services.AttachmentService
}

// NewAttachmentHandler cria uma nova instância do handler
func NewAttachmentHandler(service services.AttachmentService) *AttachmentHandler {
	return &AttachmentHandler{
		service: service,
	}
}

// UploadFile faz upload de um arquivo
func (h *AttachmentHandler) UploadFile(c *gin.Context) {
	// Parse do formulário multipart
	if err := c.Request.ParseMultipartForm(50 << 20); err != nil { // 50MB max
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Erro ao processar formulário",
			"details": err.<PERSON>rror(),
		})
		return
	}

	// Obter arquivo
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Arquivo não encontrado",
			"details": err.Error(),
		})
		return
	}
	defer file.Close()

	// Parse dos dados da requisição
	var request models.AttachmentUploadRequest
	request.EntityType = c.PostForm("entity_type")
	request.Category = models.AttachmentCategory(c.PostForm("category"))
	request.Title = c.PostForm("title")
	request.Description = c.PostForm("description")
	request.Tags = c.PostForm("tags")
	request.IsPublic = c.PostForm("is_public") == "true"

	// Parse do EntityID
	entityIDStr := c.PostForm("entity_id")
	if entityIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "entity_id é obrigatório",
		})
		return
	}

	entityID, err := strconv.ParseUint(entityIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "entity_id inválido",
		})
		return
	}
	request.EntityID = uint(entityID)

	// Validar campos obrigatórios
	if request.EntityType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "entity_type é obrigatório",
		})
		return
	}

	if request.Category == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "category é obrigatório",
		})
		return
	}

	// Obter informações do usuário (assumindo que está no contexto)
	userID := uint(1)     // TODO: Obter do contexto de autenticação
	userName := "Sistema" // TODO: Obter do contexto de autenticação
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// Fazer upload
	response, err := h.service.UploadFile(file, header, &request, userID, userName, ipAddress, userAgent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao fazer upload do arquivo",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Arquivo enviado com sucesso",
		"data":    response,
	})
}

// GetAttachments lista anexos por entidade
func (h *AttachmentHandler) GetAttachments(c *gin.Context) {
	entityType := c.Query("entity_type")
	entityIDStr := c.Query("entity_id")

	if entityType == "" || entityIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "entity_type e entity_id são obrigatórios",
		})
		return
	}

	entityID, err := strconv.ParseUint(entityIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "entity_id inválido",
		})
		return
	}

	// Criar filtros opcionais
	filter := &models.AttachmentFilter{}

	if typeParam := c.Query("type"); typeParam != "" {
		filter.Type = models.AttachmentType(typeParam)
	}

	if categoryParam := c.Query("category"); categoryParam != "" {
		filter.Category = models.AttachmentCategory(categoryParam)
	}

	if tagsParam := c.Query("tags"); tagsParam != "" {
		filter.Tags = tagsParam
	}

	if onlyLatestParam := c.Query("only_latest"); onlyLatestParam == "true" {
		filter.OnlyLatest = true
	}

	// Buscar anexos
	attachments, err := h.service.GetByEntity(entityType, uint(entityID), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao buscar anexos",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  attachments,
		"count": len(attachments),
	})
}

// GetAttachment busca um anexo específico
func (h *AttachmentHandler) GetAttachment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	attachment, err := h.service.GetByID(uint(id))
	if err != nil {
		if strings.Contains(err.Error(), "não encontrado") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Anexo não encontrado",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao buscar anexo",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": attachment,
	})
}

// DownloadFile faz download de um arquivo
func (h *AttachmentHandler) DownloadFile(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	// Buscar anexo
	attachment, err := h.service.GetByID(uint(id))
	if err != nil {
		if strings.Contains(err.Error(), "não encontrado") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Anexo não encontrado",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao buscar anexo",
			"details": err.Error(),
		})
		return
	}

	// TODO: Verificar permissões de acesso

	// Verificar se arquivo existe no disco
	filePath := fmt.Sprintf("uploads/attachments/%s", attachment.FileName)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Arquivo não encontrado no disco",
		})
		return
	}

	// Definir headers para download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", attachment.OriginalName))
	c.Header("Content-Type", attachment.MimeType)

	// Servir arquivo
	c.File(filePath)
}

// GetThumbnail retorna o thumbnail de uma imagem
func (h *AttachmentHandler) GetThumbnail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	// Buscar anexo
	attachment, err := h.service.GetByID(uint(id))
	if err != nil {
		if strings.Contains(err.Error(), "não encontrado") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Anexo não encontrado",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao buscar anexo",
			"details": err.Error(),
		})
		return
	}

	if !attachment.HasThumbnail {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Thumbnail não disponível",
		})
		return
	}

	// Construir caminho do thumbnail
	ext := filepath.Ext(attachment.FileName)
	name := strings.TrimSuffix(attachment.FileName, ext)
	thumbnailPath := fmt.Sprintf("uploads/attachments/thumbnails/%s_thumb%s", name, ext)

	// Verificar se thumbnail existe
	if _, err := os.Stat(thumbnailPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Thumbnail não encontrado no disco",
		})
		return
	}

	// Servir thumbnail
	c.File(thumbnailPath)
}

// DeleteAttachment remove um anexo
func (h *AttachmentHandler) DeleteAttachment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	// TODO: Obter userID do contexto de autenticação
	userID := uint(1)

	err = h.service.DeleteFile(uint(id), userID)
	if err != nil {
		if strings.Contains(err.Error(), "não encontrado") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Anexo não encontrado",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao deletar anexo",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Anexo removido com sucesso",
	})
}

// GetStats retorna estatísticas de anexos
func (h *AttachmentHandler) GetStats(c *gin.Context) {
	entityType := c.Query("entity_type")
	entityIDStr := c.Query("entity_id")

	var entityID uint
	if entityIDStr != "" {
		id, err := strconv.ParseUint(entityIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "entity_id inválido",
			})
			return
		}
		entityID = uint(id)
	}

	stats, err := h.service.GetStats(entityType, entityID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao obter estatísticas",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}
