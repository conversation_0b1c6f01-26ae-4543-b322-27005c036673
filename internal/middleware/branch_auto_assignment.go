package middleware

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

// BranchAutoAssignmentMiddleware middleware para vinculação automática de ordens à filial
type BranchAutoAssignmentMiddleware struct {
	db *gorm.DB
}

// NewBranchAutoAssignmentMiddleware cria uma nova instância do middleware
func NewBranchAutoAssignmentMiddleware(db *gorm.DB) *BranchAutoAssignmentMiddleware {
	return &BranchAutoAssignmentMiddleware{
		db: db,
	}
}

// AutoAssignBranch middleware que automaticamente vincula ordens à filial do usuário logado
func (m *BranchAutoAssignmentMiddleware) AutoAssignBranch() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Verificar se é uma requisição de criação de ordem
		if c.Request.Method != "POST" {
			c.Next()
			return
		}

		// Verificar se é uma rota de criação de ordem
		path := c.Request.URL.Path
		isOrderCreation := false
		
		orderPaths := []string{
			"/api/maintenance",
			"/api/maintenance-orders",
			"/api/orders",
			"/api/ordens",
		}
		
		for _, orderPath := range orderPaths {
			if path == orderPath {
				isOrderCreation = true
				break
			}
		}

		if !isOrderCreation {
			c.Next()
			return
		}

		// Obter ID do usuário do contexto
		userIDInterface, exists := c.Get("userID")
		if !exists {
			log.Printf("[BRANCH-AUTO-ASSIGNMENT] UserID não encontrado no contexto")
			c.Next()
			return
		}

		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case string:
			if id, err := strconv.ParseUint(v, 10, 32); err == nil {
				userID = uint(id)
			} else {
				log.Printf("[BRANCH-AUTO-ASSIGNMENT] Erro ao converter userID string para uint: %v", err)
				c.Next()
				return
			}
		default:
			log.Printf("[BRANCH-AUTO-ASSIGNMENT] Tipo de userID não suportado: %T", v)
			c.Next()
			return
		}

		// Buscar informações do usuário
		var user models.User
		if err := m.db.First(&user, userID).Error; err != nil {
			log.Printf("[BRANCH-AUTO-ASSIGNMENT] Erro ao buscar usuário ID %d: %v", userID, err)
			c.Next()
			return
		}

		// Verificar se o usuário tem uma filial associada
		if user.BranchID == nil {
			log.Printf("[BRANCH-AUTO-ASSIGNMENT] Usuário %d (%s) não tem filial associada", userID, user.Email)
			c.Next()
			return
		}

		branchID := *user.BranchID

		// Verificar se a filial existe
		var branch models.Branch
		if err := m.db.First(&branch, branchID).Error; err != nil {
			log.Printf("[BRANCH-AUTO-ASSIGNMENT] Filial ID %d não encontrada para usuário %d: %v", branchID, userID, err)
			c.Next()
			return
		}

		// Adicionar informações da filial ao contexto para uso nos handlers
		c.Set("autoBranchID", branchID)
		c.Set("autoBranchName", branch.Name)
		c.Set("autoAssignmentEnabled", true)

		log.Printf("[BRANCH-AUTO-ASSIGNMENT] ✅ Filial ID %d (%s) será automaticamente vinculada à ordem do usuário %d (%s)", 
			branchID, branch.Name, userID, user.Email)

		c.Next()
	}
}

// GetUserBranchInfo retorna informações da filial do usuário logado
func (m *BranchAutoAssignmentMiddleware) GetUserBranchInfo(userID uint) (*models.Branch, error) {
	var user models.User
	if err := m.db.Preload("Branch").First(&user, userID).Error; err != nil {
		return nil, err
	}

	if user.BranchID == nil {
		return nil, nil
	}

	return user.Branch, nil
}

// ValidateBranchAccess valida se o usuário tem acesso à filial especificada
func (m *BranchAutoAssignmentMiddleware) ValidateBranchAccess(userID uint, branchID uint) bool {
	var user models.User
	if err := m.db.First(&user, userID).Error; err != nil {
		log.Printf("[BRANCH-ACCESS-VALIDATION] Erro ao buscar usuário %d: %v", userID, err)
		return false
	}

	// Administradores têm acesso a todas as filiais
	if user.Role == "admin" {
		return true
	}

	// Usuários de filial só têm acesso à sua própria filial
	if user.BranchID != nil && *user.BranchID == branchID {
		return true
	}

	// Gerentes podem ter acesso a múltiplas filiais (implementar lógica específica se necessário)
	if user.Role == "gerente" || user.Role == "manager" {
		// Por enquanto, gerentes têm acesso apenas à sua filial
		// Esta lógica pode ser expandida conforme necessário
		if user.BranchID != nil && *user.BranchID == branchID {
			return true
		}
	}

	log.Printf("[BRANCH-ACCESS-VALIDATION] Usuário %d não tem acesso à filial %d", userID, branchID)
	return false
}

// BranchFilterMiddleware middleware para filtrar dados por filial
func (m *BranchAutoAssignmentMiddleware) BranchFilterMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obter ID do usuário do contexto
		userIDInterface, exists := c.Get("userID")
		if !exists {
			c.Next()
			return
		}

		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		default:
			c.Next()
			return
		}

		// Buscar informações do usuário
		var user models.User
		if err := m.db.First(&user, userID).Error; err != nil {
			c.Next()
			return
		}

		// Se o usuário é admin, não aplicar filtro
		if user.Role == "admin" {
			c.Set("branchFilter", "none")
			c.Next()
			return
		}

		// Se o usuário tem filial associada, aplicar filtro
		if user.BranchID != nil {
			c.Set("branchFilter", "enabled")
			c.Set("userBranchID", *user.BranchID)
			log.Printf("[BRANCH-FILTER] Aplicando filtro de filial %d para usuário %d (%s)", 
				*user.BranchID, userID, user.Email)
		}

		c.Next()
	}
}

// ExtractBranchIDFromPath extrai o ID da filial da URL (para rotas como /filial/:id/ordens)
func ExtractBranchIDFromPath(c *gin.Context) (uint, bool) {
	branchIDStr := c.Param("filialID")
	if branchIDStr == "" {
		branchIDStr = c.Param("branchID")
	}
	if branchIDStr == "" {
		branchIDStr = c.Param("id")
	}

	if branchIDStr == "" {
		return 0, false
	}

	branchID, err := strconv.ParseUint(branchIDStr, 10, 32)
	if err != nil {
		return 0, false
	}

	return uint(branchID), true
}

// ValidateBranchAccessMiddleware middleware para validar acesso à filial
func (m *BranchAutoAssignmentMiddleware) ValidateBranchAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extrair ID da filial da URL
		branchID, hasBranchID := ExtractBranchIDFromPath(c)
		if !hasBranchID {
			c.Next()
			return
		}

		// Obter ID do usuário
		userIDInterface, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
			c.Abort()
			return
		}

		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		default:
			c.JSON(http.StatusBadRequest, gin.H{"error": "ID de usuário inválido"})
			c.Abort()
			return
		}

		// Validar acesso
		if !m.ValidateBranchAccess(userID, branchID) {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Acesso negado à filial especificada",
				"code":  "BRANCH_ACCESS_DENIED",
			})
			c.Abort()
			return
		}

		c.Set("validatedBranchID", branchID)
		c.Next()
	}
}
