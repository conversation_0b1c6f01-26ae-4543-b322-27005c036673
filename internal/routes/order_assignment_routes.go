package routes

import (
	"net/http"
	"tradicao/internal/middleware"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// SetupOrderAssignmentRoutes configura as rotas para atribuição de ordens
// NOTA: Handlers de atribuição foram consolidados no unified_order_handler
func SetupOrderAssignmentRoutes(router *gin.Engine, authMiddleware gin.HandlerFunc) {
	// Grupo de rotas para a API de atribuição de ordens
	apiAssignments := router.Group("/api/order-assignments")
	apiAssignments.Use(authMiddleware)

	// Rotas para atribuição de ordens a técnicos
	// Apenas administradores, gerentes e prestadores podem atribuir ordens a técnicos
	technicianAssignments := apiAssignments.Group("/technician")
	technicianAssignments.Use(middleware.RoleMiddleware("admin", "gerente", "prestador"))
	{
		// TODO: Implementar no unified_order_handler
		technicianAssignments.POST("", func(c *gin.Context) {
			c.JSON(http.StatusNotImplemented, gin.H{
				"success": false,
				"message": "Funcionalidade movida para /api/orders - usar unified handler",
			})
		})

		technicianAssignments.GET("", func(c *gin.Context) {
			c.JSON(http.StatusNotImplemented, gin.H{
				"success": false,
				"message": "Funcionalidade movida para /api/orders - usar unified handler",
			})
		})
	}

	// Rotas para atribuição de ordens a prestadores
	// Apenas administradores, gerentes e financeiro podem atribuir ordens a prestadores
	providerAssignments := apiAssignments.Group("/provider")
	providerAssignments.Use(permissions.RoleMiddleware("admin", "gerente", "financeiro"))
	{
		// TODO: Implementar no unified_order_handler
		providerAssignments.POST("", func(c *gin.Context) {
			c.JSON(http.StatusNotImplemented, gin.H{
				"success": false,
				"message": "Funcionalidade movida para /api/orders - usar unified handler",
			})
		})

		providerAssignments.GET("", func(c *gin.Context) {
			c.JSON(http.StatusNotImplemented, gin.H{
				"success": false,
				"message": "Funcionalidade movida para /api/orders - usar unified handler",
			})
		})
	}

	// Redirecionar rota antiga para nova arquitetura
	router.GET("/api/ordens/atribuir", authMiddleware, permissions.RoleMiddleware("admin", "gerente", "prestador"), func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/api/orders")
	})
}
