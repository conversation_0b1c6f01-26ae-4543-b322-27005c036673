package routes

import (
	"log"
	"net/http"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRouterV2 configura todas as rotas da aplicação - versão 2
func SetupRouterV2(db *gorm.DB) *gin.Engine {
	r := gin.Default()

	// Load HTML templates
	r.LoadHTMLGlob("web/templates/**/*")

	// Servindo arquivos estáticos
	r.Static("/static", "./web/static")
	r.StaticFS("/web", http.Dir("./web"))

	// Rotas públicas (sem autenticação)
	r.GET("/login", func(c *gin.Context) {
		c.HTML(http.StatusOK, "login/login.html", nil)
	})
	// Usando o wrapper para compatibilidade
	r.POST("/auth", handlers.AuthHandlerWrapper(db))

	// Rotas de logout
	r.GET("/logout", handlers.LogoutHandler)              // Rota de logout para acesso direto via navegador
	r.POST("/api/auth/logout", handlers.LogoutAPIHandler) // Rota de logout para chamadas AJAX

	// Middleware de autenticação para rotas protegidas
	protected := r.Group("/")
	protected.Use(middleware.AuthMiddleware())

	// Rotas de páginas principais
	protected.GET("/", handlers.HomePage)
	protected.GET("/dashboard", func(c *gin.Context) {
		c.HTML(http.StatusOK, "dashboard.html", nil)
	})
	protected.GET("/calendar", func(c *gin.Context) {
		c.HTML(http.StatusOK, "calendar.html", nil)
	})
	protected.GET("/minha_conta", func(c *gin.Context) {
		c.HTML(http.StatusOK, "minha_conta.html", nil)
	})
	protected.GET("/settings", func(c *gin.Context) {
		log.Println("Acessando rota /settings")
		c.HTML(http.StatusOK, "settings.html", gin.H{
			"title": "Configurações - Rede Tradição",
		})
	})

	// Inicializar componentes para filiais, estações e branches
	// Repositórios
	filialRepo := repository.NewGormFilialRepository(db)

	// Serviços
	filialService := services.NewFilialService(filialRepo)

	// Handlers
	filialAPIHandler := handlers.NewFilialAPIHandler(filialService)

	// Configurar rotas de filiais
	SetupFilialRoutes(r, filialAPIHandler)

	// Inicializando handlers adicionais
	// Criar repositório e serviço para equipamentos usando Ent + Atlas
	// Comentado temporariamente para permitir a compilação
	// equipmentRepo := repository.NewEntEquipmentRepository() // Ajustar para passar client Ent
	// equipmentService := services.NewEquipmentService(equipmentRepo)
	equipmentService := &services.EquipmentService{}
	equipmentHandler := handlers.NewEquipmentHandler(equipmentService)

	// Configurando as rotas de API
	// As rotas de filiais, estações e branches já são configuradas pelo SetupUnifiedComponents
	SetupEquipmentRoutes(r, equipmentHandler)

	// Rotas adicionais para equipamentos por filial
	equipmentRoutes := r.Group("/api/equipments")
	equipmentRoutes.Use(middleware.AuthMiddleware())
	// Implementar quando o handler estiver disponível
	// equipmentRoutes.GET("/filial/:id", equipmentHandler.GetEquipmentsByFilial)

	// Rotas da API temporárias ou legadas
	apiGroup := r.Group("/api")
	apiGroup.Use(middleware.AuthMiddleware())

	// Exemplo de rota legada - atualizar quando migrar para nova estrutura
	apiGroup.GET("/calendar-events", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"events": []string{}})
	})

	// apiGroup.POST("/maintenance-orders", func(c *gin.Context) { // REMOVIDO - usar /api/orders
	//	c.JSON(http.StatusOK, gin.H{"status": "success"})
	// })

	apiGroup.POST("/provider-update-order", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "success"})
	})

	// Rota para página de criação de ordens (nova versão)
	protected.GET("/ordens/create", func(c *gin.Context) {
		c.HTML(http.StatusOK, "ordens/create_order_new.html", gin.H{
			"title": "Nova Ordem de Manutenção - Rede Tradição",
		})
	})

	// Handle 404
	r.NoRoute(func(c *gin.Context) {
		c.HTML(404, "404.html", nil)
	})

	return r
}
