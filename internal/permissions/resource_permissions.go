package permissions

import (
	"log"
	"tradicao/internal/database"
	"tradicao/internal/models"
)

// hasOrderPermission verifica se um usuário tem permissão para acessar uma ordem específica
func (s *Service) hasOrderPermission(userID uint, orderID uint, action Action) bool {
	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		log.Printf("[PERMISSION-SERVICE] Erro ao conectar ao banco de dados")
		return false
	}

	// Buscar usuário no banco de dados
	var user models.User
	if err := db.First(&user, userID).Error; err != nil {
		log.Printf("[PERMISSION-SERVICE] Usuário não encontrado: %d", userID)
		return false
	}

	// Administradores e gerentes têm acesso a todas as ordens
	if user.Role == models.RoleAdmin || user.Role == models.RoleGerente {
		log.Printf("[PERMISSION-SERVICE] Acesso permitido para administrador/gerente: %d", userID)
		return true
	}

	// Buscar ordem no banco de dados
	var order models.MaintenanceOrder
	if err := db.First(&order, orderID).Error; err != nil {
		log.Printf("[PERMISSION-SERVICE] Ordem não encontrada: %d", orderID)
		return false
	}

	// Verificar permissões específicas com base no perfil do usuário
	switch user.Role {
	case models.RoleTechnician:
		// Técnicos podem acessar ordens atribuídas a eles ou ao seu prestador de serviço
		log.Printf("[PERMISSION-SERVICE] Verificando permissão para técnico: %d, TechnicianID da ordem: %v", userID, order.TechnicianID)

		// Verificar se o técnico está atribuído diretamente à ordem
		if order.TechnicianID != nil && *order.TechnicianID == userID {
			log.Printf("[PERMISSION-SERVICE] Acesso permitido para técnico atribuído: %d", userID)
			return true
		}

		// Verificar se o técnico pertence a algum prestador de serviço
		if user.ServiceProviderID != nil && *user.ServiceProviderID > 0 {
			// Verificar se a ordem está atribuída ao prestador de serviço do técnico
			// Não importa se a ordem tem ou não um técnico atribuído
			if order.ServiceProviderID != nil && *order.ServiceProviderID == *user.ServiceProviderID {
				log.Printf("[PERMISSION-SERVICE] Acesso permitido para técnico (%d) do prestador de serviço (%d)", userID, *user.ServiceProviderID)
				return true
			}
		}

		// Logs detalhados para depuração
		log.Printf("[PERMISSION-SERVICE] Verificando permissão para ordem %d", orderID)
		log.Printf("[PERMISSION-SERVICE] Detalhes da ordem: ID=%d, TechnicianID=%v, ServiceProviderID=%v", order.ID, order.TechnicianID, order.ServiceProviderID)
		log.Printf("[PERMISSION-SERVICE] Detalhes do usuário: ID=%d, Role=%s, ServiceProviderID=%v", userID, user.Role, user.ServiceProviderID)

		// Verificação simplificada: permitir acesso a qualquer ordem que apareça na listagem do técnico
		// Esta é a abordagem mais direta e confiável

		// Construir a consulta para buscar ordens do técnico
		query := db.Model(&models.MaintenanceOrder{})

		// Primeiro critério: ordens atribuídas diretamente ao técnico
		query = query.Where("technician_id = ?", userID)

		// Segundo critério: ordens atribuídas ao prestador do técnico
		if user.ServiceProviderID != nil && *user.ServiceProviderID > 0 {
			query = query.Or("service_provider_id = ?", *user.ServiceProviderID)
		}

		// Executar a consulta
		var count int64
		if err := query.Where("id = ?", orderID).Count(&count).Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Erro ao verificar se ordem pertence ao técnico: %v", err)
			return false
		}

		// Se encontrou a ordem, permitir acesso
		if count > 0 {
			log.Printf("[PERMISSION-SERVICE] Acesso permitido para técnico (%d) - ordem %d encontrada na listagem", userID, orderID)
			return true
		}

		// Verificar se o técnico está associado ao prestador de serviço da ordem
		if user.ServiceProviderID != nil && order.ServiceProviderID != nil && *user.ServiceProviderID == *order.ServiceProviderID {
			log.Printf("[PERMISSION-SERVICE] Acesso permitido para técnico (%d) - associado ao prestador de serviço da ordem %d", userID, orderID)
			return true
		}

		// Verificar se o técnico está associado à filial da ordem
		var techBranchCount int64
		if err := db.Table("technician_branches").
			Where("technician_id = ? AND branch_id = ?", userID, order.BranchID).
			Count(&techBranchCount).Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Erro ao verificar tabela technician_branches: %v", err)
		} else if techBranchCount > 0 {
			log.Printf("[PERMISSION-SERVICE] Acesso permitido para técnico (%d) - associado à filial da ordem %d", userID, orderID)
			return true
		}

		// Log detalhado para depuração
		log.Printf("[PERMISSION-SERVICE] Acesso negado para técnico (%d) - ordem %d não encontrada na listagem", userID, orderID)
		log.Printf("[PERMISSION-SERVICE] Detalhes da ordem: TechnicianID=%v, ServiceProviderID=%v", order.TechnicianID, order.ServiceProviderID)
		log.Printf("[PERMISSION-SERVICE] Detalhes do usuário: ServiceProviderID=%v", user.ServiceProviderID)

		return false

	case models.RoleFilial:
		// Filiais só podem acessar ordens da própria filial
		log.Printf("[PERMISSION-SERVICE] Verificando permissão para filial: %d, BranchID do usuário: %v, BranchID da ordem: %d", userID, user.BranchID, order.BranchID)
		if user.BranchID != nil && *user.BranchID == order.BranchID {
			log.Printf("[PERMISSION-SERVICE] Acesso permitido para filial: %d", userID)
			return true
		}
		return false

	case models.RolePrestador:
		// Prestadores de serviço só podem acessar ordens atribuídas a eles
		log.Printf("[PERMISSION-SERVICE] Verificando permissão para prestador: %d, ServiceProviderID da ordem: %v", userID, order.ServiceProviderID)
		if order.ServiceProviderID != nil && *order.ServiceProviderID == userID {
			log.Printf("[PERMISSION-SERVICE] Acesso permitido para prestador: %d", userID)
			return true
		}
		return false

	default:
		return false
	}
}

// hasEquipmentPermission verifica se um usuário tem permissão para acessar um equipamento específico
func (s *Service) hasEquipmentPermission(userID uint, equipmentID uint, action Action) bool {
	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		log.Printf("[PERMISSION-SERVICE] Erro ao conectar ao banco de dados")
		return false
	}

	// Buscar usuário no banco de dados
	var user models.User
	if err := db.First(&user, userID).Error; err != nil {
		log.Printf("[PERMISSION-SERVICE] Usuário não encontrado: %d", userID)
		return false
	}

	// Administradores e gerentes têm acesso a todos os equipamentos
	if user.Role == models.RoleAdmin || user.Role == models.RoleGerente {
		return true
	}

	// Buscar equipamento no banco de dados
	var equipment models.Equipment
	if err := db.First(&equipment, equipmentID).Error; err != nil {
		log.Printf("[PERMISSION-SERVICE] Equipamento não encontrado: %d", equipmentID)
		return false
	}

	// Filiais só podem acessar equipamentos da própria filial
	if user.Role == models.RoleFilial {
		if user.BranchID != nil && *user.BranchID == equipment.BranchID {
			return true
		}
		return false
	}

	// Técnicos e prestadores podem visualizar equipamentos das filiais onde têm ordens
	if user.Role == models.RoleTechnician || user.Role == models.RolePrestador {
		// Verificar se o usuário tem alguma ordem associada a este equipamento
		var count int64
		query := db.Model(&models.MaintenanceOrder{})

		if user.Role == models.RoleTechnician {
			query = query.Where("technician_id = ? AND equipment_id = ?", userID, equipmentID)
		} else if user.Role == models.RolePrestador {
			query = query.Where("service_provider_id = ? AND equipment_id = ?", userID, equipmentID)
		}

		query.Count(&count)
		return count > 0
	}

	return false
}

// REMOVIDO: hasBranchPermission, hasTechnicianPermission, hasServiceProviderPermission
// Essas funções sempre retornavam true e foram substituídas pelo sistema unificado
// Use o UnifiedPermissionService em vez disso
