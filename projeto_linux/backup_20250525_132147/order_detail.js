// Variáveis Globais
let costsModalInstance = null;
let interactionModalInstance = null;
let assignProviderModalInstance = null;
let canEditCosts = false;

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    initializeModals();
    initializeForms();
    initializeAttachmentButton();
});

// Funções de Inicialização
function initializeModals() {
    const costsModalElement = document.getElementById('costsModal');
    if (costsModalElement) {
        costsModalInstance = new bootstrap.Modal(costsModalElement);
    }

    const interactionModalElement = document.getElementById('interactionModal');
    if (interactionModalElement) {
        interactionModalInstance = new bootstrap.Modal(interactionModalElement);
    }

    const assignProviderModalElement = document.getElementById('assignProviderModal');
    if (assignProviderModalElement) {
        assignProviderModalInstance = new bootstrap.Modal(assignProviderModalElement);
    }
}

function initializeForms() {
    const addCostForm = document.getElementById('addCostForm');
    if (addCostForm) {
        addCostForm.addEventListener('submit', handleAddCostSubmit);
    }

    const addInteractionForm = document.getElementById('addInteractionForm');
    if (addInteractionForm) {
        addInteractionForm.addEventListener('submit', handleAddInteractionSubmit);
    }

    const assignProviderForm = document.getElementById('assignProviderForm');
    if (assignProviderForm) {
        assignProviderForm.addEventListener('submit', handleAssignProviderSubmit);
    }
}

// Funções de Modal de Custos
async function viewCostsModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de visualizar custos da ordem #18 que é inválida/hardcoded');
        alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    canEditCosts = false;
    setupCostsModal(orderId);
    await loadCostsForModal(orderId);
}

async function openCostsModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de editar custos da ordem #18 que é inválida/hardcoded');
        alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    if (!canUserEditCosts()) {
        alert("Você não tem permissão para editar custos nesta ordem ou neste status.");
        return;
    }

    canEditCosts = true;
    setupCostsModal(orderId);
    await loadCostsForModal(orderId);
}

function setupCostsModal(orderId) {
    document.getElementById('costsModalOrderId').textContent = `#${orderId}`;
    document.getElementById('addCostFormContainer').style.display = canEditCosts ? 'block' : 'none';
    document.getElementById('costsModalTotal').textContent = 'Calculando...';
    document.getElementById('costItemsList').innerHTML = '<p class="text-muted">Carregando custos...</p>';

    if (costsModalInstance) {
        costsModalInstance.show();
    }
}

function canUserEditCosts() {
    return CURRENT_USER_ROLE === 'provider' &&
           CURRENT_USER_ID === ASSIGNED_PROVIDER_ID &&
           (CURRENT_ORDER_STATUS === 'in_progress' || CURRENT_ORDER_STATUS === 'revision_needed');
}

// Funções de Modal de Interação
function openInteractionModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de adicionar interação à ordem #18 que é inválida/hardcoded');
        alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    if (!canUserInteract()) {
        showToast('Não é possível adicionar interações a ordens pagas ou canceladas.', 'warning');
        return;
    }

    setupInteractionModal(orderId);
}

function setupInteractionModal(orderId) {
    document.getElementById('interactionModalOrderId').textContent = `#${orderId}`;
    document.getElementById('interactionOrderId').value = orderId;
    document.getElementById('interactionMessage').value = '';

    if (interactionModalInstance) {
        interactionModalInstance.show();
    }
}

function canUserInteract() {
    return CURRENT_ORDER_STATUS !== 'paid' && CURRENT_ORDER_STATUS !== 'cancelled';
}

// Funções de Modal de Atribuição
function openAssignModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de atribuir prestador à ordem #18 que é inválida/hardcoded');
        alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    if (!canAssignProvider()) {
        showToast('Esta ordem não está no status correto para atribuição.', 'warning');
        return;
    }

    setupAssignModal(orderId);
    loadProviders();
}

function setupAssignModal(orderId) {
    document.getElementById('assignProviderModalOrderId').textContent = `#${orderId}`;
    document.getElementById('assignProviderOrderId').value = orderId;
    const providerSelect = document.getElementById('providerSelect');
    providerSelect.innerHTML = '<option value="" selected disabled>Carregando prestadores...</option>';
    providerSelect.disabled = true;
}

function canAssignProvider() {
    return CURRENT_ORDER_STATUS === 'pending' && ASSIGNED_PROVIDER_ID === 0;
}

// Funções de API
async function loadCostsForModal(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de carregar custos da ordem #18 que é inválida/hardcoded');
        showErrorInCostsDisplay(new Error('Ordem #18 não está disponível. Por favor, selecione outra ordem.'));
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/costs`);
        if (!response.ok) throw new Error(`Erro HTTP ${response.status}`);

        const costs = await response.json();
        updateCostsDisplay(costs);
    } catch (error) {
        console.error("Erro ao carregar custos:", error);
        showErrorInCostsDisplay(error);
    }
}

function updateCostsDisplay(costs) {
    const listElement = document.getElementById('costItemsList');
    const totalElement = document.getElementById('costsModalTotal');
    const orderTotalCostDisplay = document.getElementById('orderTotalCostDisplay');

    if (!costs || costs.length === 0) {
        listElement.innerHTML = '<p class="text-muted">Nenhum custo registrado.</p>';
        updateTotalDisplay(0, totalElement, orderTotalCostDisplay);
        return;
    }

    const table = createCostsTable(costs);
    listElement.innerHTML = '';
    listElement.appendChild(table);

    const totalCost = calculateTotalCost(costs);
    updateTotalDisplay(totalCost, totalElement, orderTotalCostDisplay);
}

function createCostsTable(costs) {
    const table = document.createElement('table');
    table.className = 'table table-sm table-dark table-striped table-hover';

    const thead = table.createTHead();
    const headerRow = thead.insertRow();
    headerRow.innerHTML = `
        <th>Descrição</th>
        <th class="text-end">Qtd.</th>
        <th class="text-end">Vlr. Unit.</th>
        <th class="text-end">Subtotal</th>
        ${canEditCosts ? '<th>Ação</th>' : ''}
    `;

    const tbody = table.createTBody();
    costs.forEach(cost => {
        const row = tbody.insertRow();
        addCostRowCells(row, cost);
    });

    return table;
}

function addCostRowCells(row, cost) {
    const quantity = Number(cost.quantity) || 0;
    const unitPrice = Number(cost.unit_price) || 0;
    const subtotal = Number(cost.total_item_cost) || (quantity * unitPrice);

    row.insertCell().textContent = cost.description;

    const qtyCell = row.insertCell();
    qtyCell.className = 'text-end';
    qtyCell.textContent = quantity;

    const unitCell = row.insertCell();
    unitCell.className = 'text-end';
    unitCell.textContent = formatCurrencyJS(unitPrice);

    const subtotalCell = row.insertCell();
    subtotalCell.className = 'text-end';
    subtotalCell.textContent = formatCurrencyJS(subtotal);

    if (canEditCosts) {
        const actionCell = row.insertCell();
        const deleteButton = createDeleteButton(cost.id);
        actionCell.appendChild(deleteButton);
    }
}

function createDeleteButton(costId) {
    const button = document.createElement('button');
    button.className = 'btn btn-outline-danger btn-sm p-1';
    button.innerHTML = '<i class="fas fa-trash-alt"></i>';
    button.onclick = () => deleteCostItem(CURRENT_ORDER_ID, costId);
    return button;
}

function calculateTotalCost(costs) {
    return costs.reduce((total, cost) => {
        const quantity = Number(cost.quantity) || 0;
        const unitPrice = Number(cost.unit_price) || 0;
        return total + (Number(cost.total_item_cost) || (quantity * unitPrice));
    }, 0);
}

function updateTotalDisplay(total, totalElement, orderTotalCostDisplay) {
    const formattedTotal = formatCurrencyJS(total);
    totalElement.textContent = formattedTotal;
    if (orderTotalCostDisplay) {
        orderTotalCostDisplay.textContent = formattedTotal;
    }
}

function showErrorInCostsDisplay(error) {
    const listElement = document.getElementById('costItemsList');
    const totalElement = document.getElementById('costsModalTotal');
    const orderTotalCostDisplay = document.getElementById('orderTotalCostDisplay');

    listElement.innerHTML = `<p class="text-danger">Erro ao carregar custos: ${escapeHTML(error.message)}. Tente novamente.</p>`;
    totalElement.textContent = 'Erro';
    if (orderTotalCostDisplay) {
        orderTotalCostDisplay.textContent = 'Erro';
    }
}

// Funções Utilitárias
function formatCurrencyJS(value) {
    const numValue = Number(value);
    if (isNaN(numValue)) return "R$ 0,00";
    return numValue.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
}

function escapeHTML(str) {
    if (str === null || str === undefined) return '';
    let element = document.createElement('div');
    element.innerText = str;
    return element.innerHTML;
}

function formatStatusJS(status) {
    const statusMap = {
        'pending': 'Pendente',
        'rejected_provider': 'Recusada pelo Prestador',
        'in_progress': 'Em Andamento',
        'pending_approval': 'Pendente Aprovação',
        'revision_needed': 'Necessita Revisão',
        'approved': 'Aprovada',
        'invoiced': 'Faturada',
        'paid': 'Paga',
        'cancelled': 'Cancelada'
    };
    return statusMap[status] || status;
}

// Funções de Anexo de Arquivos
let attachmentModalInstance = null;

function initializeAttachmentButton() {
    // Inicializar o modal de anexo
    const attachmentModalElement = document.getElementById('attachmentModal');
    if (attachmentModalElement) {
        attachmentModalInstance = new bootstrap.Modal(attachmentModalElement);
    }

    // Adicionar evento de clique ao botão de anexo
    const attachFileBtn = document.getElementById('attachFileBtn');
    if (attachFileBtn) {
        attachFileBtn.addEventListener('click', openAttachmentModal);
    }

    // Adicionar evento de submit ao formulário de anexo
    const attachmentForm = document.getElementById('attachmentForm');
    if (attachmentForm) {
        attachmentForm.addEventListener('submit', handleAttachmentSubmit);
    }
}

function openAttachmentModal() {
    const orderId = document.getElementById('interactionOrderId').value;
    if (!orderId) {
        console.error("ID da ordem não encontrado");
        return;
    }

    // Configurar o modal de anexo
    document.getElementById('attachmentModalOrderId').textContent = orderId;
    document.getElementById('attachmentOrderId').value = orderId;
    document.getElementById('attachmentFile').value = '';
    document.getElementById('attachmentDescription').value = '';

    // Mostrar o modal de anexo
    if (attachmentModalInstance) {
        attachmentModalInstance.show();
    }
}

async function handleAttachmentSubmit(event) {
    event.preventDefault();

    const orderId = document.getElementById('attachmentOrderId').value;
    const fileInput = document.getElementById('attachmentFile');
    const description = document.getElementById('attachmentDescription').value;

    if (!orderId || !fileInput.files.length || !description) {
        showToast('Todos os campos são obrigatórios.', 'warning');
        return;
    }

    const file = fileInput.files[0];
    const formData = new FormData();
    formData.append('file', file);
    formData.append('description', description);

    try {
        const response = await fetch(`/api/orders/${orderId}/attachments`, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Fechar modal de anexo
        if (attachmentModalInstance) {
            attachmentModalInstance.hide();
        }

        showToast('Arquivo anexado com sucesso!', 'success');

        // Adicionar o anexo à interação
        const interactionMessage = document.getElementById('interactionMessage');
        const currentMessage = interactionMessage.value;
        const attachmentNote = `[Anexo: ${file.name} - ${description}]`;

        if (currentMessage) {
            interactionMessage.value = `${currentMessage}\n${attachmentNote}`;
        } else {
            interactionMessage.value = attachmentNote;
        }

    } catch (error) {
        console.error('Erro ao anexar arquivo:', error);
        showToast(`Erro ao anexar arquivo: ${error.message}`, 'error');
    }
}

function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastPlacement');
    if (!toastContainer) {
        alert(`[${type.toUpperCase()}] ${message}`);
        return;
    }

    const toastId = `toast-${Date.now()}`;
    const toastTypeClass = type === 'error' ? 'bg-danger' : (type === 'success' ? 'bg-success' : 'bg-info');

    const toastHTML = `
        <div id="${toastId}" class="toast align-items-center text-white ${toastTypeClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${escapeHTML(message)}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    toastElement.addEventListener('hidden.bs.toast', () => toastElement.remove());
}