// JavaScript simplificado para a página de Ordens

document.addEventListener('DOMContentLoaded', function() {
    // Elementos da página
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    const orderRows = document.querySelectorAll('.order-row');
    const filteredCount = document.getElementById('filteredCount');

    // Função para aplicar filtros
    function applyFilters() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const priorityValue = priorityFilter.value;

        let visibleCount = 0;

        orderRows.forEach(row => {
            const title = row.querySelector('.order-title').textContent.toLowerCase();
            const status = row.getAttribute('data-status');
            const priority = row.getAttribute('data-priority');

            // Verificar se a ordem atende a todos os filtros
            const matchesSearch = searchTerm === '' || title.includes(searchTerm);
            const matchesStatus = statusValue === 'all' || status === statusValue;
            const matchesPriority = priorityValue === 'all' || priority === priorityValue;

            // Mostrar ou ocultar a linha com base nos filtros
            if (matchesSearch && matchesStatus && matchesPriority) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Atualizar contador de ordens filtradas
        if (filteredCount) {
            filteredCount.textContent = visibleCount;
        }
    }

    // Adicionar event listeners para os filtros
    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    if (priorityFilter) {
        priorityFilter.addEventListener('change', applyFilters);
    }

    // Limpar filtros
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            if (searchInput) searchInput.value = '';
            if (statusFilter) statusFilter.value = 'all';
            if (priorityFilter) priorityFilter.value = 'all';
            applyFilters();
        });
    }

    // Botões de ação
    const deleteButtons = document.querySelectorAll('.delete-order');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.getAttribute('data-id');

            // Verificar se o ID é 18 e bloqueá-lo
            if (orderId === "18" || parseInt(orderId) === 18) {
                console.error('Tentativa de excluir a ordem #18 que é inválida/hardcoded');
                alert('Ordem #18 não está disponível para exclusão. Por favor, selecione outra ordem.');
                return;
            }

            if (confirm(`Tem certeza que deseja excluir a ordem #${orderId}?`)) {
                // Aqui seria feita a requisição para excluir a ordem
                console.log(`Excluindo ordem #${orderId}`);
            }
        });
    });

    // Inicializar tooltips do Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
