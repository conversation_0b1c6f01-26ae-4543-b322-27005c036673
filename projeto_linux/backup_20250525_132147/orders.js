/**
 * Módulo JavaScript para gerenciamento de ordens de manutenção
 * Sistema Shell Tradição
 */

// Configuração da API
const API_URL = ''; // URL base da API, vazio para usar a mesma origem

// Verifica autenticação do usuário
const authToken = localStorage.getItem('auth_token');

// Cache local
const cache = {
    orders: new Map(),
    metrics: new Map(),
    lastUpdate: new Date(),
    ttl: 5 * 60 * 1000 // 5 minutos
};

// Configurações
const config = {
    pageSize: 10,
    currentPage: 1,
    totalPages: 1,
    filters: {
        status: 'all',
        branch: 'all',
        date: ''
    }
};

// Elementos da página
const elements = {
    ordersList: document.getElementById('ordersList'),
    pagination: document.getElementById('pagination'),
    statusFilter: document.getElementById('statusFilter'),
    branchFilter: document.getElementById('branchFilter'),
    dateFilter: document.getElementById('dateFilter'),
    applyFilter: document.getElementById('applyFilter'),
    refreshBtn: document.getElementById('refreshBtn'),
    metrics: {
        pending: document.getElementById('pendingOrdersCount'),
        inProgress: document.getElementById('inProgressOrdersCount'),
        completed: document.getElementById('completedOrdersCount'),
        total: document.getElementById('totalOrdersCount')
    }
};

// Funções de formatação
function formatStatus(status) {
    const statusMap = {
        'aberta': 'Aberta',
        'confirmada': 'Confirmada',
        'em_analise': 'Em Análise',
        'em_andamento': 'Em Andamento',
        'aguardando_peca': 'Aguardando Peças',
        'concluida_tecnico': 'Conclusão Técnica',
        'validada_filial': 'Validada pela Filial',
        'aprovada_gerencia': 'Aprovada pela Gerência',
        'concluida': 'Concluída',
        'cancelada': 'Cancelada',
        'rejeitada': 'Rejeitada'
    };
    return statusMap[status] || status;
}

function getStatusClass(status) {
    const classMap = {
        'aberta': 'bg-secondary',
        'confirmada': 'bg-info text-dark',
        'em_analise': 'bg-primary',
        'em_andamento': 'bg-warning text-dark',
        'aguardando_peca': 'bg-warning text-dark',
        'concluida_tecnico': 'bg-info text-dark',
        'validada_filial': 'bg-info text-dark',
        'aprovada_gerencia': 'bg-success',
        'concluida': 'bg-success',
        'cancelada': 'bg-danger',
        'rejeitada': 'bg-danger'
    };
    return classMap[status] || 'bg-secondary';
}

function formatPrioridade(prioridade) {
    const prioridadeMap = {
        'baixa': 'Baixa',
        'media': 'Média',
        'alta': 'Alta',
        'urgente': 'Urgente'
    };
    return prioridadeMap[prioridade] || prioridade;
}

function getPrioridadeClass(prioridade) {
    const classMap = {
        'baixa': 'bg-success',
        'media': 'bg-info text-dark',
        'alta': 'bg-warning text-dark',
        'urgente': 'bg-danger'
    };
    return classMap[prioridade] || 'bg-secondary';
}

function formatTipo(tipo) {
    const tipoMap = {
        'preventiva': 'Preventiva',
        'corretiva': 'Corretiva',
        'inspecao': 'Inspeção',
        'calibragem': 'Calibragem',
        'instalacao': 'Instalação'
    };
    return tipoMap[tipo] || tipo;
}

function formatData(dataString) {
    if (!dataString) return '-';
    const data = new Date(dataString);
    return data.toLocaleDateString('pt-BR');
}

function formatDataHora(dataString) {
    if (!dataString) return '-';
    const data = new Date(dataString);
    return data.toLocaleDateString('pt-BR') + ' ' + data.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
}

function formatMoeda(valor) {
    if (valor === null || valor === undefined) return 'R$ 0,00';
    return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(valor);
}

// Função para carregar ordens com cache
async function carregarOrdens(pagina = 1, filtros = {}, forceUpdate = false) {
    try {
        const cacheKey = `${pagina}-${JSON.stringify(filtros)}`;
        const now = new Date();

        // Verificar cache apenas se não for forçada a atualização
        if (!forceUpdate && cache.orders.has(cacheKey) && (now - cache.lastUpdate) < cache.ttl) {
            const cachedData = cache.orders.get(cacheKey);
            renderizarOrdens(cachedData);
            atualizarPaginacao(cachedData.total, cachedData.paginas, cachedData.pagina_atual);
            atualizarInfo(cachedData.total);
            console.log('Dados carregados do cache');
            return;
        }

        console.log('Carregando dados da API', { pagina, filtros, forceUpdate });

        const params = new URLSearchParams({
            pagina: pagina,
            ...filtros
        });

        const userID = localStorage.getItem('user_id');
        if (userID) {
            params.append('userID', userID);
        }

        const response = await fetch(`${API_URL}/api/orders?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Authorization': authToken ? `Bearer ${authToken}` : ''
            }
        });

        if (!response.ok) {
            throw new Error(`Erro ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Atualizar cache
        cache.orders.set(cacheKey, data);
        cache.lastUpdate = now;

        renderizarOrdens(data);
        atualizarPaginacao(data.total, data.paginas, data.pagina_atual);
        atualizarInfo(data.total);

    } catch (error) {
        console.error('Erro ao carregar ordens:', error);
        exibirMensagem('Não foi possível carregar as ordens de manutenção. Tente novamente mais tarde.', 'erro');
    }
}

// Função para criar nova ordem
async function criarOrdem(formData) {
    try {
        const response = await fetch(`${API_URL}/api/orders`, {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': authToken ? `Bearer ${authToken}` : ''
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.mensagem || `Erro ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;

    } catch (error) {
        console.error('Erro ao criar ordem:', error);
        throw error;
    }
}

// Função para obter detalhes de uma ordem
async function obterOrdem(id) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (id === 18 || id === "18") {
        console.error('Tentativa de obter a ordem #18 que é inválida/hardcoded');
        throw new Error('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
    }

    try {
        const response = await fetch(`${API_URL}/api/orders/${id}`, {
            method: 'GET',
            headers: {
                'Authorization': authToken ? `Bearer ${authToken}` : ''
            }
        });

        if (!response.ok) {
            throw new Error(`Erro ${response.status}: ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        console.error(`Erro ao obter detalhes da ordem ${id}:`, error);
        throw error;
    }
}

// Função para atualizar status de uma ordem
async function atualizarStatusOrdem(id, novoStatus, observacao) {
    try {
        const response = await fetch(`${API_URL}/api/orders/${id}/status`, {
            method: 'PATCH',
            headers: {
                'Authorization': authToken ? `Bearer ${authToken}` : '',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: novoStatus,
                observacao: observacao
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.mensagem || `Erro ${response.status}: ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        console.error(`Erro ao atualizar status da ordem ${id}:`, error);
        throw error;
    }
}

// Função otimizada para renderizar ordens
function renderizarOrdens(data) {
    const fragment = document.createDocumentFragment();

    data.ordens.forEach(ordem => {
        const card = document.createElement('div');
        card.className = 'card-shell';
        card.innerHTML = `
            <div class="card-header">
                <h5>Ordem #${ordem.id}</h5>
                <span class="badge ${getStatusClass(ordem.status)}">${formatStatus(ordem.status)}</span>
            </div>
            <div class="card-body">
                <div class="info-item">
                    <span class="info-label">Equipamento:</span>
                    <span class="info-value">${ordem.equipamento_nome}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Data:</span>
                    <span class="info-value">${formatData(ordem.data_abertura)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Prioridade:</span>
                    <span class="info-value">${formatPrioridade(ordem.prioridade)}</span>
                </div>
            </div>
        `;
        fragment.appendChild(card);
    });

    elements.ordersList.innerHTML = '';
    elements.ordersList.appendChild(fragment);
}

// Função para exibir modal de detalhes da ordem
async function mostrarDetalhesOrdem(id) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (id === 18 || id === "18") {
        console.error('Tentativa de mostrar detalhes da ordem #18 que é inválida/hardcoded');
        alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    try {
        const modal = document.getElementById('orderDetailsModal');
        const content = document.getElementById('orderDetailsContent');

        if (!modal || !content) return;

        // Mostrar carregamento
        content.innerHTML = `
            <div class="text-center p-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Carregando...</span>
                </div>
                <p class="mt-3">Carregando detalhes da ordem...</p>
            </div>
        `;

        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();

        // Buscar dados da ordem
        const ordem = await obterOrdem(id);

        // Renderizar detalhes
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="order-details-section">
                        <h6>Informações Básicas</h6>
                        <div class="detail-item">
                            <span class="detail-label">ID:</span>
                            <span>${ordem.id}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Título:</span>
                            <span>${ordem.titulo}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Tipo:</span>
                            <span>${formatTipo(ordem.tipo)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Filial:</span>
                            <span>${ordem.filial_nome || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Equipamento:</span>
                            <span>${ordem.equipamento_nome || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Status:</span>
                            <span class="status-badge ${getStatusClass(ordem.status)}">${formatStatus(ordem.status)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Prioridade:</span>
                            <span class="priority-badge ${getPrioridadeClass(ordem.prioridade)}">${formatPrioridade(ordem.prioridade)}</span>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="order-details-section">
                        <h6>Datas</h6>
                        <div class="detail-item">
                            <span class="detail-label">Abertura:</span>
                            <span>${formatDataHora(ordem.data_abertura)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Desejada:</span>
                            <span>${ordem.data_desejada ? formatData(ordem.data_desejada) : '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Início:</span>
                            <span>${ordem.data_inicio ? formatDataHora(ordem.data_inicio) : '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Conclusão:</span>
                            <span>${ordem.data_conclusao ? formatDataHora(ordem.data_conclusao) : '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Tempo Estimado:</span>
                            <span>${ordem.tempo_estimado ? ordem.tempo_estimado + ' minutos' : '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Tempo Real:</span>
                            <span>${ordem.tempo_real ? ordem.tempo_real + ' minutos' : '-'}</span>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="order-details-section">
                        <h6>Descrição do Problema</h6>
                        <p>${ordem.descricao || 'Sem descrição'}</p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="order-details-section">
                        <h6>Solicitante</h6>
                        <div class="detail-item">
                            <span class="detail-label">Nome:</span>
                            <span>${ordem.solicitante_nome || '-'}</span>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="order-details-section">
                        <h6>Técnico</h6>
                        <div class="detail-item">
                            <span class="detail-label">Nome:</span>
                            <span>${ordem.tecnico_nome || 'Não atribuído'}</span>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="order-details-section">
                        <h6>Observações</h6>
                        <p>${ordem.observacoes || 'Sem observações'}</p>
                    </div>
                </div>

                ${ordem.fotos && ordem.fotos.length > 0 ? `
                    <div class="col-12">
                        <div class="order-details-section">
                            <h6>Fotos</h6>
                            <div class="photo-preview">
                                ${ordem.fotos.map(foto => `
                                    <div class="photo-item">
                                        <img src="${foto.url}" alt="Foto">
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                ` : ''}

                ${ordem.historico && ordem.historico.length > 0 ? `
                    <div class="col-12">
                        <div class="order-details-section">
                            <h6>Histórico</h6>
                            <div class="timeline">
                                ${ordem.historico.map((item, index) => `
                                    <div class="timeline-item">
                                        <div class="timeline-marker">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-date">
                                                ${formatDataHora(item.data)}
                                            </div>
                                            <div>
                                                <strong>${item.descricao}</strong>
                                            </div>
                                            <div>
                                                <small>${item.usuario || 'Sistema'}</small>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

    } catch (error) {
        console.error('Erro ao mostrar detalhes da ordem:', error);
        alert('Não foi possível carregar os detalhes da ordem. Tente novamente.');
    }
}

// Função para atualizar paginação
function atualizarPaginacao(total, paginas, paginaAtual) {
    const pagination = document.getElementById('ordersPagination');
    if (!pagination) return;

    pagination.innerHTML = '';

    // Botão anterior
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${paginaAtual === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" data-page="${paginaAtual - 1}" aria-label="Anterior">
            <span aria-hidden="true">&laquo;</span>
        </a>
    `;
    pagination.appendChild(prevLi);

    // Páginas
    for (let i = 1; i <= paginas; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === paginaAtual ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    // Botão próximo
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${paginaAtual === paginas ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" data-page="${paginaAtual + 1}" aria-label="Próximo">
            <span aria-hidden="true">&raquo;</span>
        </a>
    `;
    pagination.appendChild(nextLi);

    // Adicionar eventos aos links de paginação
    document.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.getAttribute('data-page'));
            if (page && page !== paginaAtual) {
                carregarOrdens(page);
            }
        });
    });
}

// Função para atualizar informações de contagem
function atualizarInfo(total) {
    const orderCountInfo = document.getElementById('orderCountInfo');
    if (orderCountInfo) {
        orderCountInfo.textContent = `Total: ${total} ordem(ns)`;
    }
}

// Função para exibir mensagens de sucesso/erro
function exibirMensagem(mensagem, tipo = 'sucesso') {
    const alertColor = tipo === 'sucesso' ? 'success' : 'danger';
    const alertIcon = tipo === 'sucesso' ? 'check-circle' : 'exclamation-triangle';

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${alertColor} alert-dismissible fade show`;
    alertDiv.setAttribute('role', 'alert');
    alertDiv.innerHTML = `
        <i class="fas fa-${alertIcon} me-2"></i>
        ${mensagem}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
    `;

    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }

    // Auto-fechar após 5 segundos
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 150);
    }, 5000);
}

// Filtros
document.addEventListener('DOMContentLoaded', function() {
    // Carregar ordens ao iniciar
    if (document.getElementById('ordersTableBody')) {
        carregarOrdens();
    }

    // Evento para aplicar filtros
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            const filtros = {
                status: document.getElementById('statusFilter').value,
                prioridade: document.getElementById('priorityFilter').value,
                filial: document.getElementById('locationFilter').value,
                data: document.getElementById('dateFilter').value
            };

            // Remover filtros vazios ou "all"
            Object.keys(filtros).forEach(key => {
                if (filtros[key] === 'all' || !filtros[key]) {
                    delete filtros[key];
                }
            });

            carregarOrdens(1, filtros);
        });
    }

    // Evento para limpar filtros
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            // Resetar filtros
            document.getElementById('statusFilter').value = 'all';
            document.getElementById('priorityFilter').value = 'all';
            document.getElementById('locationFilter').value = 'all';
            document.getElementById('dateFilter').value = '';

            // Carregar ordens sem filtros
            carregarOrdens();
        });
    }
});

// Inicialização do DataTable
$(document).ready(function() {
    $('#ordersTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/pt-BR.json'
        },
        order: [[0, 'desc']]
    });
});

// Função para editar uma ordem
function editOrder(id) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (id === 18 || id === "18") {
        console.error('Tentativa de editar a ordem #18 que é inválida/hardcoded');
        alert('Ordem #18 não está disponível para edição. Por favor, selecione outra ordem.');
        return;
    }

    loadOrderData(id);
    $('#editModal').modal('show');
}

// Função para excluir uma ordem
function deleteOrder(id) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (id === 18 || id === "18") {
        console.error('Tentativa de excluir a ordem #18 que é inválida/hardcoded');
        alert('Ordem #18 não está disponível para exclusão. Por favor, selecione outra ordem.');
        return;
    }

    $('#deleteModal').data('orderId', id);
    $('#deleteModal').modal('show');
}

// Função para confirmar exclusão
function confirmDelete() {
    const id = $('#deleteModal').data('orderId');

    // Verificar se o ID é 18 e bloqueá-lo
    if (id === 18 || id === "18") {
        console.error('Tentativa de confirmar exclusão da ordem #18 que é inválida/hardcoded');
        showErrorMessage('Ordem #18 não está disponível para exclusão. Por favor, selecione outra ordem.');
        $('#deleteModal').modal('hide');
        return;
    }

    // TODO: Implementar chamada à API para excluir a ordem
    fetch(`${API_URL}/api/orders/${id}`, {
        method: 'DELETE',
        headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : '',
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (response.ok) {
            showSuccessMessage('Ordem excluída com sucesso!');
            updateTable();
        } else {
            showErrorMessage('Erro ao excluir ordem. Tente novamente.');
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        showErrorMessage('Erro ao excluir ordem. Tente novamente.');
    });

    $('#deleteModal').modal('hide');
}

// Função para cancelar exclusão
function cancelDelete() {
    $('#deleteModal').modal('hide');
}

// Função para salvar edição
function saveEdit() {
    const formData = new FormData(document.getElementById('editForm'));

    // TODO: Implementar chamada à API para salvar a ordem
    fetch(`${API_URL}/api/orders/${id}`, {
        method: 'PUT',
        body: formData,
        headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : ''
        }
    })
    .then(response => {
        if (response.ok) {
            showSuccessMessage('Ordem atualizada com sucesso!');
            updateTable();
        } else {
            showErrorMessage('Erro ao atualizar ordem. Tente novamente.');
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        showErrorMessage('Erro ao atualizar ordem. Tente novamente.');
    });

    $('#editModal').modal('hide');
}

// Função para cancelar edição
function cancelEdit() {
    $('#editModal').modal('hide');
}

// Função para carregar dados da ordem
function loadOrderData(id) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (id === 18 || id === "18") {
        console.error('Tentativa de carregar dados da ordem #18 que é inválida/hardcoded');
        showErrorMessage('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    // TODO: Implementar chamada à API para carregar os dados da ordem
    fetch(`${API_URL}/api/orders/${id}`, {
        method: 'GET',
        headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : ''
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('clientName').value = data.clientName;
        document.getElementById('date').value = data.date;
        document.getElementById('status').value = data.status;
    })
    .catch(error => {
        console.error('Erro:', error);
        showErrorMessage('Erro ao carregar dados da ordem. Tente novamente.');
    });
}

// Função para atualizar tabela
function updateTable() {
    fetch(`${API_URL}/api/orders`, {
        method: 'GET',
        headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : '',
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Erro ao atualizar tabela');
        }
        return response.json();
    })
    .then(data => {
        const table = $('#ordersTable').DataTable();
        table.clear();
        table.rows.add(data);
        table.draw();
    })
    .catch(error => {
        console.error('Erro:', error);
        showErrorMessage('Erro ao atualizar tabela. Tente novamente.');
    });
}

// Função para exibir mensagem de sucesso
function showSuccessMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message message-success';
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(messageDiv);
        }, 300);
    }, 3000);
}

// Função para exibir mensagem de erro
function showErrorMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message message-error';
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(messageDiv);
        }, 300);
    }, 3000);
}

// Adicionar animação de saída
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Função para abrir o modal de atribuição de prestador
function openAssignModal(event) {
    const orderId = event.target.closest('[data-order-id]').dataset.orderId;

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de atribuir prestador à ordem #18 que é inválida/hardcoded');
        showErrorMessage('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    document.getElementById('assignOrderId').value = orderId;
    const modal = new bootstrap.Modal(document.getElementById('assignProviderModal'));
    modal.show();
}

// Função para atribuir prestador à ordem
function assignProvider() {
    const form = document.getElementById('assignProviderForm');
    const orderId = document.getElementById('assignOrderId').value;

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de atribuir prestador à ordem #18 que é inválida/hardcoded');
        showErrorMessage('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
        return;
    }

    const providerId = document.getElementById('providerId').value;
    const note = document.getElementById('assignmentNote').value;

    if (!providerId) {
        showToast('error', 'Por favor, selecione um prestador.');
        return;
    }

    fetch(`/api/orders/${orderId}/assign`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            providerId: providerId,
            note: note
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Erro ao atribuir prestador');
        }
        return response.json();
    })
    .then(data => {
        showToast('success', 'Prestador atribuído com sucesso!');
        bootstrap.Modal.getInstance(document.getElementById('assignProviderModal')).hide();
        setTimeout(() => window.location.reload(), 1500);
    })
    .catch(error => {
        console.error('Erro:', error);
        showToast('error', 'Erro ao atribuir prestador. Por favor, tente novamente.');
    });
}

// Inicialização
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    loadOrders();
    loadMetrics();
});

// Configurar event listeners
function setupEventListeners() {
    elements.applyFilter.addEventListener('click', () => {
        updateFilters();
        loadOrders();
    });

    elements.refreshBtn.addEventListener('click', () => {
        loadOrders();
        loadMetrics();
    });

    // Paginação
    elements.pagination.addEventListener('click', (e) => {
        if (e.target.classList.contains('page-link')) {
            e.preventDefault();
            const page = parseInt(e.target.dataset.page);
            if (page && page !== config.currentPage) {
                config.currentPage = page;
                loadOrders();
            }
        }
    });
}

// Atualizar filtros
function updateFilters() {
    config.filters = {
        status: elements.statusFilter.value,
        branch: elements.branchFilter.value,
        date: elements.dateFilter.value
    };
    config.currentPage = 1; // Resetar para primeira página
}

// Carregar ordens
async function loadOrders() {
    try {
        showLoading();

        // Obter a filial do usuário logado
        const userBranchID = localStorage.getItem('user_branch');
        const userRole = localStorage.getItem('user_role');

        // Construir filtros
        const filtros = {
            status: config.filters.status,
            branch: config.filters.branch,
            date: config.filters.date
        };

        // Se for usuário de filial, adicionar filtro por filial
        if (userRole === 'filial' && userBranchID) {
            filtros.branch = userBranchID;
        }

        // Carregar ordens usando a mesma função que a criação
        await carregarOrdens(config.currentPage, filtros, true);
    } catch (error) {
        console.error('Erro ao carregar ordens:', error);
        showError('Erro ao carregar ordens de manutenção. Tente novamente mais tarde.');
    } finally {
        hideLoading();
    }
}
}

// Carregar métricas
async function loadMetrics() {
    try {
        const response = await fetch('/api/orders/metrics');
        const data = await response.json();

        if (data.success) {
            updateMetrics(data.metrics);
        }
    } catch (error) {
        console.error('Erro ao carregar métricas:', error);
    }
}

// Renderizar ordens
function renderOrders(orders) {
    if (!orders || orders.length === 0) {
        elements.ordersList.innerHTML = `
            <div class="text-center py-5">
                <div class="empty-state">
                    <i class="bx bx-clipboard text-warning mb-3"></i>
                    <h5>Nenhuma ordem encontrada</h5>
                    <p class="text-muted">Tente ajustar os filtros ou criar uma nova ordem</p>
                </div>
            </div>
        `;
        return;
    }

    // Obter informações do usuário
    const userBranchID = localStorage.getItem('user_branch');
    const userRole = localStorage.getItem('user_role');

    // Filtrar as ordens - última camada de segurança
    const finalOrders = orders.filter(order => {
        // Nunca mostrar a ordem #18
        if (order.id === 18 || order.id === "18") return false;

        // Remover ordens com texto de teste no título ou descrição
        const isTestOrder = (
            (order.title && /teste|test|simulação|simulacao|exemplo|example|demo/i.test(order.title)) ||
            (order.description && /teste|test|simulação|simulacao|exemplo|example|demo/i.test(order.description))
        );
        if (isTestOrder) return false;

        // Usuários de filial só veem suas próprias ordens
        if (userRole === 'filial' && userBranchID) {
            // Se o branch_id da ordem não corresponder ao do usuário, não mostrar
            if (order.branch_id !== parseInt(userBranchID) && order.branch_id !== userBranchID) {
                return false;
            }
        }

        return true;
    });

    if (finalOrders.length === 0) {
        elements.ordersList.innerHTML = `
            <div class="text-center py-5">
                <div class="empty-state">
                    <i class="bx bx-clipboard text-warning mb-3"></i>
                    <h5>Nenhuma ordem encontrada após filtragem de segurança</h5>
                    <p class="text-muted">Você só pode visualizar ordens da sua filial</p>
                </div>
            </div>
        `;
        return;
    }

    const ordersHTML = finalOrders.map(order => `
        <div class="order-item" data-id="${order.id}">
            <div class="order-header">
                <div class="order-id">#${order.id}</div>
                <div class="order-status status-${order.status}">
                    <i class="bx ${getStatusIcon(order.status)}"></i>
                    ${formatStatus(order.status)}
                </div>
            </div>
            <div class="order-body">
                <h5 class="order-title">${order.title}</h5>
                <div class="order-meta">
                    <div class="meta-item">
                        <i class="bx bx-building"></i>
                        <span>${order.branch || '-'}</span>
                    </div>
                    <div class="meta-item">
                        <i class="bx bx-calendar"></i>
                        <span>${formatDate(order.created_at)}</span>
                    </div>
                </div>
            </div>
            <div class="order-footer">
                <button class="btn btn-sm btn-shell-yellow view-details" data-id="${order.id}">
                    <i class="bx bx-show"></i> Detalhes
                </button>
                <a href="/orders/${order.id}/edit" class="btn btn-sm btn-warning">
                    <i class="bx bx-edit"></i> Editar
                </a>
            </div>
        </div>
    `).join('');

    elements.ordersList.innerHTML = ordersHTML;

    // Adicionar event listeners para os botões de detalhes
    document.querySelectorAll('.view-details').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            window.location.href = `/orders/${orderId}`;
        });
    });
}

// Atualizar paginação
function updatePagination(total, pages) {
    config.totalPages = pages;

    let paginationHTML = '';

    // Botão anterior
    paginationHTML += `
        <li class="page-item ${config.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${config.currentPage - 1}">
                <i class="bx bx-chevron-left"></i>
            </a>
        </li>
    `;

    // Números das páginas
    for (let i = 1; i <= pages; i++) {
        if (i === 1 || i === pages || (i >= config.currentPage - 2 && i <= config.currentPage + 2)) {
            paginationHTML += `
                <li class="page-item ${i === config.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        } else if (i === config.currentPage - 3 || i === config.currentPage + 3) {
            paginationHTML += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
    }

    // Botão próximo
    paginationHTML += `
        <li class="page-item ${config.currentPage === pages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${config.currentPage + 1}">
                <i class="bx bx-chevron-right"></i>
            </a>
        </li>
    `;

    elements.pagination.innerHTML = paginationHTML;
}

// Atualizar métricas
function updateMetrics(metrics) {
    elements.metrics.pending.textContent = metrics.pending;
    elements.metrics.inProgress.textContent = metrics.in_progress;
    elements.metrics.completed.textContent = metrics.completed;
    elements.metrics.total.textContent = metrics.total;
}

// Funções auxiliares
function showLoading() {
    elements.ordersList.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-3 text-warning">Carregando ordens...</p>
        </div>
    `;
}

function hideLoading() {
    // O conteúdo será substituído quando as ordens forem carregadas
}

function showError(message) {
    elements.ordersList.innerHTML = `
        <div class="text-center py-5">
            <div class="error-state">
                <i class="bx bx-error-circle text-danger mb-3"></i>
                <h5>${message}</h5>
                <button class="btn btn-shell-yellow mt-3" onclick="loadOrders()">
                    <i class="bx bx-refresh me-1"></i> Tentar Novamente
                </button>
            </div>
        </div>
    `;
}

function getStatusIcon(status) {
    switch(status) {
        case 'pendente': return 'bx-time-five';
        case 'em_andamento': return 'bx-loader-alt';
        case 'concluida': return 'bx-check-circle';
        case 'cancelada': return 'bx-x-circle';
        default: return 'bx-help-circle';
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}
