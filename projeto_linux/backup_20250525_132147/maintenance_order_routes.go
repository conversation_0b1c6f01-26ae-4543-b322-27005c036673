package routes

import (
	"tradicao/internal/controllers"

	"github.com/gin-gonic/gin"
)

// SetupMaintenanceOrderRoutes configura as rotas de ordens de manutenção
func SetupMaintenanceOrderRoutes(router *gin.Engine, controller *controllers.MaintenanceOrderController) {
	maintenanceOrders := router.Group("/api/maintenance-orders")
	{
		// Rotas básicas CRUD
		maintenanceOrders.GET("/branch/:branchID", controller.GetByBranch)
		maintenanceOrders.GET("/:id", controller.GetByID)
		maintenanceOrders.POST("", controller.Create)
		maintenanceOrders.PUT("/:id", controller.Update)
		maintenanceOrders.DELETE("/:id", controller.Delete)

		// Rotas de consulta
		maintenanceOrders.GET("/date-range", controller.GetByDateRange)
		maintenanceOrders.GET("/status/:status", controller.GetByStatus)
		maintenanceOrders.GET("/provider/:providerID", controller.GetByProvider)
		maintenanceOrders.GET("/metrics", controller.GetMetrics)
		maintenanceOrders.GET("/:orderID/notes", controller.GetNotes)

		// Rotas de atualização específicas
		maintenanceOrders.POST("/:id/materials", controller.AddMaterial)
		maintenanceOrders.PUT("/:id/status/:status", controller.UpdateStatus)
		maintenanceOrders.PUT("/:id/priority/:priority", controller.UpdatePriority)
		maintenanceOrders.PUT("/:id/due-date/:dueDate", controller.UpdateDueDate)
		maintenanceOrders.PUT("/:id/total-cost", controller.UpdateTotalCost)

		// Rota para administração de segurança
		maintenanceOrders.POST("/admin/remove-test-orders", controller.RemoveTestOrders)
	}
}
