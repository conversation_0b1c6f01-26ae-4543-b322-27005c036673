<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - Sistema Tradição</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Lightbox CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css" rel="stylesheet">
    
    <style>
        .attachment-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .attachment-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .attachment-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .attachment-icon {
            width: 100%;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 4rem;
            color: #6c757d;
        }
        
        .attachment-info {
            margin-top: 10px;
        }
        
        .attachment-title {
            font-weight: bold;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .attachment-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .attachment-actions {
            margin-top: 10px;
        }
        
        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .upload-zone {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-zone:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        
        .upload-zone.dragover {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .category-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .version-badge {
            background: #17a2b8;
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-images"></i> {{.title}}</h1>
                    <div>
                        <button class="btn btn-primary" onclick="openUploadModal()">
                            <i class="fas fa-upload"></i> Upload
                        </button>
                        <button class="btn btn-outline-secondary" onclick="refreshGallery()">
                            <i class="fas fa-sync-alt"></i> Atualizar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h5><i class="fas fa-file"></i> Total de Arquivos</h5>
                    <h2 id="totalFiles">-</h2>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h5><i class="fas fa-hdd"></i> Tamanho Total</h5>
                    <h2 id="totalSize">-</h2>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h5><i class="fas fa-image"></i> Imagens</h5>
                    <h2 id="imageCount">-</h2>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h5><i class="fas fa-file-alt"></i> Documentos</h5>
                    <h2 id="documentCount">-</h2>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Tipo</label>
                    <select class="form-select" id="filterType" onchange="applyFilters()">
                        <option value="">Todos os tipos</option>
                        <option value="image">Imagens</option>
                        <option value="document">Documentos</option>
                        <option value="video">Vídeos</option>
                        <option value="audio">Áudios</option>
                        <option value="other">Outros</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Categoria</label>
                    <select class="form-select" id="filterCategory" onchange="applyFilters()">
                        <option value="">Todas as categorias</option>
                        <option value="technical">Técnico</option>
                        <option value="evidence">Evidência</option>
                        <option value="invoice">Nota Fiscal</option>
                        <option value="contract">Contrato</option>
                        <option value="certificate">Certificado</option>
                        <option value="manual">Manual</option>
                        <option value="other">Outros</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Tags</label>
                    <input type="text" class="form-control" id="filterTags" placeholder="Buscar por tags..." onkeyup="applyFilters()">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Opções</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="onlyLatest" onchange="applyFilters()">
                        <label class="form-check-label" for="onlyLatest">
                            Apenas versões mais recentes
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Galeria de Anexos -->
        <div class="row" id="attachmentGallery">
            <!-- Anexos serão carregados aqui via JavaScript -->
        </div>

        <!-- Loading -->
        <div class="text-center" id="loadingSpinner" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
        </div>

        <!-- Empty State -->
        <div class="text-center" id="emptyState" style="display: none;">
            <i class="fas fa-folder-open fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">Nenhum anexo encontrado</h4>
            <p class="text-muted">Faça upload de arquivos para começar</p>
            <button class="btn btn-primary" onclick="openUploadModal()">
                <i class="fas fa-upload"></i> Fazer Upload
            </button>
        </div>
    </div>

    <!-- Modal de Upload -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-upload"></i> Upload de Arquivos</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Upload Zone -->
                    <div class="upload-zone" id="uploadZone">
                        <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
                        <h5>Arraste arquivos aqui ou clique para selecionar</h5>
                        <p class="text-muted">Máximo 50MB por arquivo</p>
                        <input type="file" id="fileInput" multiple style="display: none;">
                    </div>

                    <!-- Upload Form -->
                    <form id="uploadForm" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Categoria *</label>
                                <select class="form-select" id="uploadCategory" required>
                                    <option value="">Selecione uma categoria</option>
                                    <option value="technical">Técnico</option>
                                    <option value="evidence">Evidência</option>
                                    <option value="invoice">Nota Fiscal</option>
                                    <option value="contract">Contrato</option>
                                    <option value="certificate">Certificado</option>
                                    <option value="manual">Manual</option>
                                    <option value="other">Outros</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Título</label>
                                <input type="text" class="form-control" id="uploadTitle" placeholder="Título do arquivo">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">Tags</label>
                                <input type="text" class="form-control" id="uploadTags" placeholder="tag1, tag2, tag3">
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="uploadPublic">
                                    <label class="form-check-label" for="uploadPublic">
                                        Arquivo público
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">Descrição</label>
                                <textarea class="form-control" id="uploadDescription" rows="3" placeholder="Descrição do arquivo"></textarea>
                            </div>
                        </div>
                    </form>

                    <!-- Upload Progress -->
                    <div id="uploadProgress" style="display: none;">
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="uploadStatus">Preparando upload...</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="uploadButton" onclick="startUpload()">
                        <i class="fas fa-upload"></i> Fazer Upload
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    <script src="/static/js/attachment-gallery.js"></script>
    
    <script>
        // Configurações globais
        window.attachmentConfig = {
            entityType: '{{.entity_type}}',
            entityId: '{{.entity_id}}'
        };
        
        // Inicializar galeria quando página carregar
        document.addEventListener('DOMContentLoaded', function() {
            loadAttachments();
            loadStats();
        });
    </script>
</body>
</html>
