{{ define "calendarios/calendar_flip.html" }}
<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/calendar-flip.css">

    <!-- Scripts do calendário -->
    <script src="/static/js/unified_unified_unified_unified_unified_orders.js" defer></script>

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        /* Estas definições são específicas desta página e não estão no CSS global */
        body {
            background-color: #1a1a1a;
            background-image: linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111),
                linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111);
            background-size: 60px 60px;
            background-position: 0 0, 30px 30px;
        }

        /* Botão para voltar ao dashboard */
        .btn-back-to-dashboard {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #FFCC00;
            color: #000;
            padding: 8px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 100;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-back-to-dashboard:hover {
            background-color: #ffcd00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        /* Estilos específicos para ordens urgentes */
        .urgent-styled {
            border-left: 4px solid #dc3545 !important;
            background-color: rgba(220, 53, 69, 0.1);
            box-shadow: 0 3px 6px rgba(220, 53, 69, 0.2);
            position: relative;
        }

        .urgent-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #dc3545;
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 3px 6px;
            border-radius: 3px;
            letter-spacing: 0.5px;
        }

        /* Estilos para o modal de seleção de ordens */
        .order-selection-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .order-selection-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .order-selection-content {
            background-color: #1e1e1e;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            padding: 20px;
            position: relative;
            border: 2px solid #FFCC00;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .order-selection-modal.active .order-selection-content {
            transform: scale(1);
        }

        .close-selection-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #fff;
            font-size: 20px;
            cursor: pointer;
        }

        .order-option {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid #333;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .order-option:hover {
            background-color: #2a2a2a;
        }

        .order-option:last-child {
            border-bottom: none;
        }

        .order-indicator {
            width: 10px;
            height: 100%;
            margin-right: 15px;
            border-radius: 3px;
        }

        .order-details {
            flex: 1;
        }

        .order-details h4 {
            margin: 0 0 8px 0;
            color: #FFCC00;
        }

        .order-details p {
            margin: 5px 0;
            color: #ccc;
        }

        .container-fluid {
            padding: 20px;
        }

        /* Para corrigir possíveis problemas de escala */
        .calendar-dashboard-container {
            transform: scale(1);
            transform-origin: center top;
        }

        /* Estilos para a seção de ordens em destaque */
        .featured-orders-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: rgba(30, 30, 30, 0.9);
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
            border: 1px solid #444;
        }

        .featured-orders-section h4 {
            color: var(--shell-yellow);
            margin-bottom: 15px;
            border-bottom: 2px solid var(--shell-yellow);
            padding-bottom: 10px;
        }

        .featured-orders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .featured-orders-category h5 {
            font-size: 1rem;
            margin-bottom: 10px;
            color: #eee;
        }

        .featured-orders-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .featured-order-item {
            display: flex;
            align-items: center;
            background: #222;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: 1px solid #333;
        }

        .featured-order-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .featured-order-indicator {
            width: 10px;
            height: 40px;
            border-radius: 3px;
            margin-right: 10px;
        }

        .featured-order-indicator.urgent {
            background-color: #dc3545;
        }

        .featured-order-indicator.scheduled {
            background-color: #0d6efd;
        }

        .featured-order-content {
            flex-grow: 1;
        }

        .featured-order-content h6 {
            margin: 0 0 5px 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: #ddd;
        }

        .featured-order-details {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            font-size: 0.8rem;
            color: #bbb;
        }

        /* Aumentando o tamanho do calendário após remover o painel lateral */
        .calendar-dashboard-layout {
            grid-template-columns: 1fr !important;
        }

        .calendar-days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
        }

        .calendar-day {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 64px;
            background: rgba(40,40,40,0.95);
            border-radius: 12px;
            color: #f8f9fa;
            font-family: 'Rajdhani', sans-serif;
            font-size: 1.25rem;
            font-weight: 600;
            box-shadow: 0 2px 8px #0002;
            position: relative;
            cursor: pointer;
            transition: background 0.18s, box-shadow 0.18s, color 0.18s;
            border: 2px solid transparent;
        }

        .calendar-day.today {
            border: 2px solid var(--shell-yellow, #FDB813);
            background: linear-gradient(120deg, #333 60%, #FDB81322 100%);
            color: #FDB813;
            box-shadow: 0 0 0 3px #FDB81344;
        }

        .calendar-day.selected {
            border: 2px solid #ED1C24;
            background: linear-gradient(120deg, #333 60%, #ED1C2422 100%);
            color: #ED1C24;
            box-shadow: 0 0 0 3px #ED1C2444;
        }

        .calendar-day:hover {
            background: #222;
            color: #FDB813;
            box-shadow: 0 4px 16px #0004;
            z-index: 2;
        }

        .calendar-day .day-number {
            display: block;
            text-align: center;
            width: 2.2em;
            height: 2.2em;
            line-height: 2.2em;
            border-radius: 50%;
            background: none;
            margin-bottom: 2px;
            font-size: 1.15em;
            font-weight: 700;
        }

        .calendar-day.today .day-number {
            background: var(--shell-yellow, #FDB813);
            color: #222;
        }

        .calendar-day.selected .day-number {
            background: #ED1C24;
            color: #fff;
        }

        .calendar-day .order-dots {
            display: flex;
            gap: 2px;
            margin-top: 2px;
            justify-content: center;
            align-items: center;
        }

        .calendar-day .order-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            border: 1.5px solid #222;
            box-shadow: 0 1px 3px #0006;
        }

        .order-dot.Aguardando { background: #17a2b8; }
        .order-dot.Atrasado { background: #dc3545; }
        .order-dot.Urgente { background: #ED1C24; }
        .order-dot.Aprovado { background: #28a745; }
        .order-dot.Concluído { background: #6c757d; }
        .order-dot.Programado { background: #0d6efd; }

        .calendar-day .order-count-badge {
            background: #FDB813;
            color: #222;
            font-size: 0.85em;
            font-weight: bold;
            border-radius: 8px;
            padding: 0 6px;
            margin-left: 4px;
            margin-top: 2px;
            box-shadow: 0 1px 3px #0006;
        }

        @media (max-width: 767.98px) {
            .calendar-day {
                font-size: 1rem;
                height: 44px;
                min-width: 36px;
                padding: 2px;
            }
            .calendar-day .day-number {
                width: 1.7em;
                height: 1.7em;
                line-height: 1.7em;
                font-size: 1em;
            }
        }

        /* Efeito visual ao clicar/tocar nos cards */
        .card-hover-effect:active, .card-hover-effect:focus {
            box-shadow: 0 0 0 3px var(--shell-yellow, #FDB813), 0 2px 8px rgba(0,0,0,0.25);
            outline: none;
            background: rgba(253,184,19,0.05);
            transition: box-shadow 0.15s, background 0.15s;
        }
        @media (max-width: 767.98px) {
            .order-title { font-size: 1rem; }
            .order-info { font-size: 0.95rem; }
            .service-order-item { min-height: 70px; padding: 10px 6px; }
            .side-orders-section { padding: 6px 2px; }
        }

        /* ... existing code ... */
        #orders-panel .toggle-btn {
            border: none;
            outline: none;
            background: var(--shell-yellow, #FDB813);
            color: #222;
            font-size: 1.2rem;
            box-shadow: 0 2px 8px #0002;
            transition: background 0.2s;
        }
        #orders-panel .toggle-btn:active, #orders-panel .toggle-btn:focus {
            background: #fffbe6;
            color: #222;
        }
        #orders-panel-content.collapsed {
            max-height: 0 !important;
            opacity: 0 !important;
            pointer-events: none;
        }
        @media (max-width: 767.98px) {
            #orders-panel .toggle-btn {
                right: 10px !important;
                top: 10px !important;
                width: 32px;
                height: 32px;
                font-size: 1rem;
            }
        }

        /* Melhorias na harmonização visual */
        .orders-container {
            background: rgba(34, 34, 34, 0.8);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            border-radius: 15px;
            border: 1px solid rgba(236, 28, 36, 0.18);
            transition: all 0.3s ease;
        }

        .order-panel-header {
            background: linear-gradient(135deg, rgba(237, 28, 36, 0.85) 0%, rgba(34, 34, 34, 1) 100%);
            border-radius: 14px 14px 0 0;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .order-panel-title {
            font-family: 'Rajdhani', sans-serif;
            font-weight: 700;
            font-size: 1.8rem;
            margin: 0;
            letter-spacing: 1px;
            color: #FDB813;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        .order-panel-toggle {
            position: absolute;
            right: 15px;
            background: rgba(34, 34, 34, 0.7);
            border: 1px solid rgba(253, 184, 19, 0.5);
            color: #FDB813;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .order-panel-toggle:hover {
            background: rgba(253, 184, 19, 0.2);
            transform: scale(1.05);
        }

        .order-panels-container {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            padding: 16px;
        }

        .order-panel {
            flex: 1;
            min-width: 300px;
            background: rgba(24, 24, 24, 0.9);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .order-panel-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 14px;
            justify-content: space-between;
            align-items: center;
        }

        .order-panel-title-inner {
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            font-size: 1.3rem;
            margin: 0;
            color: var(--shell-yellow);
        }

        .order-filter-label {
            font-size: 0.75rem;
            opacity: 0.7;
            margin-bottom: 4px;
        }

        .order-filter-badge {
            font-size: 0.7rem;
            border-radius: 50px;
            padding: 1px 8px;
            margin-left: 5px;
            background: rgba(253, 184, 19, 0.2);
            color: #FDB813;
        }

        .service-orders-list {
            max-height: 450px;
            overflow-y: auto;
            padding-right: 8px;
            margin-top: 6px;
        }

        .service-orders-list::-webkit-scrollbar {
            width: 6px;
        }

        .service-orders-list::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        .service-orders-list::-webkit-scrollbar-thumb {
            background: rgba(253, 184, 19, 0.3);
            border-radius: 10px;
        }

        .service-orders-list::-webkit-scrollbar-thumb:hover {
            background: rgba(253, 184, 19, 0.5);
        }

        /* Ajustes responsivos para mobile */
        @media (max-width: 768px) {
            .order-panel-title {
                font-size: 1.5rem;
            }

            .order-panel {
                min-width: 100%;
            }

            .order-panel-filters {
                justify-content: center;
            }
        }

        /* Animação de toggle */
        .panel-collapsed .service-orders-list {
            display: none;
        }

        .panel-collapsed .order-panel-filters {
            display: none;
        }

        .panel-collapsed .order-panel {
            padding: 8px;
        }

        .rotate-icon {
            transition: transform 0.3s ease;
        }

        .rotate-icon.rotated {
            transform: rotate(180deg);
        }

        /* Melhorias no visual do calendário */
        .calendar-container {
            background: rgba(30, 30, 30, 0.8);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(236, 28, 36, 0.18);
            margin-bottom: 20px;
        }

        /* Estilo para notificações */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            transform: translateY(-100px);
            opacity: 0;
            transition: transform 0.3s, opacity 0.3s;
        }

        .notification.show {
            transform: translateY(0);
            opacity: 1;
        }

        .notification.info {
            background-color: #17a2b8;
            color: white;
        }

        .notification.error {
            background-color: #dc3545;
            color: white;
        }

        .notification.warning {
            background-color: #ffc107;
            color: #212529;
        }

        .notification.success {
            background-color: #28a745;
            color: white;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-title {
            font-family: 'Rajdhani', sans-serif;
            font-weight: 700;
            font-size: 1.6rem;
            color: var(--shell-yellow);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin: 0;
        }

        .calendar-controls {
            display: flex;
            gap: 10px;
        }

        .calendar-controls button {
            background: rgba(34, 34, 34, 0.7);
            border: 1px solid rgba(253, 184, 19, 0.5);
            color: #FDB813;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-controls button:hover {
            background: rgba(253, 184, 19, 0.2);
            transform: scale(1.05);
        }

        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
            margin-bottom: 6px;
        }

        .calendar-weekday {
            text-align: center;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            padding: 8px 0;
            color: var(--shell-yellow);
            background: rgba(34, 34, 34, 0.7);
            border-radius: 8px;
        }

        .calendar-days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
        }

        .calendar-day {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 70px;
            background: rgba(40, 40, 40, 0.9);
            border-radius: 10px;
            transition: all 0.3s ease;
            overflow: hidden;
            cursor: pointer;
        }

        .calendar-day:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            background: rgba(50, 50, 50, 0.9);
        }

        .calendar-day.today {
            background: linear-gradient(135deg, rgba(237, 28, 36, 0.2) 0%, rgba(40, 40, 40, 0.9) 100%);
            border: 1px solid rgba(237, 28, 36, 0.5);
        }

        .calendar-day.different-month {
            opacity: 0.5;
            background: rgba(30, 30, 30, 0.7);
        }

        .day-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-top: 5px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .calendar-day.today .day-number {
            background: rgba(237, 28, 36, 0.7);
            color: white;
        }

        .calendar-day:hover .day-number {
            background: rgba(253, 184, 19, 0.2);
            color: var(--shell-yellow);
        }

        .day-content {
            width: 100%;
            padding: 2px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .day-dots {
            display: flex;
            justify-content: center;
            gap: 3px;
            margin-top: 3px;
        }

        .day-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
        }

        .day-dot.priority {
            background-color: #dc3545;
        }

        .day-dot.scheduled {
            background-color: #17a2b8;
        }

        .day-event-count {
            position: absolute;
            top: 5px;
            right: 5px;
            min-width: 18px;
            height: 18px;
            border-radius: 9px;
            background: rgba(237, 28, 36, 0.8);
            color: white;
            font-size: 0.7rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsividade do calendário */
        @media (max-width: 768px) {
            .calendar-container {
                padding: 10px;
            }

            .calendar-weekday {
                font-size: 0.8rem;
                padding: 5px 0;
            }

            .calendar-day {
                min-height: 60px;
            }

            .day-number {
                width: 25px;
                height: 25px;
                font-size: 0.9rem;
            }
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configuração adicional após o DOM ser carregado
            setupOrdersToggle();
            setupOrderClick();
            setupFilters();
        });

        // Configura o toggle para o painel lateral
        function setupOrdersToggle() {
            const toggleOrdersBtn = document.getElementById('toggleOrdersBtn');
            const ordersPanel = document.getElementById('orders-panel-content');
            const toggleIcon = document.getElementById('toggleIcon');

            if (toggleOrdersBtn) {
                toggleOrdersBtn.addEventListener('click', function() {
                    console.log('Toggle button clicked');
                    if (ordersPanel.classList.contains('collapsed')) {
                        // Expandir o painel
                        ordersPanel.classList.remove('collapsed');
                        toggleIcon.classList.remove('fa-chevron-down');
                        toggleIcon.classList.add('fa-chevron-up');
                        toggleOrdersBtn.setAttribute('aria-expanded', 'true');
                        console.log('Panel expanded');
                    } else {
                        // Recolher o painel
                        ordersPanel.classList.add('collapsed');
                        toggleIcon.classList.remove('fa-chevron-up');
                        toggleIcon.classList.add('fa-chevron-down');
                        toggleOrdersBtn.setAttribute('aria-expanded', 'false');
                        console.log('Panel collapsed');
                    }
                });
            } else {
                console.error('Toggle button not found');
            }
        }

        // Configura o evento de clique em ordens para navegação
        function setupOrderClick() {
            document.querySelectorAll('.service-order-item').forEach(item => {
                item.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-order-id');
                    if (orderId) {
                        // Navegar para a página de detalhes da ordem
                        window.location.href = `/ordens/detalhes/${orderId}`;
                    }
                });
            });
        }

        // Configura filtros para as ordens
        function setupFilters() {
            const statusFilter = document.getElementById('status-filter');
            const priorityFilter = document.getElementById('priority-filter');

            if (statusFilter) {
                statusFilter.addEventListener('change', applyFilters);
            }

            if (priorityFilter) {
                priorityFilter.addEventListener('change', applyFilters);
            }
        }

        // Aplica filtros às ordens visíveis
        function applyFilters() {
            const statusFilter = document.getElementById('status-filter').value;
            const priorityFilter = document.getElementById('priority-filter').value;

            document.querySelectorAll('.service-order-item').forEach(item => {
                const status = item.getAttribute('data-status');
                const priority = item.getAttribute('data-priority');

                let statusMatch = statusFilter === 'all' || status === statusFilter;
                let priorityMatch = priorityFilter === 'all' || priority === priorityFilter;

                if (statusMatch && priorityMatch) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Função auxiliar para formatar datas
        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;
            return date.toLocaleDateString('pt-BR');
        }
    </script>
</head>

<body>
    <!-- Botão para voltar ao dashboard -->
    <a href="/dashboard" class="btn-back-to-dashboard">
        <i class="fas fa-tachometer-alt"></i> Voltar ao Dashboard
    </a>

    <div class="container-fluid">
        <!-- Modal para detalhes do card -->
        <div class="service-detail-modal" id="service-detail-modal">
            <div class="service-detail-content">
                <button class="service-detail-close" id="close-modal">
                    <i class="fas fa-times"></i>
                </button>
                <h2 id="modal-title" class="mb-4" style="color: var(--shell-yellow);">Detalhes do Serviço</h2>
                <div id="modal-content">
                    <!-- Conteúdo dinâmico será inserido aqui -->
                </div>
            </div>
        </div>

        <div class="calendar-dashboard-container">
            <!-- Card Giratório 3D -->
            <div class="flip-card-container">
                <div class="flip-card" id="flip-card">
                    <div class="flip-card-inner">
                        <!-- Frente: Calendário -->
                        <div class="flip-card-front">
                            <div class="pump-border calendar-dashboard-layout">
                                <div class="calendar-container">
                                    <div class="calendar-header">
                                        <h2 class="calendar-title">Calendário de Manutenções</h2>
                                        <div class="calendar-controls">
                                            <button id="prevMonthBtn" aria-label="Mês anterior" title="Mês anterior">
                                                <i class="fas fa-chevron-left"></i>
                                            </button>
                                            <button id="todayBtn" aria-label="Hoje" title="Ir para hoje">
                                                <i class="fas fa-calendar-day"></i>
                                            </button>
                                            <button id="nextMonthBtn" aria-label="Próximo mês" title="Próximo mês">
                                                <i class="fas fa-chevron-right"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="calendar-month-year">
                                        <span id="currentMonthYear" class="fw-bold">Junho 2023</span>
                                    </div>

                                    <div class="calendar-weekdays">
                                        <div class="calendar-weekday">Dom</div>
                                        <div class="calendar-weekday">Seg</div>
                                        <div class="calendar-weekday">Ter</div>
                                        <div class="calendar-weekday">Qua</div>
                                        <div class="calendar-weekday">Qui</div>
                                        <div class="calendar-weekday">Sex</div>
                                        <div class="calendar-weekday">Sáb</div>
                                    </div>

                                    <div class="calendar-days" id="calendarDays">
                                        <!-- Dias do calendário serão inseridos via JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Verso: Detalhes da Ordem -->
                        <div class="flip-card-back">
                            <div class="glass-panel">
                                <div class="panel-header">
                                    <button class="shell-btn back-to-calendar" id="back-to-calendar">
                                        <i class="fas fa-arrow-left"></i> Voltar ao Calendário
                                    </button>
                                    <h3 id="order-panel-title">Detalhes da Ordem</h3>
                                </div>

                                <!-- Conteúdo da ordem -->
                                <div class="order-detail-panel">
                                    <!-- Seção principal de detalhes da ordem -->
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h4 id="order-title" class="text-dark"><span
                                                    id="order-maintenance-type">Manutenção Preventiva</span></h4>

                                            <!-- Prioridade movida para a seção de detalhes principais -->
                                            <div class="d-flex align-items-center mb-2">
                                                <span id="order-priority" class="badge bg-warning text-white">Prioridade
                                                    Média</span>
                                            </div>

                                            <div class="mb-3">
                                                <h5 class="text-muted fs-6">Detalhes da Manutenção</h5>
                                                <p id="order-description">Troca de filtro de combustível da bomba 2.
                                                    Cliente relatou baixa pressão durante abastecimento. Peças já
                                                    disponíveis no estoque local.</p>
                                            </div>

                                            <div class="mb-3">
                                                <h5 class="text-muted fs-6">Filial</h5>
                                                <p id="order-location"><i class="fas fa-map-marker-alt text-danger"></i>
                                                    Posto Shell Ipiranga</p>
                                            </div>

                                            <div class="mb-3">
                                                <h5 class="text-muted fs-6">Equipamento</h5>
                                                <p id="order-equipment"><i class="fas fa-tools text-secondary"></i>
                                                    Bomba de Combustível #2</p>
                                            </div>
                                        </div>

                                        <div class="col-md-4 border-start">
                                            <h5 class="text-muted fs-6">Responsável</h5>
                                            <p id="order-responsible"><i class="fas fa-user text-primary"></i> Carlos
                                                Silva</p>

                                            <h5 class="text-muted fs-6 mt-3">Status Atual</h5>
                                            <p id="order-status"><span class="badge bg-info">Aguardando Aprovação</span>
                                            </p>

                                            <h5 class="text-muted fs-6 mt-3">Tempo Estimado</h5>
                                            <p id="order-time"><i class="far fa-clock"></i> 2 horas</p>

                                            <!-- Botões abaixo do tempo estimado, sem alterar a disposição original -->
                                            <div class="d-flex justify-content-center mt-4">
                                                <button class="shell-btn" id="print-order"
                                                    onclick="printOrderDetails()"><i class="fas fa-print"></i>
                                                    Imprimir</button>
                                            </div>
                                            <div class="d-flex justify-content-between mt-3">
                                                <button class="shell-btn shell-btn-success me-2" id="approve-order"
                                                    onclick="approveOrder()"><i class="fas fa-check-circle"></i>
                                                    Aprovar</button>
                                                <button class="shell-btn shell-btn-danger" id="reject-order"
                                                    onclick="rejectOrder()"><i class="fas fa-times-circle"></i>
                                                    Reprovar</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Detalhes Ordem -->
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <h5 class="text-muted border-bottom pb-2">Detalhes Ordem</h5>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 180px;">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-history"></i>
                                                </div>
                                                <h6>Histórico</h6>
                                                <div class="text-center mt-3">
                                                    <p class="mt-2 mb-3">Bomba de Combustível #2</p>
                                                    <p class="text-muted">
                                                        <i class="fas fa-eye"></i> Clique para ver
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 180px;">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-file-invoice-dollar"></i>
                                                </div>
                                                <h6>Custos</h6>
                                                <div class="text-center" style="margin-top: 20px;">
                                                    <div style="font-size: 1rem;">Total</div>
                                                    <div class="text-warning"
                                                        style="font-size: 1.5rem; font-weight: bold; padding: 10px;">R$
                                                        1.042,50</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 180px;">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-comments"></i>
                                                </div>
                                                <h6>Interação</h6>
                                                <div class="text-center mt-4">
                                                    <p class="text-muted">
                                                        <i class="fas fa-comment-dots"></i> Clique para interagir
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 180px;">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-calendar-check"></i>
                                                </div>
                                                <h6>Cronograma</h6>
                                                <div class="text-center mt-3">
                                                    <div class="timeline-simple">
                                                        <p class="mb-1"><i class="fas fa-play-circle text-success"></i>
                                                            20/03/2025 - 14:00h</p>
                                                        <p><i class="fas fa-stop-circle text-danger"></i> 20/03/2025 -
                                                            16:00h</p>
                                                    </div>
                                                    <p class="text-muted mt-2">
                                                        <i class="fas fa-clock"></i> Clique para ver
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Espaço reservado para conteúdo adicional -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Botão Legendas removido daqui e movido para dentro do flip-instruction -->
            </div>
            <!-- A seção Agenda foi removida conforme solicitado -->

            <!-- Modal de Legendas -->
            <div class="modal fade" id="legendModal" tabindex="-1" aria-labelledby="legendModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content legend-modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="legendModalLabel">Legendas - Status das Ordens</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Fechar"></button>
                        </div>
                        <div class="modal-body">
                            <div class="legend-grid">
                                <div class="legend-item">
                                    <span class="legend-badge bg-info"></span>
                                    <span class="legend-text">Aguardando Aprovação</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-success"></span>
                                    <span class="legend-text">Aprovado</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-success" style="opacity: 0.7;"></span>
                                    <span class="legend-text">Aprovado - Manutenção Programada</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-danger"></span>
                                    <span class="legend-text">Reprovado</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-danger"
                                        style="background-color: #dc3545 !important;"></span>
                                    <span class="legend-text">Urgente</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-warning"></span>
                                    <span class="legend-text">Média Prioridade</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-danger"></span>
                                    <span class="legend-text">Alta Prioridade</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-primary"></span>
                                    <span class="legend-text">Programada</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-secondary"></span>
                                    <span class="legend-text">Concluída</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PAINEL DE ORDENS DE MANUTENÇÃO -->
    <div class="scheduled-services-container mt-4" id="orders-panel">
        <div class="glass-container p-0">
            <div class="row justify-content-center align-items-center m-0">
                <div class="col-12">
                    <div class="d-flex justify-content-center align-items-center position-relative">
                        <h2 class="text-center fw-bold py-3 flex-grow-1" style="color: var(--shell-yellow); letter-spacing: 1px; font-family: 'Rajdhani', sans-serif; font-size: 2rem; text-shadow: 0 2px 8px #000; background: rgba(30,30,30,0.7); border-radius: 12px 12px 0 0;">
                            Ordens de Manutenção
                        </h2>
                        <button id="toggleOrdersBtn" class="shell-btn toggle-btn ms-2" aria-label="Expandir/Recolher painel de ordens" style="position: absolute; right: 18px; top: 50%; transform: translateY(-50%); background: var(--shell-yellow); color: #222; border-radius: 50%; width: 38px; height: 38px; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 8px #0002; transition: background 0.2s; z-index: 10;">
                            <i id="toggleIcon" class="fas fa-chevron-up" style="transition: transform 0.3s;"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="row g-3 m-0 flex-wrap orders-panel-content" id="orders-panel-content" style="transition: max-height 0.4s cubic-bezier(.4,2,.6,1), opacity 0.3s; overflow: hidden; max-height: 2000px; opacity: 1;">
                <!-- Coluna Esquerda: Ordens Prioritárias -->
                <div class="col-12 col-md-6 d-flex flex-column">
                    <div class="side-orders-section bg-dark rounded-3 p-2 h-100 shadow-sm">
                        <div class="d-flex justify-content-between align-items-center mb-2 flex-wrap gap-2">
                            <h5 class="text-warning mb-0 d-flex align-items-center gap-2"><i class="fas fa-exclamation-circle"></i> Ordens Prioritárias</h5>
                            <div class="btn-group btn-group-sm" role="group" aria-label="Filtros Prioritários">
                                <button type="button" class="shell-btn filter-btn-priority active" data-filter="all">Todos</button>
                                <button type="button" class="shell-btn filter-btn-priority" data-filter="Aguardando">Aguardando</button>
                                <button type="button" class="shell-btn filter-btn-priority" data-filter="Atrasado">Atrasado</button>
                                <button type="button" class="shell-btn filter-btn-priority" data-filter="Urgente">Urgente</button>
                            </div>
                        </div>
                        <div class="priority-orders-list">
                        {{ range .PriorityOrders }}
                            <div class="service-order-item mb-2 shadow-sm rounded-2 card-hover-effect" data-status="{{ .Status }}" data-order-id="{{ .ID }}" tabindex="0" aria-label="Ordem {{ .OrderNumber }}: {{ .Title }}" title="Clique para ver detalhes da ordem {{ .OrderNumber }}" style="cursor:pointer; min-height: 90px;">
                                <div class="order-status-indicator {{ statusToClass .Status }}"></div>
                                <div class="order-content">
                                    <h5 class="order-title d-flex align-items-center gap-2">
                                        <i class="bi {{ priorityToIcon .Priority }} text-warning" aria-hidden="true"></i>
                                        {{ defaultString .Title "(Sem título)" }} (Ordem #{{ defaultString .OrderNumber "-" }})
                                    </h5>
                                    <div class="order-info small text-wrap">
                                        <span class="order-date me-2"><i class="fas fa-calendar-day"></i> {{ if .StartDate }}{{ formatDate .StartDate }}{{ else }}-{{ end }}</span>
                                        <span class="order-location me-2"><i class="fas fa-map-marker-alt text-danger"></i> {{ defaultString .StationName "(Filial não informada)" }}</span>
                                        <span class="order-priority"><span class="badge bg-danger">{{ formatPriority .Priority }}</span></span>
                                    </div>
                                    <div class="order-status mt-1">
                                        <span class="badge bg-{{ statusToClass .Status }}">{{ formatStatus .Status }}</span>
                                    </div>
                                </div>
                            </div>
                        {{ else }}
                            <div class="text-muted">Nenhuma ordem prioritária encontrada.</div>
                        {{ end }}
                        </div>
                    </div>
                </div>
                <!-- Coluna Direita: Ordens Programadas/Aprovadas -->
                <div class="col-12 col-md-6 d-flex flex-column">
                    <div class="side-orders-section bg-dark rounded-3 p-2 h-100 shadow-sm">
                        <div class="d-flex justify-content-between align-items-center mb-2 flex-wrap gap-2">
                            <h5 class="text-success mb-0 d-flex align-items-center gap-2"><i class="fas fa-check-circle"></i> Ordens Programadas/Aprovadas</h5>
                            <div class="btn-group btn-group-sm" role="group" aria-label="Filtros Programadas/Aprovadas">
                                <button type="button" class="shell-btn filter-btn-scheduled active" data-filter="all">Todos</button>
                                <button type="button" class="shell-btn filter-btn-scheduled" data-filter="Aprovado">Aprovado</button>
                                <button type="button" class="shell-btn filter-btn-scheduled" data-filter="Concluído">Concluído</button>
                                <button type="button" class="shell-btn filter-btn-scheduled" data-filter="Programado">Programado</button>
                            </div>
                        </div>
                        <div class="scheduled-orders-list">
                        {{ range .ScheduledOrders }}
                            <div class="service-order-item mb-2 shadow-sm rounded-2 card-hover-effect" data-status="{{ .Status }}" data-order-id="{{ .ID }}" tabindex="0" aria-label="Ordem {{ .OrderNumber }}: {{ .Title }}" title="Clique para ver detalhes da ordem {{ .OrderNumber }}" style="cursor:pointer; min-height: 90px;">
                                <div class="order-status-indicator {{ statusToClass .Status }}"></div>
                                <div class="order-content">
                                    <h5 class="order-title d-flex align-items-center gap-2">
                                        <i class="bi {{ priorityToIcon .Priority }} text-primary" aria-hidden="true"></i>
                                        {{ defaultString .Title "(Sem título)" }} (Ordem #{{ defaultString .OrderNumber "-" }})
                                    </h5>
                                    <div class="order-info small text-wrap">
                                        <span class="order-date me-2"><i class="fas fa-calendar-day"></i> {{ if .StartDate }}{{ formatDate .StartDate }}{{ else }}-{{ end }}</span>
                                        <span class="order-location me-2"><i class="fas fa-map-marker-alt text-danger"></i> {{ defaultString .StationName "(Filial não informada)" }}</span>
                                        <span class="order-priority"><span class="badge bg-primary">{{ formatPriority .Priority }}</span></span>
                                    </div>
                                    <div class="order-status mt-1">
                                        <span class="badge bg-{{ statusToClass .Status }}">{{ formatStatus .Status }}</span>
                                    </div>
                                </div>
                            </div>
                        {{ else }}
                            <div class="text-muted">Nenhuma ordem programada/aprovada encontrada.</div>
                        {{ end }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
{{ end }}