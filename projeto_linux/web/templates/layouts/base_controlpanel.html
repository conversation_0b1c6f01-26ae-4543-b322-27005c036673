{{ define "layouts/base_controlpanel.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/animations.css">

    <!-- Estilos adicionais específicos -->
    <link rel="stylesheet" href="/static/css/calendar-flip.css">
    <link rel="stylesheet" href="/static/css/pump.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    {{ if eq .page "calendario" }}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css">
    {{ end }}

    <style>
        :root {
            --shell-red: #ED1C24;
            --shell-yellow: #f4b31d;
            --dark-gray: #1a1a1a;
            --medium-gray: #333;
            --light-gray: #444;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--dark-gray);
            color: #fff;
            min-height: 100vh;
        }

        .dashboard-container {
            max-width: 1500px;
            margin: 0 auto;
            padding: 15px;
        }

        /* Layout principal com duas colunas */
        .dashboard-main {
            display: flex;
            min-height: calc(100vh - 60px);
        }

        /* Navbar superior */
        .shell-navbar {
            background: linear-gradient(90deg, #1a1a1a, #222);
            border-bottom: 3px solid var(--shell-yellow);
            padding: 5px 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .shell-navbar .navbar-brand {
            color: var(--shell-yellow);
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .shell-logo {
            width: 40px;
            height: 40px;
            margin-right: 10px;
        }

        .shell-navbar .navbar-nav .nav-link {
            color: #fff;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .shell-navbar .navbar-nav .nav-link:hover {
            color: var(--shell-yellow);
            transform: translateY(-2px);
        }

        .user-dropdown .dropdown-toggle {
            display: flex;
            align-items: center;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            padding: 5px 15px;
            color: #fff;
            transition: all 0.3s ease;
        }

        .user-dropdown .dropdown-toggle:hover {
            background: rgba(0, 0, 0, 0.5);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--shell-red);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            font-weight: bold;
            box-shadow: 0 0 10px rgba(237, 28, 36, 0.5);
        }

        .notification-bell {
            position: relative;
            margin-right: 20px;
        }

        .notification-count {
            position: absolute;
            top: -5px;
            right: -10px;
            background: var(--shell-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 0 5px rgba(237, 28, 36, 0.7);
            animation: pulse 2s infinite;
        }

        /* Conteúdo principal */
        .content-area {
            flex: 1;
            padding: 20px;
            background: var(--medium-gray);
            border-radius: 10px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--light-gray);
            overflow-y: auto;
            min-height: calc(100vh - 100px);
        }

        /* Animações */
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .dashboard-main {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
                height: auto;
                margin-bottom: 15px;
            }
            .content-area {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Barra de navegação superior -->
    <nav class="navbar navbar-expand-lg shell-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <img src="/static/images/shell-logo.png" alt="Shell Logo" class="shell-logo">
                <span>Rede Tradição Shell</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <!-- Links de navegação principais -->
                    <li class="nav-item">
                        <a class="nav-link{{ if eq .page "dashboard" }} active{{ end }}" href="/dashboard">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link{{ if eq .page "calendario" }} active{{ end }}" href="/calendario">
                            <i class="fas fa-calendar-alt"></i> Calendário
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link{{ if eq .page "orders" }} active{{ end }}" href="/orders">
                            <i class="fas fa-clipboard-list"></i> Ordens
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link{{ if eq .page "relatorios" }} active{{ end }}" href="/relatorios">
                            <i class="fas fa-chart-bar"></i> Relatórios
                        </a>
                    </li>

                    <!-- Ícone de notificação -->
                    <li class="nav-item me-3">
                        <a class="nav-link notification-bell" href="#" id="notificationBell">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count">3</span>
                        </a>
                    </li>

                    <!-- Dropdown do usuário -->
                    <li class="nav-item dropdown user-dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar">{{ if .User }}{{ slice .User.Name 0 1 }}{{ else }}U{{ end }}</div>
                            <span>{{ if .User }}{{ .User.Name }}{{ else }}Usuário{{ end }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="/minha-conta"><i class="fas fa-user-circle"></i> Minha Conta</a></li>
                            <li><a class="dropdown-item" href="/configuracoes"><i class="fas fa-cog"></i> Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt"></i> Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Container principal do dashboard -->
    <div class="dashboard-container">
        <!-- Área de conteúdo principal -->
        <div class="content-area">
            {{ block "content" . }}{{ end }}
        </div>
    </div>

    <!-- Painel de notificações (inicialmente oculto) -->
    <div class="notification-panel" id="notificationPanel" style="display: none;">
        <div class="notification-panel-header">
            <h5>Notificações</h5>
            <button id="closeNotifications" class="close-btn"><i class="fas fa-times"></i></button>
        </div>
        <div class="notification-list">
            <div class="notification-item unread">
                <div class="notification-icon bg-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">Manutenção Urgente</div>
                    <div class="notification-text">Bomba 3 do Posto Shell Paulista requer manutenção imediata.</div>
                    <div class="notification-time">2 horas atrás</div>
                </div>
            </div>
            <div class="notification-item unread">
                <div class="notification-icon bg-warning">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">Ordem Aprovada</div>
                    <div class="notification-text">Sua ordem de serviço #12345 foi aprovada.</div>
                    <div class="notification-time">5 horas atrás</div>
                </div>
            </div>
            <div class="notification-item">
                <div class="notification-icon bg-info">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">Lembrete de Manutenção</div>
                    <div class="notification-text">Manutenção preventiva agendada para amanhã.</div>
                    <div class="notification-time">1 dia atrás</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts necessários -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/dashboard.js"></script>

    {{ if eq .page "calendario" }}
    <!-- Scripts para o calendário -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
    <script src="/static/js/unified_unified_unified_unified_orders.js"></script>
    {{ end }}

    <script>
        // Script para o painel de notificações
        $(document).ready(function() {
            $('#notificationBell').click(function(e) {
                e.preventDefault();
                $('#notificationPanel').toggle();
                // Remove a contagem de notificações não lidas ao clicar
                $('.notification-count').hide();
            });

            $('#closeNotifications').click(function() {
                $('#notificationPanel').hide();
            });

            // Fecha painel ao clicar fora
            $(document).click(function(e) {
                if (!$(e.target).closest('#notificationPanel, #notificationBell').length) {
                    $('#notificationPanel').hide();
                }
            });
        });
    </script>
</body>
</html>
{{ end }}