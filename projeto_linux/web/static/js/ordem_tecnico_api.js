/**
 * API para interação com o backend - Ordem Técnico
 * Sistema Tradição - Módulo de Técnicos
 */

// Classe para gerenciar as chamadas à API
class OrdemTecnicoAPI {
    constructor() {
        this.baseUrl = '/api/orders';
        this.csrfToken = this.getCSRFToken();
    }

    // Obtém o token CSRF do cookie
    getCSRFToken() {
        const name = 'csrf_token=';
        const decodedCookie = decodeURIComponent(document.cookie);
        const cookieArray = decodedCookie.split(';');

        for (let i = 0; i < cookieArray.length; i++) {
            let cookie = cookieArray[i].trim();
            if (cookie.indexOf(name) === 0) {
                return cookie.substring(name.length, cookie.length);
            }
        }
        return '';
    }

    // Configuração padrão para requisições
    getRequestConfig(method, data = null) {
        const config = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': this.csrfToken
            },
            credentials: 'same-origin'
        };

        if (data) {
            config.body = JSON.stringify(data);
        }

        return config;
    }

    // Trata erros de requisição
    handleRequestError(response) {
        if (!response.ok) {
            throw new Error(`Erro na requisição: ${response.status} ${response.statusText}`);
        }
        return response.json();
    }

    // Obtém dados de uma ordem
    async getOrdem(ordemId) {
        try {
            const response = await fetch(`${this.baseUrl}/${ordemId}`, this.getRequestConfig('GET'));
            const data = await this.handleRequestError(response);
            return data;
        } catch (error) {
            console.error('Erro ao obter dados da ordem:', error);
            throw error;
        }
    }

    // Salva dados de manutenção
    async saveManutencao(ordemId, data) {
        try {
            const response = await fetch(
                `${this.baseUrl}/${ordemId}/manutencao`,
                this.getRequestConfig('POST', data)
            );
            return this.handleRequestError(response);
        } catch (error) {
            console.error('Erro ao salvar dados de manutenção:', error);
            throw error;
        }
    }

    // Salva dados de custos
    async saveCustos(ordemId, data) {
        try {
            const response = await fetch(
                `${this.baseUrl}/${ordemId}/custos`,
                this.getRequestConfig('POST', data)
            );
            return this.handleRequestError(response);
        } catch (error) {
            console.error('Erro ao salvar dados de custos:', error);
            throw error;
        }
    }

    // Salva dados de cronograma
    async saveCronograma(ordemId, data) {
        try {
            const response = await fetch(
                `${this.baseUrl}/${ordemId}/cronograma`,
                this.getRequestConfig('POST', data)
            );
            return this.handleRequestError(response);
        } catch (error) {
            console.error('Erro ao salvar dados de cronograma:', error);
            throw error;
        }
    }

    // Salva mensagem de chat
    async saveChatMessage(ordemId, message) {
        try {
            const data = {
                ordem_id: ordemId,
                mensagem: message
            };

            const response = await fetch(
                `${this.baseUrl}/${ordemId}/chat`,
                this.getRequestConfig('POST', data)
            );
            return this.handleRequestError(response);
        } catch (error) {
            console.error('Erro ao enviar mensagem de chat:', error);
            throw error;
        }
    }
}

// Exporta a classe para uso global
window.OrdemTecnicoAPI = new OrdemTecnicoAPI();
