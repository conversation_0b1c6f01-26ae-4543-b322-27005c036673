const CACHE_NAME = 'shell-manutencao-cache-v1';
const URLS_TO_CACHE = [
  '/',
  '/login',
  '/dashboard',
  '/static/css/main.css',
  '/static/css/calendar-flip.css',
  '/static/js/main.js',
  '/static/js/calendar-flip.js',
  '/static/images/logo-shell.png'
];

// Instalação do Service Worker
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('Cache aberto');
        return cache.addAll(URLS_TO_CACHE);
      })
  );
});

// Ativação do Service Worker
self.addEventListener('activate', function(event) {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            // Deletar caches antigos
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Interceptação de requisições
self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Cache hit - retorna a resposta do cache
        if (response) {
          return response;
        }

        // Clone da requisição
        const fetchRequest = event.request.clone();

        return fetch(fetchRequest).then(
          function(response) {
            // Requisições sem sucesso ou para endpoints de API não são cacheadas
            if(!response || response.status !== 200 || response.type !== 'basic' || event.request.url.includes('/api/')) {
              return response;
            }

            // Clone da resposta
            const responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then(function(cache) {
                cache.put(event.request, responseToCache);
              });

            return response;
          }
        );
      })
  );
});

// Eventos de sincronização em background
self.addEventListener('sync', function(event) {
  if (event.tag === 'sync-maintenance-orders') {
    event.waitUntil(syncMaintenanceOrders());
  }
});

// Eventos de push (notificações)
self.addEventListener('push', function(event) {
  if (event.data) {
    const data = event.data.json();
    const title = data.title || 'Shell Manutenção';
    const options = {
      body: data.message || 'Atualização recebida',
      icon: '/static/images/icons/icon-192x192.png',
      badge: '/static/images/icons/badge-72x72.png',
      data: data.url || '/'
    };

    event.waitUntil(
      self.registration.showNotification(title, options)
    );
  }
});

// Evento de clique na notificação
self.addEventListener('notificationclick', function(event) {
  event.notification.close();

  event.waitUntil(
    clients.openWindow(event.notification.data)
  );
});

// Função para sincronizar ordens de manutenção pendentes
function syncMaintenanceOrders() {
  return fetch('/api/maintenance/sync') // Exclusão da ordem #18 feita no backend
    .then(response => response.json())
    .then(data => {
      if (data.status === 'success') {
        // Filtro da ordem removido - feito no backend
        console.log('Sincronização concluída:', data);
      }
    })
    .catch(error => {
      console.error('Erro na sincronização:', error);
    });
}