package permissions

import (
	"log"
	"strings"
	"sync"
)

// Service é um serviço mínimo de permissões para compatibilidade
// NOTA: Este é um stub mínimo - o sistema unificado é o principal
type Service struct {
	config *Config
	mutex  sync.RWMutex
}

// REMOVIDO: Tipos duplicados - usar types.go

// NewService cria um novo serviço de permissões carregando do arquivo YAML
func NewService(configPath string) (*Service, error) {
	log.Printf("[PERMISSIONS] Carregando configuração de: %s", configPath)

	// Tentar carregar do arquivo YAML primeiro
	config, err := LoadConfig(configPath)
	if err != nil {
		log.Printf("[PERMISSIONS] ERRO ao carregar YAML: %v", err)
		log.Printf("[PERMISSIONS] Usando configuração de fallback hardcoded...")

		// Fallback para configuração hardcoded se o YAML falhar
		config = &Config{
			PublicPages: []string{"/", "/login", "/logout", "/acesso-negado", "/change-password"},
			PublicAPIs:  []string{"/api/auth/login", "/api/auth/logout", "/api/auth/change-password"},
			Roles: map[string]RoleConfig{
				"admin": {
					Description: "Administrador do sistema",
					Pages:       []string{"*"},
					APIs:        []string{"*"},
				},
				"gerente": {
					Description: "Gerente",
					Pages:       []string{"/dashboard", "/ordens", "/equipamentos", "/filiais", "/calendario", "/relatorios"},
					APIs:        []string{"/api/ordens", "/api/equipamentos", "/api/filiais", "/api/dashboard/metrics", "/api/user/me"},
				},
				"financeiro": {
					Description: "Financeiro",
					Pages:       []string{"/dashboard", "/ordens", "/calendario", "/financeiro", "/relatorios"},
					APIs:        []string{"/api/ordens", "/api/calendar-events", "/api/user/me", "/api/dashboard/metrics"},
				},
				"tecnico": {
					Description: "Técnico",
					Pages:       []string{"/ordemtecnico", "/calendario", "/dashboard", "/minha-conta"},
					APIs:        []string{"/api/ordens", "/api/calendar-events", "/api/user/me", "/api/maintenance-orders"},
				},
				"prestador": {
					Description: "Prestador de serviços",
					Pages:       []string{"/calendario", "/ordens", "/dashboard", "/minha-conta"},
					APIs:        []string{"/api/ordens", "/api/calendar-events", "/api/user/me"},
				},
				"filial": {
					Description: "Usuário de filial",
					Pages:       []string{"/dashboard", "/calendario", "/orders", "/minha-conta", "/manutencao", "/nova-manutencao"},
					APIs:        []string{"/api/ordens", "/api/calendar-events", "/api/user/me", "/api/equipments", "/api/maintenance"},
				},
			},
		}
	} else {
		log.Printf("[PERMISSIONS] Configuração YAML carregada com sucesso!")
		// Imprimir configuração para debug
		DumpConfig(config)
	}

	return &Service{
		config: config,
	}, nil
}

// HasPermission verifica se um papel tem permissão para acessar um recurso
func (s *Service) HasPermission(role string, resourcePath string, permType PermissionType) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Admin tem acesso a tudo
	if role == "admin" {
		return true
	}

	// Verificar recursos públicos
	if permType == PagePermission {
		for _, publicPage := range s.config.PublicPages {
			if publicPage == resourcePath {
				return true
			}
		}
	} else if permType == APIPermission {
		for _, publicAPI := range s.config.PublicAPIs {
			if publicAPI == resourcePath {
				return true
			}
		}
	}

	// Verificar permissões específicas do papel
	roleConfig, exists := s.config.Roles[role]
	if !exists {
		log.Printf("AVISO: Papel não encontrado: %s", role)
		return false
	}

	var permissions []string
	if permType == PagePermission {
		permissions = roleConfig.Pages
	} else {
		permissions = roleConfig.APIs
	}

	for _, permission := range permissions {
		if permission == "*" {
			return true
		}

		// Normalizar caminhos para comparação (remover barra inicial se presente)
		normalizedPermission := strings.TrimPrefix(permission, "/")
		normalizedResourcePath := strings.TrimPrefix(resourcePath, "/")

		if normalizedPermission == normalizedResourcePath {
			log.Printf("[PERMISSIONS-DEBUG] ✅ PERMISSÃO ENCONTRADA: %s (role: %s) pode acessar %s", role, permission, resourcePath)
			return true
		}
	}

	return false
}

// GetPermittedRoles retorna os papéis que têm permissão para acessar um recurso
func (s *Service) GetPermittedRoles(resourcePath string, permType PermissionType) []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	var permittedRoles []string

	for role := range s.config.Roles {
		if s.HasPermission(role, resourcePath, permType) {
			permittedRoles = append(permittedRoles, role)
		}
	}

	return permittedRoles
}

// GetConfig retorna a configuração do serviço
func (s *Service) GetConfig() *Config {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.config
}

// REMOVIDO: HasResourcePermission - usar sistema unificado
// Use permissions.GetGlobalUnifiedService().HasResourcePermission() em vez disso
