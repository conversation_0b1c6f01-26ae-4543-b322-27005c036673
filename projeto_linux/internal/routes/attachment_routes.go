package routes

import (
	"net/http"

	"tradicao/internal/handlers"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupAttachmentRoutes configura as rotas para anexos
func SetupAttachmentRoutes(router *gin.Engine, attachmentHandler *handlers.AttachmentHandler) {
	// Grupo de rotas para anexos
	api := router.Group("/api/attachments")
	api.Use(middleware.AuthMiddleware()) // Requer autenticação

	// Rota de teste sem autenticação (APENAS PARA DESENVOLVIMENTO)
	testAPI := router.Group("/api/test-attachments")
	{
		// Estatísticas sem autenticação
		testAPI.GET("/stats", attachmentHandler.GetStats)

		// Listar anexos sem autenticação
		testAPI.GET("", attachmentHandler.GetAttachments)

		// Buscar anexo específico sem autenticação
		testAPI.GET("/:id", attachmentHandler.GetAttachment)
	}

	// Rotas principais
	{
		// Upload de arquivo
		api.POST("/upload", attachmentHandler.UploadFile)

		// Listar anexos por entidade
		api.GET("", attachmentHandler.GetAttachments)

		// Buscar anexo específico
		api.GET("/:id", attachmentHandler.GetAttachment)

		// Download de arquivo
		api.GET("/:id/download", attachmentHandler.DownloadFile)

		// Thumbnail de imagem
		api.GET("/:id/thumbnail", attachmentHandler.GetThumbnail)

		// Preview de imagem (mesmo que download para imagens)
		api.GET("/:id/preview", attachmentHandler.DownloadFile)

		// Deletar anexo
		api.DELETE("/:id", attachmentHandler.DeleteAttachment)

		// Estatísticas
		api.GET("/stats", attachmentHandler.GetStats)
	}

	// Rotas para versionamento
	versions := api.Group("/:id/versions")
	{
		// Listar versões de um arquivo
		versions.GET("", func(c *gin.Context) {
			// TODO: Implementar GetVersions no handler
			c.JSON(200, gin.H{"message": "Em desenvolvimento"})
		})

		// Criar nova versão
		versions.POST("", func(c *gin.Context) {
			// TODO: Implementar CreateNewVersion no handler
			c.JSON(200, gin.H{"message": "Em desenvolvimento"})
		})

		// Marcar versão como atual
		versions.PUT("/:version_id/latest", func(c *gin.Context) {
			// TODO: Implementar MarkAsLatestVersion no handler
			c.JSON(200, gin.H{"message": "Em desenvolvimento"})
		})
	}

	// Rotas web para interface de anexos
	web := router.Group("/attachments")
	web.Use(middleware.AuthMiddleware())
	{
		// Página de galeria de anexos
		web.GET("/gallery", func(c *gin.Context) {
			entityType := c.Query("entity_type")
			entityID := c.Query("entity_id")

			c.HTML(200, "attachments/gallery.html", gin.H{
				"title":       "Galeria de Anexos",
				"entity_type": entityType,
				"entity_id":   entityID,
			})
		})

		// Página de upload
		web.GET("/upload", func(c *gin.Context) {
			entityType := c.Query("entity_type")
			entityID := c.Query("entity_id")

			c.HTML(200, "attachments/upload.html", gin.H{
				"title":       "Upload de Arquivos",
				"entity_type": entityType,
				"entity_id":   entityID,
			})
		})

		// Página de gerenciamento
		web.GET("/manage", func(c *gin.Context) {
			c.HTML(200, "attachments/manage.html", gin.H{
				"title": "Gerenciar Anexos",
			})
		})
	}
}

// SetupLegacyAttachmentRoutes configura rotas de compatibilidade com sistema antigo
func SetupLegacyAttachmentRoutes(router *gin.Engine, attachmentHandler *handlers.AttachmentHandler) {
	// Rotas legadas para compatibilidade
	legacy := router.Group("/api")
	legacy.Use(middleware.AuthMiddleware())

	// Redirecionamentos para manter compatibilidade
	{
		// Upload legado
		legacy.POST("/upload", func(c *gin.Context) {
			// Redirecionar para nova rota
			c.Redirect(301, "/api/attachments/upload")
		})

		// Download legado
		legacy.GET("/download/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.Redirect(301, "/api/attachments/"+id+"/download")
		})

		// Anexos de ordem de manutenção (compatibilidade) - usando maintenance-orders para evitar conflito
		legacy.GET("/maintenance-orders/:order_id/attachments", func(c *gin.Context) {
			orderID := c.Param("order_id")
			c.Redirect(301, "/api/attachments?entity_type=order&entity_id="+orderID)
		})

		legacy.POST("/maintenance-orders/:order_id/attachments", func(c *gin.Context) {
			// Redirecionar para upload com parâmetros
			orderID := c.Param("order_id")
			c.Request.URL.Path = "/api/attachments/upload"
			c.Request.Form.Set("entity_type", "order")
			c.Request.Form.Set("entity_id", orderID)
			c.Request.Form.Set("category", "evidence")
			attachmentHandler.UploadFile(c)
		})
	}
}

// AttachmentRoutesConfig configuração para rotas de anexos
type AttachmentRoutesConfig struct {
	MaxFileSize        int64    // Tamanho máximo do arquivo em bytes
	AllowedTypes       []string // Tipos MIME permitidos
	UploadPath         string   // Caminho para upload
	ThumbnailPath      string   // Caminho para thumbnails
	EnableVersioning   bool     // Se versionamento está habilitado
	EnablePublicAccess bool     // Se acesso público está habilitado
}

// DefaultAttachmentConfig retorna configuração padrão
func DefaultAttachmentConfig() *AttachmentRoutesConfig {
	return &AttachmentRoutesConfig{
		MaxFileSize: 50 * 1024 * 1024, // 50MB
		AllowedTypes: []string{
			// Imagens
			"image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp",
			// Documentos
			"application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			"application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
			"text/plain", "application/rtf",
			// Vídeos
			"video/mp4", "video/avi", "video/quicktime", "video/x-msvideo",
			// Áudios
			"audio/mpeg", "audio/wav", "audio/flac", "audio/aac",
			// Compactados
			"application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
		},
		UploadPath:         "uploads/attachments",
		ThumbnailPath:      "uploads/attachments/thumbnails",
		EnableVersioning:   true,
		EnablePublicAccess: false,
	}
}

// SetupAttachmentRoutesWithConfig configura rotas com configuração personalizada
func SetupAttachmentRoutesWithConfig(router *gin.Engine, attachmentHandler *handlers.AttachmentHandler, config *AttachmentRoutesConfig) {
	// Middleware para validar configurações
	validateConfig := func() gin.HandlerFunc {
		return func(c *gin.Context) {
			// Aqui você pode adicionar validações baseadas na configuração
			// Por exemplo, verificar tamanho do arquivo, tipo MIME, etc.
			c.Next()
		}
	}

	// Aplicar middleware de validação
	api := router.Group("/api/attachments")
	api.Use(middleware.AuthMiddleware())
	api.Use(validateConfig())

	// Configurar rotas normalmente
	SetupAttachmentRoutes(router, attachmentHandler)
}

// AttachmentMiddleware middleware específico para anexos
func AttachmentMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Adicionar headers de segurança
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")

		// Continuar processamento
		c.Next()
	}
}

// FileUploadMiddleware middleware para upload de arquivos
func FileUploadMiddleware(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Limitar tamanho do request
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSize)

		c.Next()
	}
}
