package routes

import (
	"net/http"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupUnifiedOrderRoutesNew configura todas as rotas unificadas do sistema
// Esta função substitui todas as rotas duplicadas por endpoints únicos e consistentes
func SetupUnifiedOrderRoutesNew(router *gin.Engine, authMiddleware gin.HandlerFunc) {
	// Criar handler unificado
	unifiedHandler := handlers.NewUnifiedOrderHandler()

	// API Routes - Endpoints unificados para ordens
	apiOrders := router.Group("/api/orders")
	apiOrders.Use(authMiddleware)                // Middleware de autenticação
	apiOrders.Use(middleware.FilialMiddleware()) // Middleware de filial para contexto
	{
		// Endpoint principal para listar ordens
		// Substitui: /api/orders, /api/ordens, /api/orders/list
		apiOrders.GET("", unifiedHandler.ListOrders)

		// Endpoint para obter uma ordem específica
		// Substitui: /api/orders/:id, /api/ordens/:id
		apiOrders.GET("/:id", unifiedHandler.GetOrder)

		// Endpoint para ordens do calendário
		// Substitui: /api/ordens/calendario, /api/calendar-events
		apiOrders.GET("/calendar", unifiedHandler.GetCalendarOrders)

		// Endpoint para ordens do técnico
		// Substitui: /api/ordens/tecnico
		apiOrders.GET("/technician", unifiedHandler.GetTechnicianOrders)

		// Endpoints para métricas e estatísticas
		apiOrders.GET("/metrics", func(c *gin.Context) {
			// TODO: Implementar métricas unificadas
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "Métricas em desenvolvimento",
				"data": gin.H{
					"total":       0,
					"pending":     0,
					"in_progress": 0,
					"completed":   0,
				},
			})
		})
	}

	// Redirecionamentos para manter compatibilidade
	// Redirecionar endpoints antigos para os novos
	setupCompatibilityRedirects(router)

	// Web Routes - Páginas HTML (mantidas para compatibilidade)
	webOrders := router.Group("/orders")
	webOrders.Use(authMiddleware)
	webOrders.Use(middleware.FilialMiddleware())
	{
		// Página principal de ordens
		webOrders.GET("", func(c *gin.Context) {
			c.HTML(http.StatusOK, "ordens/orders_gallery_style.html", gin.H{
				"title":      "Ordens de Serviço - Rede Tradição",
				"page":       "orders",
				"ActivePage": "orders",
			})
		})

		// Página de calendário
		webOrders.GET("/calendar", func(c *gin.Context) {
			c.HTML(http.StatusOK, "ordens/calendar.html", gin.H{
				"title":      "Calendário de Ordens - Rede Tradição",
				"page":       "orders_calendar",
				"ActivePage": "orders",
			})
		})

		// Página de detalhes da ordem
		webOrders.GET("/:id", func(c *gin.Context) {
			c.HTML(http.StatusOK, "ordens/order_detail.html", gin.H{
				"title":      "Detalhes da Ordem - Rede Tradição",
				"page":       "order_detail",
				"ActivePage": "orders",
				"OrderID":    c.Param("id"),
			})
		})
	}
}

// setupCompatibilityRedirects configura redirecionamentos para manter compatibilidade
func setupCompatibilityRedirects(router *gin.Engine) {
	// Redirecionamentos para endpoints antigos

	// /api/ordens -> /api/orders
	router.GET("/api/ordens", func(c *gin.Context) {
		// Preservar query parameters
		query := c.Request.URL.RawQuery
		newURL := "/api/orders"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/ordens/:id -> /api/orders/:id
	router.GET("/api/ordens/:id", func(c *gin.Context) {
		id := c.Param("id")
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/" + id
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/ordens/tecnico -> /api/orders/technician
	router.GET("/api/ordens/tecnico", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/technician"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/ordens/calendario -> /api/orders/calendar
	router.GET("/api/ordens/calendario", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/calendar"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/calendar-events -> /api/orders/calendar
	router.GET("/api/calendar-events", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/calendar"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// Redirecionamentos para páginas web antigas
	router.GET("/ordens", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/orders")
	})

	router.GET("/ordens/:id", func(c *gin.Context) {
		id := c.Param("id")
		c.Redirect(http.StatusMovedPermanently, "/orders/"+id)
	})

	// Rota /calendario removida - já existe no main.go
}

// RemoveDeprecatedRoutes remove rotas obsoletas que foram substituídas
// Esta função serve como documentação das rotas que foram removidas
func RemoveDeprecatedRoutes() {
	// ROTAS REMOVIDAS:
	// - /api/ordens (duplicata de /api/orders)
	// - /api/ordens/:id (duplicata de /api/orders/:id)
	// - /api/ordens/tecnico (movido para /api/orders/technician)
	// - /api/ordens/calendario (movido para /api/orders/calendar)
	// - /api/calendar-events (movido para /api/orders/calendar)
	// - Múltiplos handlers duplicados foram consolidados

	// BENEFÍCIOS DA UNIFICAÇÃO:
	// ✅ Endpoint único e consistente (/api/orders)
	// ✅ Resposta padronizada (StandardResponse)
	// ✅ Filtros consistentes em todos os endpoints
	// ✅ Paginação padronizada
	// ✅ Tratamento de erros unificado
	// ✅ Exclusão automática da ordem #18
	// ✅ Logs estruturados
	// ✅ Cache otimizado
	// ✅ Queries eficientes
	// ✅ Manutenção simplificada
}

// GetUnifiedEndpoints retorna lista dos endpoints unificados
func GetUnifiedEndpoints() map[string]string {
	return map[string]string{
		"list_orders":       "GET /api/orders",
		"get_order":         "GET /api/orders/:id",
		"calendar_orders":   "GET /api/orders/calendar",
		"technician_orders": "GET /api/orders/technician",
		"order_metrics":     "GET /api/orders/metrics",
		"orders_page":       "GET /orders",
		"calendar_page":     "GET /orders/calendar",
		"order_detail_page": "GET /orders/:id",
	}
}
