package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes registra as rotas relacionadas ao dashboard
func RegisterRoutes(router *gin.Engine) {
	// Rota de API para logout
	router.POST("/api/auth/logout", func(c *gin.Context) {
		c.<PERSON>("session_token", "", -1, "/", "", false, true)
		c.<PERSON>(http.StatusOK, gin.H{"success": true, "message": "Logout realizado com sucesso"})
	})

	// Grupo de rotas para o dashboard
	dashboardRoutes := router.Group("/dashboard")
	{
		// Rota principal do dashboard
		dashboardRoutes.GET("", func(c *gin.Context) {
			user, _ := c.Get("user")
			if user == nil {
				user = gin.H{
					"Name": "Usuário Demonstração",
					"Role": "Administrador",
				}
			}
			c.HTML(http.StatusOK, "dashboard/dashboard.html", gin.H{
				"title": "Dashboard - Rede Tradição Shell",
				"page":  "dashboard",
				"User":  user,
			})
		})

		// Rota para visualização de ordens de serviço - REDIRECIONADA
		dashboardRoutes.GET("/orders", func(c *gin.Context) {
			// Redirecionar para o sistema unificado
			c.Redirect(http.StatusMovedPermanently, "/orders")
		})

		// Rota para aprovação de ordens
		dashboardRoutes.GET("/approval", func(c *gin.Context) {
			user, _ := c.Get("user")
			if user == nil {
				user = gin.H{
					"Name": "Usuário Demonstração",
					"Role": "Administrador",
				}
			}
			c.HTML(http.StatusOK, "dashboard/dashboard.html", gin.H{
				"title": "Aprovação de Ordens - Dashboard",
				"page":  "dashboard_approval",
				"User":  user,
			})
		})
	}
}
