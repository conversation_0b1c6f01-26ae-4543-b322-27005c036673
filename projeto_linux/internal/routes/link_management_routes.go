package routes

import (
	"tradicao/internal/controllers"
	"tradicao/internal/database"
	"tradicao/internal/middleware"
	"tradicao/internal/permissions"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupLinkManagementRoutes configura as rotas para gerenciamento de vínculos
func SetupLinkManagementRoutes(router *gin.Engine, controller *controllers.LinkManagementController, techOrderService *services.TechnicianOrderService) {
	// Grupo de rotas para a API de gerenciamento de vínculos
	apiLinks := router.Group("/api/links")
	apiLinks.Use(middleware.AuthMiddleware())

	// Rotas administrativas (apenas admin, gerente e financeiro)
	adminLinks := apiLinks.Group("")
	adminLinks.Use(permissions.RoleMiddleware("admin", "gerente", "financeiro"))

	// Vínculos entre prestadores e filiais
	adminLinks.GET("/provider-branch", controller.GetAllProviderBranchLinks)
	adminLinks.POST("/provider-branch", controller.LinkProviderToBranch)
	adminLinks.DELETE("/provider-branch/:provider_id/:branch_id", controller.UnlinkProviderFromBranch)
	adminLinks.GET("/branch/:branch_id/providers", controller.GetBranchProviders)

	// Vínculos entre técnicos e filiais
	adminLinks.GET("/technician-branch", controller.GetAllTechnicianBranchLinks)
	adminLinks.POST("/technician-branch", controller.LinkTechnicianToBranch)
	adminLinks.DELETE("/technician-branch/:technician_id/:branch_id", controller.UnlinkTechnicianFromBranch)
	adminLinks.GET("/branch/:branch_id/technicians", controller.GetBranchTechnicians)
	adminLinks.GET("/provider/:provider_id/technicians", controller.GetProviderTechnicians)

	// Configurar controlador de técnicos filtrados
	filteredTechController := controllers.NewFilteredTechnicianController(database.GetGormDB(), controller.GetService())

	// Rota para técnicos filtrados por prestador e filial (acessível para todos os usuários autenticados)
	// Esta rota aceita o parâmetro branch_id na query string para filtrar por filial
	apiLinks.GET("/filtered/provider/:provider_id/technicians", filteredTechController.GetTechniciansByProviderAndBranch)

	// Rota pública para obter técnicos de um prestador (acessível para todos os usuários autenticados)
	// Esta rota é usada no formulário de criação de ordens
	apiLinks.GET("/provider/:provider_id/technicians/public", controller.GetProviderTechnicians)

	// Vínculos entre prestadoras e técnicos
	adminLinks.GET("/provider-technician", controller.GetAllProviderTechnicianLinks)
	adminLinks.POST("/provider-technician", controller.LinkProviderToTechnician)
	adminLinks.DELETE("/provider-technician/:provider_id/:technician_id", controller.UnlinkProviderFromTechnician)
	adminLinks.POST("/provider-technician/inherit-branches", controller.InheritBranchLinks)

	// Configurar controlador de atribuição de técnicos
	techAssignController := controllers.NewTechnicianAssignmentController(techOrderService)

	// Vínculos entre técnicos e ordens
	adminLinks.POST("/technician-order", techAssignController.AssignTechnicianToOrder)
	adminLinks.DELETE("/technician-order/:technician_id/:order_id", techAssignController.UnassignTechnicianFromOrder)
	adminLinks.GET("/order/:order_id/technicians", techAssignController.GetOrderTechnicians)
	// adminLinks.GET("/technician/:technician_id/orders", techAssignController.GetTechnicianOrders) // REMOVIDO - usar /api/orders/technician

	// Rotas para a página de gerenciamento de vínculos foram removidas
}
