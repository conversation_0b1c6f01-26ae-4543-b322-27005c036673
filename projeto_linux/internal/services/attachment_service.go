package services

import (
	"crypto/md5"
	"fmt"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io"
	"log"
	"math/rand"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// AttachmentService interface para serviços de anexos
type AttachmentService interface {
	UploadFile(file multipart.File, header *multipart.FileHeader, request *models.AttachmentUploadRequest, userID uint, userName string, ipAddress string, userAgent string) (*models.AttachmentResponse, error)
	GetByID(id uint) (*models.AttachmentResponse, error)
	GetByEntity(entityType string, entityID uint, filter *models.AttachmentFilter) ([]models.AttachmentResponse, error)
	GetStats(entityType string, entityID uint) (*models.AttachmentStats, error)
	DeleteFile(id uint, userID uint) error
	CreateNewVersion(id uint, file multipart.File, header *multipart.FileHeader, versionNotes string, userID uint, userName string, ipAddress string, userAgent string) (*models.AttachmentResponse, error)
	GetVersions(id uint) ([]models.AttachmentResponse, error)
	MarkAsLatestVersion(id uint) error
	GetDownloadURL(id uint) (string, error)
	GetThumbnailURL(id uint) (string, error)
	GenerateThumbnail(id uint) error
}

// AttachmentServiceImpl implementação do serviço de anexos
type AttachmentServiceImpl struct {
	repo        repository.AttachmentRepository
	uploadPath  string
	maxFileSize int64
	allowedExts map[string]bool
}

// NewAttachmentService cria uma nova instância do serviço
func NewAttachmentService(repo repository.AttachmentRepository) AttachmentService {
	// Configurações padrão
	uploadPath := "uploads/attachments"
	maxFileSize := int64(50 * 1024 * 1024) // 50MB

	// Extensões permitidas
	allowedExts := map[string]bool{
		// Imagens
		".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true, ".webp": true,
		// Documentos
		".pdf": true, ".doc": true, ".docx": true, ".xls": true, ".xlsx": true, ".ppt": true, ".pptx": true,
		".txt": true, ".rtf": true, ".odt": true, ".ods": true, ".odp": true,
		// Vídeos
		".mp4": true, ".avi": true, ".mov": true, ".wmv": true, ".flv": true, ".webm": true,
		// Áudios
		".mp3": true, ".wav": true, ".flac": true, ".aac": true, ".ogg": true,
		// Outros
		".zip": true, ".rar": true, ".7z": true, ".tar": true, ".gz": true,
	}

	// Criar diretório de upload se não existir
	if err := os.MkdirAll(uploadPath, 0755); err != nil {
		log.Printf("Erro ao criar diretório de upload: %v", err)
	}

	return &AttachmentServiceImpl{
		repo:        repo,
		uploadPath:  uploadPath,
		maxFileSize: maxFileSize,
		allowedExts: allowedExts,
	}
}

// UploadFile faz upload de um arquivo
func (s *AttachmentServiceImpl) UploadFile(file multipart.File, header *multipart.FileHeader, request *models.AttachmentUploadRequest, userID uint, userName string, ipAddress string, userAgent string) (*models.AttachmentResponse, error) {
	// Validar arquivo
	if err := s.validateFile(header); err != nil {
		return nil, err
	}

	// Calcular hash do arquivo
	fileHash, err := s.calculateFileHash(file)
	if err != nil {
		return nil, fmt.Errorf("erro ao calcular hash do arquivo: %w", err)
	}

	// Verificar duplicatas
	duplicate, err := s.repo.CheckDuplicate(fileHash)
	if err != nil {
		return nil, fmt.Errorf("erro ao verificar duplicatas: %w", err)
	}

	if duplicate != nil {
		log.Printf("Arquivo duplicado detectado: %s (Hash: %s)", header.Filename, fileHash)
		// Retornar o arquivo existente em vez de fazer upload novamente
		return s.attachmentToResponse(duplicate), nil
	}

	// Gerar nome único para o arquivo
	fileName := s.generateUniqueFileName(header.Filename)
	filePath := filepath.Join(s.uploadPath, fileName)

	// Salvar arquivo no disco
	if err := s.saveFileToDisk(file, filePath); err != nil {
		return nil, fmt.Errorf("erro ao salvar arquivo: %w", err)
	}

	// Determinar tipo do arquivo
	fileType := s.determineFileType(header.Filename)

	// Criar registro no banco
	attachment := &models.Attachment{
		OriginalName:    header.Filename,
		FileName:        fileName,
		FilePath:        filePath,
		FileSize:        header.Size,
		MimeType:        header.Header.Get("Content-Type"),
		FileHash:        fileHash,
		Type:            fileType,
		Category:        request.Category,
		Title:           request.Title,
		Description:     request.Description,
		Tags:            request.Tags,
		IsPublic:        request.IsPublic,
		EntityType:      request.EntityType,
		EntityID:        request.EntityID,
		UploadedByID:    userID,
		UploadedByName:  userName,
		IPAddress:       ipAddress,
		UserAgent:       userAgent,
		Version:         1,
		IsLatestVersion: true,
	}

	// Processar imagem se necessário
	if fileType == models.AttachmentTypeImage {
		if err := s.processImage(attachment, filePath); err != nil {
			log.Printf("Erro ao processar imagem: %v", err)
			// Não falhar o upload por causa disso
		}
	}

	// Salvar no banco
	if err := s.repo.Create(attachment); err != nil {
		// Remover arquivo do disco se falhar ao salvar no banco
		os.Remove(filePath)
		return nil, fmt.Errorf("erro ao salvar anexo no banco: %w", err)
	}

	log.Printf("Arquivo enviado com sucesso: %s (ID: %d)", header.Filename, attachment.ID)
	return s.attachmentToResponse(attachment), nil
}

// GetByID busca um anexo por ID
func (s *AttachmentServiceImpl) GetByID(id uint) (*models.AttachmentResponse, error) {
	attachment, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	return s.attachmentToResponse(attachment), nil
}

// GetByEntity busca anexos por entidade
func (s *AttachmentServiceImpl) GetByEntity(entityType string, entityID uint, filter *models.AttachmentFilter) ([]models.AttachmentResponse, error) {
	attachments, err := s.repo.GetByEntity(entityType, entityID, filter)
	if err != nil {
		return nil, err
	}

	responses := make([]models.AttachmentResponse, len(attachments))
	for i, attachment := range attachments {
		responses[i] = *s.attachmentToResponse(&attachment)
	}

	return responses, nil
}

// GetStats retorna estatísticas de anexos
func (s *AttachmentServiceImpl) GetStats(entityType string, entityID uint) (*models.AttachmentStats, error) {
	return s.repo.GetStats(entityType, entityID)
}

// DeleteFile remove um arquivo
func (s *AttachmentServiceImpl) DeleteFile(id uint, userID uint) error {
	// Buscar anexo
	attachment, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}

	// Verificar permissão (simplificado - pode ser expandido)
	if attachment.UploadedByID != userID {
		// Aqui você pode adicionar verificação de permissões mais complexa
		log.Printf("Usuário %d tentou deletar arquivo de outro usuário %d", userID, attachment.UploadedByID)
	}

	// Remover arquivo do disco
	if err := os.Remove(attachment.FilePath); err != nil {
		log.Printf("Erro ao remover arquivo do disco: %v", err)
		// Continuar mesmo se não conseguir remover do disco
	}

	// Remover thumbnail se existir
	if attachment.HasThumbnail {
		thumbnailPath := s.getThumbnailPath(attachment.FileName)
		if err := os.Remove(thumbnailPath); err != nil {
			log.Printf("Erro ao remover thumbnail: %v", err)
		}
	}

	// Remover do banco (soft delete)
	return s.repo.SoftDelete(id)
}

// CreateNewVersion cria uma nova versão de um arquivo
func (s *AttachmentServiceImpl) CreateNewVersion(id uint, file multipart.File, header *multipart.FileHeader, versionNotes string, userID uint, userName string, ipAddress string, userAgent string) (*models.AttachmentResponse, error) {
	// Buscar arquivo original
	original, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Validar arquivo
	if err := s.validateFile(header); err != nil {
		return nil, err
	}

	// Calcular hash do arquivo
	fileHash, err := s.calculateFileHash(file)
	if err != nil {
		return nil, fmt.Errorf("erro ao calcular hash do arquivo: %w", err)
	}

	// Gerar nome único para o arquivo
	fileName := s.generateUniqueFileName(header.Filename)
	filePath := filepath.Join(s.uploadPath, fileName)

	// Salvar arquivo no disco
	if err := s.saveFileToDisk(file, filePath); err != nil {
		return nil, fmt.Errorf("erro ao salvar arquivo: %w", err)
	}

	// Determinar próximo número de versão
	versions, err := s.repo.GetVersions(original.ID)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar versões: %w", err)
	}

	nextVersion := 1
	if len(versions) > 0 {
		for _, v := range versions {
			if v.Version >= nextVersion {
				nextVersion = v.Version + 1
			}
		}
	} else {
		nextVersion = original.Version + 1
	}

	// Determinar tipo do arquivo
	fileType := s.determineFileType(header.Filename)

	// Criar nova versão
	newVersion := &models.Attachment{
		OriginalName:    header.Filename,
		FileName:        fileName,
		FilePath:        filePath,
		FileSize:        header.Size,
		MimeType:        header.Header.Get("Content-Type"),
		FileHash:        fileHash,
		Type:            fileType,
		Category:        original.Category,
		Title:           original.Title,
		Description:     original.Description,
		Tags:            original.Tags,
		IsPublic:        original.IsPublic,
		EntityType:      original.EntityType,
		EntityID:        original.EntityID,
		UploadedByID:    userID,
		UploadedByName:  userName,
		IPAddress:       ipAddress,
		UserAgent:       userAgent,
		Version:         nextVersion,
		ParentID:        &original.ID,
		VersionNotes:    versionNotes,
		IsLatestVersion: true,
	}

	// Processar imagem se necessário
	if fileType == models.AttachmentTypeImage {
		if err := s.processImage(newVersion, filePath); err != nil {
			log.Printf("Erro ao processar imagem: %v", err)
		}
	}

	// Criar nova versão (isso também marca as anteriores como não sendo a mais recente)
	if err := s.repo.CreateVersion(newVersion); err != nil {
		// Remover arquivo do disco se falhar
		os.Remove(filePath)
		return nil, fmt.Errorf("erro ao criar nova versão: %w", err)
	}

	log.Printf("Nova versão criada: %s (ID: %d, Versão: %d)", header.Filename, newVersion.ID, newVersion.Version)
	return s.attachmentToResponse(newVersion), nil
}

// GetVersions retorna todas as versões de um arquivo
func (s *AttachmentServiceImpl) GetVersions(id uint) ([]models.AttachmentResponse, error) {
	versions, err := s.repo.GetVersions(id)
	if err != nil {
		return nil, err
	}

	responses := make([]models.AttachmentResponse, len(versions))
	for i, version := range versions {
		responses[i] = *s.attachmentToResponse(&version)
	}

	return responses, nil
}

// MarkAsLatestVersion marca uma versão como a mais recente
func (s *AttachmentServiceImpl) MarkAsLatestVersion(id uint) error {
	return s.repo.MarkAsLatestVersion(id)
}

// GetDownloadURL retorna a URL de download de um arquivo
func (s *AttachmentServiceImpl) GetDownloadURL(id uint) (string, error) {
	attachment, err := s.repo.GetByID(id)
	if err != nil {
		return "", err
	}

	// Gerar URL de download
	return fmt.Sprintf("/api/attachments/%d/download", attachment.ID), nil
}

// GetThumbnailURL retorna a URL do thumbnail de uma imagem
func (s *AttachmentServiceImpl) GetThumbnailURL(id uint) (string, error) {
	attachment, err := s.repo.GetByID(id)
	if err != nil {
		return "", err
	}

	if !attachment.HasThumbnail {
		return "", fmt.Errorf("arquivo não possui thumbnail")
	}

	return fmt.Sprintf("/api/attachments/%d/thumbnail", attachment.ID), nil
}

// GenerateThumbnail gera thumbnail para uma imagem
func (s *AttachmentServiceImpl) GenerateThumbnail(id uint) error {
	attachment, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}

	if attachment.Type != models.AttachmentTypeImage {
		return fmt.Errorf("arquivo não é uma imagem")
	}

	return s.generateThumbnail(attachment)
}

// Métodos auxiliares privados

// validateFile valida se o arquivo pode ser enviado
func (s *AttachmentServiceImpl) validateFile(header *multipart.FileHeader) error {
	// Verificar tamanho
	if header.Size > s.maxFileSize {
		return fmt.Errorf("arquivo muito grande. Máximo permitido: %d bytes", s.maxFileSize)
	}

	// Verificar extensão
	ext := strings.ToLower(filepath.Ext(header.Filename))
	if !s.allowedExts[ext] {
		return fmt.Errorf("extensão de arquivo não permitida: %s", ext)
	}

	return nil
}

// calculateFileHash calcula o hash MD5 do arquivo
func (s *AttachmentServiceImpl) calculateFileHash(file multipart.File) (string, error) {
	// Reset file pointer
	file.Seek(0, 0)

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	// Reset file pointer again
	file.Seek(0, 0)

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// generateUniqueFileName gera um nome único para o arquivo
func (s *AttachmentServiceImpl) generateUniqueFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%d_%s%s", timestamp, generateRandomString(8), ext)
}

// saveFileToDisk salva o arquivo no disco
func (s *AttachmentServiceImpl) saveFileToDisk(file multipart.File, filePath string) error {
	// Criar diretório se não existir
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// Criar arquivo no disco
	dst, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer dst.Close()

	// Copiar conteúdo
	_, err = io.Copy(dst, file)
	return err
}

// determineFileType determina o tipo do arquivo baseado na extensão
func (s *AttachmentServiceImpl) determineFileType(filename string) models.AttachmentType {
	ext := strings.ToLower(filepath.Ext(filename))

	imageExts := map[string]bool{
		".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true, ".webp": true,
	}

	videoExts := map[string]bool{
		".mp4": true, ".avi": true, ".mov": true, ".wmv": true, ".flv": true, ".webm": true,
	}

	audioExts := map[string]bool{
		".mp3": true, ".wav": true, ".flac": true, ".aac": true, ".ogg": true,
	}

	documentExts := map[string]bool{
		".pdf": true, ".doc": true, ".docx": true, ".xls": true, ".xlsx": true,
		".ppt": true, ".pptx": true, ".txt": true, ".rtf": true, ".odt": true,
		".ods": true, ".odp": true,
	}

	if imageExts[ext] {
		return models.AttachmentTypeImage
	} else if videoExts[ext] {
		return models.AttachmentTypeVideo
	} else if audioExts[ext] {
		return models.AttachmentTypeAudio
	} else if documentExts[ext] {
		return models.AttachmentTypeDocument
	}

	return models.AttachmentTypeOther
}

// processImage processa uma imagem (dimensões, thumbnail)
func (s *AttachmentServiceImpl) processImage(attachment *models.Attachment, filePath string) error {
	// Abrir arquivo de imagem
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Decodificar imagem para obter dimensões
	config, _, err := image.DecodeConfig(file)
	if err != nil {
		return err
	}

	// Salvar dimensões
	attachment.ImageWidth = &config.Width
	attachment.ImageHeight = &config.Height

	// Gerar thumbnail
	if err := s.generateThumbnail(attachment); err != nil {
		log.Printf("Erro ao gerar thumbnail: %v", err)
		// Não falhar por causa do thumbnail
	} else {
		attachment.HasThumbnail = true
	}

	return nil
}

// generateThumbnail gera thumbnail para uma imagem
func (s *AttachmentServiceImpl) generateThumbnail(attachment *models.Attachment) error {
	// Abrir arquivo original
	file, err := os.Open(attachment.FilePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Decodificar imagem
	img, format, err := image.Decode(file)
	if err != nil {
		return err
	}

	// Redimensionar para thumbnail (150x150)
	thumbnail := resizeImage(img, 150, 150)

	// Criar arquivo de thumbnail
	thumbnailPath := s.getThumbnailPath(attachment.FileName)
	thumbnailFile, err := os.Create(thumbnailPath)
	if err != nil {
		return err
	}
	defer thumbnailFile.Close()

	// Salvar thumbnail
	switch format {
	case "jpeg", "jpg":
		return jpeg.Encode(thumbnailFile, thumbnail, &jpeg.Options{Quality: 80})
	case "png":
		return png.Encode(thumbnailFile, thumbnail)
	default:
		return jpeg.Encode(thumbnailFile, thumbnail, &jpeg.Options{Quality: 80})
	}
}

// getThumbnailPath retorna o caminho do thumbnail
func (s *AttachmentServiceImpl) getThumbnailPath(fileName string) string {
	ext := filepath.Ext(fileName)
	name := strings.TrimSuffix(fileName, ext)
	return filepath.Join(s.uploadPath, "thumbnails", name+"_thumb"+ext)
}

// attachmentToResponse converte Attachment para AttachmentResponse
func (s *AttachmentServiceImpl) attachmentToResponse(attachment *models.Attachment) *models.AttachmentResponse {
	response := &models.AttachmentResponse{
		ID:                attachment.ID,
		OriginalName:      attachment.OriginalName,
		FileName:          attachment.FileName,
		FileSize:          attachment.FileSize,
		FileSizeFormatted: attachment.FormatFileSize(),
		MimeType:          attachment.MimeType,
		Type:              attachment.Type,
		Category:          attachment.Category,
		Title:             attachment.Title,
		Description:       attachment.Description,
		Tags:              attachment.GetTagsSlice(),
		IsPublic:          attachment.IsPublic,
		ImageWidth:        attachment.ImageWidth,
		ImageHeight:       attachment.ImageHeight,
		HasThumbnail:      attachment.HasThumbnail,
		Version:           attachment.Version,
		IsLatestVersion:   attachment.IsLatestVersion,
		VersionNotes:      attachment.VersionNotes,
		UploadedByName:    attachment.UploadedByName,
		CreatedAt:         attachment.CreatedAt,
		UpdatedAt:         attachment.UpdatedAt,
		DownloadURL:       fmt.Sprintf("/api/attachments/%d/download", attachment.ID),
	}

	if attachment.HasThumbnail {
		response.ThumbnailURL = fmt.Sprintf("/api/attachments/%d/thumbnail", attachment.ID)
	}

	if attachment.IsImage() {
		response.PreviewURL = fmt.Sprintf("/api/attachments/%d/preview", attachment.ID)
	}

	return response
}

// generateRandomString gera uma string aleatória
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// resizeImage redimensiona uma imagem mantendo a proporção
func resizeImage(src image.Image, maxWidth, maxHeight int) image.Image {
	srcBounds := src.Bounds()
	srcWidth := srcBounds.Dx()
	srcHeight := srcBounds.Dy()

	// Calcular nova dimensão mantendo proporção
	var newWidth, newHeight int
	if srcWidth > srcHeight {
		newWidth = maxWidth
		newHeight = (srcHeight * maxWidth) / srcWidth
		if newHeight > maxHeight {
			newHeight = maxHeight
			newWidth = (srcWidth * maxHeight) / srcHeight
		}
	} else {
		newHeight = maxHeight
		newWidth = (srcWidth * maxHeight) / srcHeight
		if newWidth > maxWidth {
			newWidth = maxWidth
			newHeight = (srcHeight * maxWidth) / srcWidth
		}
	}

	// Criar nova imagem
	dst := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// Redimensionar usando draw básico (implementação simples)
	draw.Draw(dst, dst.Bounds(), src, image.Point{}, draw.Src)

	return dst
}
