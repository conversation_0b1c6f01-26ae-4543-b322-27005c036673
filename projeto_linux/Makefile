# Makefile - Sistema Tradição
# Comandos para manter qualidade e prevenir duplicações

.PHONY: help check-quality check-duplications build test clean format lint security deploy-check

# Configurações
GO_FILES := $(shell find . -name "*.go" -not -path "./vendor/*")
JS_FILES := $(shell find web/static/js -name "*.js")
PROJECT_NAME := sistema-tradicao

# Cores para output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

## help: Mostra esta ajuda
help:
	@echo "$(BLUE)Sistema Tradição - Comandos Simplificados$(NC)"
	@echo "=========================================="
	@echo ""
	@echo "$(GREEN)🔍 Verificação (SEM GIT):$(NC)"
	@echo "  check-quick        - ⚡ Verificação rápida (recomendado)"
	@echo "  check-quality      - 🔍 Verificação completa"
	@echo "  check-duplications - 🔍 Apenas duplicações"
	@echo "  monitor            - 🔄 Monitor contínuo (background)"
	@echo ""
	@echo "$(GREEN)🧹 Limpeza:$(NC)"
	@echo "  clean-duplications - 🧹 Limpar duplicações automaticamente"
	@echo "  clean              - 🧹 Limpar arquivos temporários"
	@echo ""
	@echo "$(GREEN)🔧 Build:$(NC)"
	@echo "  build              - 🔨 Compilar o projeto"
	@echo "  test               - 🧪 Executar testes"
	@echo ""
	@echo "$(GREEN)📊 Informações:$(NC)"
	@echo "  stats              - 📊 Estatísticas do projeto"
	@echo "  status             - 📋 Status atual do sistema"
	@echo ""

## check-quality: Verificação completa de qualidade
check-quality: check-duplications check-syntax check-security lint
	@echo "$(GREEN)✅ Verificação de qualidade concluída!$(NC)"

## check-quick: Verificação rápida (recomendado para quem não usa Git)
check-quick:
	@echo "$(BLUE)⚡ Verificação rápida...$(NC)"
	@./scripts/check-quick.sh

## check-duplications: Verificar duplicações no código
check-duplications:
	@echo "$(BLUE)🔍 Verificando duplicações...$(NC)"
	@./scripts/check-duplications.sh

## monitor: Iniciar monitor contínuo de duplicações
monitor:
	@echo "$(BLUE)🔄 Iniciando monitor contínuo...$(NC)"
	@./scripts/monitor-duplications.sh

## clean-duplications: Limpar duplicações automaticamente
clean-duplications:
	@echo "$(BLUE)🧹 Limpando duplicações...$(NC)"
	@./scripts/clean-duplications.sh

## status: Mostrar status atual do sistema
status:
	@echo "$(BLUE)📋 Status do Sistema Tradição$(NC)"
	@echo "=============================="
	@echo "Handlers unificados: $$(find internal/handlers -name "unified_*.go" | wc -l)"
	@echo "JavaScript unificado: $$(find web/static/js -name "unified_*.js" | wc -l)"
	@echo "Última verificação: $$(if [ -f logs/duplications_monitor.log ]; then tail -1 logs/duplications_monitor.log | cut -d']' -f1 | tr -d '['; else echo 'Nunca executada'; fi)"
	@echo "Alertas ativos: $$(if [ -f ALERTA_DUPLICACOES.txt ]; then echo 'SIM - Verificar ALERTA_DUPLICACOES.txt'; else echo 'Nenhum'; fi)"

## check-syntax: Verificar sintaxe Go e JavaScript
check-syntax:
	@echo "$(BLUE)🔍 Verificando sintaxe Go...$(NC)"
	@for file in $(GO_FILES); do \
		if ! go fmt -l $$file >/dev/null 2>&1; then \
			echo "$(RED)❌ Erro de sintaxe em $$file$(NC)"; \
			exit 1; \
		fi; \
	done
	@echo "$(GREEN)✅ Sintaxe Go OK$(NC)"

	@if command -v node >/dev/null 2>&1; then \
		echo "$(BLUE)🔍 Verificando sintaxe JavaScript...$(NC)"; \
		for file in $(JS_FILES); do \
			if ! node -c $$file >/dev/null 2>&1; then \
				echo "$(RED)❌ Erro de sintaxe em $$file$(NC)"; \
				exit 1; \
			fi; \
		done; \
		echo "$(GREEN)✅ Sintaxe JavaScript OK$(NC)"; \
	fi

## check-security: Verificar problemas de segurança
check-security:
	@echo "$(BLUE)🔒 Verificando segurança...$(NC)"

	# Verificar ordem #18 hardcoded
	@if grep -r "18\|\"18\"" web/static/js/ --include="*.js" | grep -v "unified_orders.js" >/dev/null 2>&1; then \
		echo "$(RED)❌ Ordem #18 hardcoded detectada fora do handler unificado$(NC)"; \
		exit 1; \
	fi

	# Verificar URLs antigas
	@if grep -r "/api/ordens\|/api/calendar-events" web/ --include="*.html" --include="*.js" >/dev/null 2>&1; then \
		echo "$(RED)❌ URLs antigas detectadas$(NC)"; \
		exit 1; \
	fi

	# Verificar console.log em produção
	@if grep -r "console\.log\|console\.error" web/static/js/ --include="*.js" | grep -v "unified_orders.js" >/dev/null 2>&1; then \
		echo "$(YELLOW)⚠️  console.log detectado - remover antes de produção$(NC)"; \
	fi

	@echo "$(GREEN)✅ Verificação de segurança OK$(NC)"

## build: Compilar o projeto
build: check-syntax
	@echo "$(BLUE)🔨 Compilando projeto...$(NC)"
	@go build -o bin/$(PROJECT_NAME) ./cmd/main.go
	@echo "$(GREEN)✅ Compilação concluída: bin/$(PROJECT_NAME)$(NC)"

## test: Executar testes
test:
	@echo "$(BLUE)🧪 Executando testes...$(NC)"
	@go test ./... -v
	@echo "$(GREEN)✅ Testes concluídos$(NC)"

## clean: Limpar arquivos temporários
clean:
	@echo "$(BLUE)🧹 Limpando arquivos temporários...$(NC)"
	@rm -rf bin/
	@rm -rf tmp/
	@go clean
	@echo "$(GREEN)✅ Limpeza concluída$(NC)"

## format: Formatar código Go e JavaScript
format:
	@echo "$(BLUE)📝 Formatando código Go...$(NC)"
	@go fmt ./...

	@if command -v goimports >/dev/null 2>&1; then \
		echo "$(BLUE)📝 Organizando imports Go...$(NC)"; \
		goimports -w $(GO_FILES); \
	fi

	@if command -v prettier >/dev/null 2>&1; then \
		echo "$(BLUE)📝 Formatando JavaScript...$(NC)"; \
		prettier --write $(JS_FILES); \
	fi

	@echo "$(GREEN)✅ Formatação concluída$(NC)"

## lint: Executar linters
lint:
	@echo "$(BLUE)🔍 Executando linters...$(NC)"

	@if command -v golangci-lint >/dev/null 2>&1; then \
		echo "$(BLUE)🔍 Executando golangci-lint...$(NC)"; \
		golangci-lint run; \
	else \
		echo "$(YELLOW)⚠️  golangci-lint não encontrado$(NC)"; \
	fi

	@if command -v eslint >/dev/null 2>&1; then \
		echo "$(BLUE)🔍 Executando ESLint...$(NC)"; \
		eslint $(JS_FILES); \
	else \
		echo "$(YELLOW)⚠️  ESLint não encontrado$(NC)"; \
	fi

	@echo "$(GREEN)✅ Linting concluído$(NC)"

## deploy-check: Verificação completa pré-deploy
deploy-check: check-quality build test
	@echo "$(BLUE)🚀 Verificação pré-deploy...$(NC)"

	# Verificar se não há TODOs críticos
	@TODO_COUNT=$$(grep -r "TODO\|FIXME" . --include="*.go" --include="*.js" | wc -l); \
	if [ $$TODO_COUNT -gt 10 ]; then \
		echo "$(YELLOW)⚠️  $$TODO_COUNT TODOs/FIXMEs encontrados$(NC)"; \
	fi

	# Verificar tamanho do binário
	@if [ -f "bin/$(PROJECT_NAME)" ]; then \
		SIZE=$$(du -h bin/$(PROJECT_NAME) | cut -f1); \
		echo "$(BLUE)📦 Tamanho do binário: $$SIZE$(NC)"; \
	fi

	@echo "$(GREEN)✅ Projeto pronto para deploy!$(NC)"

## pre-commit: Verificação pré-commit (usado pelo hook)
pre-commit: check-duplications check-syntax check-security
	@echo "$(GREEN)✅ Verificação pré-commit OK$(NC)"

## install-hooks: Instalar hooks do Git
install-hooks:
	@echo "$(BLUE)🔗 Instalando hooks do Git...$(NC)"
	@chmod +x .git/hooks/pre-commit
	@echo "$(GREEN)✅ Hooks instalados$(NC)"

## stats: Estatísticas do projeto
stats:
	@echo "$(BLUE)📊 Estatísticas do Projeto$(NC)"
	@echo "=========================="
	@echo "Arquivos Go: $$(find . -name "*.go" | wc -l)"
	@echo "Arquivos JS: $$(find web/static/js -name "*.js" | wc -l)"
	@echo "Linhas de código Go: $$(find . -name "*.go" -exec wc -l {} + | tail -1 | awk '{print $$1}')"
	@echo "Linhas de código JS: $$(find web/static/js -name "*.js" -exec wc -l {} + | tail -1 | awk '{print $$1}' || echo 0)"
	@echo "Templates HTML: $$(find web/templates -name "*.html" | wc -l)"

## check-deps: Verificar dependências
check-deps:
	@echo "$(BLUE)📦 Verificando dependências Go...$(NC)"
	@go mod tidy
	@go mod verify
	@echo "$(GREEN)✅ Dependências OK$(NC)"

## benchmark: Executar benchmarks
benchmark:
	@echo "$(BLUE)⚡ Executando benchmarks...$(NC)"
	@go test -bench=. -benchmem ./...

## coverage: Gerar relatório de cobertura
coverage:
	@echo "$(BLUE)📈 Gerando relatório de cobertura...$(NC)"
	@go test -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)✅ Relatório gerado: coverage.html$(NC)"

# Comando padrão
.DEFAULT_GOAL := help
