-- Migração para criar sistema de métricas por filial
-- Data: 2025-01-25
-- Descrição: Implementa contadores automáticos de ordens e equipamentos por filial

-- Tabela para armazenar métricas agregadas por filial
CREATE TABLE IF NOT EXISTS branch_metrics (
    id SERIAL PRIMARY KEY,
    branch_id INTEGER NOT NULL,
    
    -- Contadores de ordens
    total_orders INTEGER DEFAULT 0,
    orders_pending INTEGER DEFAULT 0,
    orders_in_progress INTEGER DEFAULT 0,
    orders_completed INTEGER DEFAULT 0,
    orders_cancelled INTEGER DEFAULT 0,
    
    -- Contadores por período
    orders_today INTEGER DEFAULT 0,
    orders_this_week INTEGER DEFAULT 0,
    orders_this_month INTEGER DEFAULT 0,
    orders_this_year INTEGER DEFAULT 0,
    
    -- Métricas de equipamentos
    total_equipment INTEGER DEFAULT 0,
    equipment_operational INTEGER DEFAULT 0,
    equipment_maintenance INTEGER DEFAULT 0,
    equipment_broken INTEGER DEFAULT 0,
    
    -- Timestamps
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    UNIQUE(branch_id)
);

-- Tabela para rastrear tipos de equipamentos que mais geram ordens
CREATE TABLE IF NOT EXISTS equipment_type_metrics (
    id SERIAL PRIMARY KEY,
    branch_id INTEGER NOT NULL,
    equipment_type VARCHAR(100) NOT NULL,
    
    -- Contadores
    total_orders INTEGER DEFAULT 0,
    orders_this_month INTEGER DEFAULT 0,
    orders_this_year INTEGER DEFAULT 0,
    
    -- Métricas de tempo
    avg_resolution_time INTEGER DEFAULT 0, -- em horas
    total_downtime INTEGER DEFAULT 0, -- em horas
    
    -- Custos
    total_cost DECIMAL(12,2) DEFAULT 0.00,
    avg_cost DECIMAL(12,2) DEFAULT 0.00,
    
    -- Timestamps
    last_order_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    UNIQUE(branch_id, equipment_type)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_branch_metrics_branch_id ON branch_metrics(branch_id);
CREATE INDEX IF NOT EXISTS idx_branch_metrics_updated ON branch_metrics(last_updated);

CREATE INDEX IF NOT EXISTS idx_equipment_type_metrics_branch ON equipment_type_metrics(branch_id);
CREATE INDEX IF NOT EXISTS idx_equipment_type_metrics_type ON equipment_type_metrics(equipment_type);
CREATE INDEX IF NOT EXISTS idx_equipment_type_metrics_orders ON equipment_type_metrics(total_orders DESC);

-- Função para atualizar métricas automaticamente
CREATE OR REPLACE FUNCTION update_branch_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Atualizar métricas da filial quando uma ordem é criada/atualizada
    IF TG_OP = 'INSERT' THEN
        -- Nova ordem criada
        INSERT INTO branch_metrics (branch_id, total_orders, orders_today, orders_this_week, orders_this_month, orders_this_year)
        VALUES (NEW.branch_id, 1, 1, 1, 1, 1)
        ON CONFLICT (branch_id) DO UPDATE SET
            total_orders = branch_metrics.total_orders + 1,
            orders_today = CASE 
                WHEN DATE(branch_metrics.last_updated) = CURRENT_DATE 
                THEN branch_metrics.orders_today + 1 
                ELSE 1 
            END,
            orders_this_week = CASE 
                WHEN DATE_TRUNC('week', branch_metrics.last_updated) = DATE_TRUNC('week', CURRENT_DATE)
                THEN branch_metrics.orders_this_week + 1
                ELSE 1
            END,
            orders_this_month = CASE 
                WHEN DATE_TRUNC('month', branch_metrics.last_updated) = DATE_TRUNC('month', CURRENT_DATE)
                THEN branch_metrics.orders_this_month + 1
                ELSE 1
            END,
            orders_this_year = CASE 
                WHEN DATE_TRUNC('year', branch_metrics.last_updated) = DATE_TRUNC('year', CURRENT_DATE)
                THEN branch_metrics.orders_this_year + 1
                ELSE 1
            END,
            last_updated = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP;
            
        -- Atualizar contadores por status
        IF NEW.status = 'pending' OR NEW.status = 'open' THEN
            UPDATE branch_metrics SET orders_pending = orders_pending + 1 WHERE branch_id = NEW.branch_id;
        ELSIF NEW.status = 'in_progress' OR NEW.status = 'em_andamento' THEN
            UPDATE branch_metrics SET orders_in_progress = orders_in_progress + 1 WHERE branch_id = NEW.branch_id;
        ELSIF NEW.status = 'completed' OR NEW.status = 'concluida' THEN
            UPDATE branch_metrics SET orders_completed = orders_completed + 1 WHERE branch_id = NEW.branch_id;
        ELSIF NEW.status = 'cancelled' OR NEW.status = 'cancelada' THEN
            UPDATE branch_metrics SET orders_cancelled = orders_cancelled + 1 WHERE branch_id = NEW.branch_id;
        END IF;
        
        RETURN NEW;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- Status da ordem foi atualizado
        IF OLD.status != NEW.status THEN
            -- Decrementar contador antigo
            IF OLD.status = 'pending' OR OLD.status = 'open' THEN
                UPDATE branch_metrics SET orders_pending = GREATEST(orders_pending - 1, 0) WHERE branch_id = NEW.branch_id;
            ELSIF OLD.status = 'in_progress' OR OLD.status = 'em_andamento' THEN
                UPDATE branch_metrics SET orders_in_progress = GREATEST(orders_in_progress - 1, 0) WHERE branch_id = NEW.branch_id;
            ELSIF OLD.status = 'completed' OR OLD.status = 'concluida' THEN
                UPDATE branch_metrics SET orders_completed = GREATEST(orders_completed - 1, 0) WHERE branch_id = NEW.branch_id;
            ELSIF OLD.status = 'cancelled' OR OLD.status = 'cancelada' THEN
                UPDATE branch_metrics SET orders_cancelled = GREATEST(orders_cancelled - 1, 0) WHERE branch_id = NEW.branch_id;
            END IF;
            
            -- Incrementar contador novo
            IF NEW.status = 'pending' OR NEW.status = 'open' THEN
                UPDATE branch_metrics SET orders_pending = orders_pending + 1 WHERE branch_id = NEW.branch_id;
            ELSIF NEW.status = 'in_progress' OR NEW.status = 'em_andamento' THEN
                UPDATE branch_metrics SET orders_in_progress = orders_in_progress + 1 WHERE branch_id = NEW.branch_id;
            ELSIF NEW.status = 'completed' OR NEW.status = 'concluida' THEN
                UPDATE branch_metrics SET orders_completed = orders_completed + 1 WHERE branch_id = NEW.branch_id;
            ELSIF NEW.status = 'cancelled' OR NEW.status = 'cancelada' THEN
                UPDATE branch_metrics SET orders_cancelled = orders_cancelled + 1 WHERE branch_id = NEW.branch_id;
            END IF;
            
            UPDATE branch_metrics SET last_updated = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE branch_id = NEW.branch_id;
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar métricas automaticamente
DROP TRIGGER IF EXISTS trigger_update_branch_metrics ON maintenance_orders;
CREATE TRIGGER trigger_update_branch_metrics
    AFTER INSERT OR UPDATE ON maintenance_orders
    FOR EACH ROW EXECUTE FUNCTION update_branch_metrics();

-- Função para atualizar métricas de tipos de equipamento
CREATE OR REPLACE FUNCTION update_equipment_type_metrics()
RETURNS TRIGGER AS $$
DECLARE
    eq_type VARCHAR(100);
BEGIN
    -- Buscar tipo do equipamento
    SELECT type INTO eq_type FROM equipment WHERE id = NEW.equipment_id;
    
    IF eq_type IS NOT NULL THEN
        -- Atualizar métricas do tipo de equipamento
        INSERT INTO equipment_type_metrics (branch_id, equipment_type, total_orders, orders_this_month, orders_this_year, last_order_date)
        VALUES (NEW.branch_id, eq_type, 1, 1, 1, CURRENT_TIMESTAMP)
        ON CONFLICT (branch_id, equipment_type) DO UPDATE SET
            total_orders = equipment_type_metrics.total_orders + 1,
            orders_this_month = CASE 
                WHEN DATE_TRUNC('month', equipment_type_metrics.updated_at) = DATE_TRUNC('month', CURRENT_DATE)
                THEN equipment_type_metrics.orders_this_month + 1
                ELSE 1
            END,
            orders_this_year = CASE 
                WHEN DATE_TRUNC('year', equipment_type_metrics.updated_at) = DATE_TRUNC('year', CURRENT_DATE)
                THEN equipment_type_metrics.orders_this_year + 1
                ELSE 1
            END,
            last_order_date = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para métricas de tipos de equipamento
DROP TRIGGER IF EXISTS trigger_update_equipment_type_metrics ON maintenance_orders;
CREATE TRIGGER trigger_update_equipment_type_metrics
    AFTER INSERT ON maintenance_orders
    FOR EACH ROW EXECUTE FUNCTION update_equipment_type_metrics();

-- Inserir métricas iniciais para filiais existentes
INSERT INTO branch_metrics (branch_id)
SELECT id FROM branches 
WHERE NOT EXISTS (SELECT 1 FROM branch_metrics WHERE branch_metrics.branch_id = branches.id);

-- Comentários para documentação
COMMENT ON TABLE branch_metrics IS 'Métricas agregadas por filial para análise de performance';
COMMENT ON TABLE equipment_type_metrics IS 'Métricas por tipo de equipamento para identificar equipamentos problemáticos';
COMMENT ON FUNCTION update_branch_metrics() IS 'Atualiza automaticamente as métricas quando ordens são criadas/modificadas';
COMMENT ON FUNCTION update_equipment_type_metrics() IS 'Atualiza métricas por tipo de equipamento';
