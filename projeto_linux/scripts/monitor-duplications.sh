#!/bin/bash

# Monitor Contínuo de Duplicações - Sistema Tradição
# Este script fica rodando em background verificando duplicações automaticamente

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações
INTERVALO=300  # 5 minutos (300 segundos)
LOG_FILE="logs/duplications_monitor.log"
ALERT_FILE="ALERTA_DUPLICACOES.txt"

# Criar diretório de logs se não existir
mkdir -p logs

echo -e "${BLUE}🔍 MONITOR DE DUPLICAÇÕES INICIADO${NC}"
echo -e "${BLUE}Verificando a cada $((INTERVALO/60)) minutos...${NC}"
echo -e "${BLUE}Log: $LOG_FILE${NC}"
echo -e "${BLUE}Para parar: Ctrl+C${NC}"
echo ""

# Função para log com timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Função para enviar alerta
send_alert() {
    local message="$1"
    
    # Criar arquivo de alerta visível
    cat > "$ALERT_FILE" << EOF
🚨 ALERTA DE DUPLICAÇÕES DETECTADAS! 🚨
=============================================

Data/Hora: $(date '+%Y-%m-%d %H:%M:%S')
Problema: $message

AÇÃO NECESSÁRIA:
1. Execute: ./scripts/check-duplications.sh
2. Corrija os problemas encontrados
3. Execute: ./scripts/clean-duplications.sh (se necessário)

Este arquivo será removido automaticamente quando 
as duplicações forem corrigidas.
=============================================
EOF

    echo -e "${RED}🚨 ALERTA CRIADO: $ALERT_FILE${NC}"
    
    # Tentar notificação do sistema (se disponível)
    if command -v notify-send >/dev/null 2>&1; then
        notify-send "Sistema Tradição" "Duplicações detectadas! Verificar $ALERT_FILE"
    fi
    
    # Tentar som de alerta (se disponível)
    if command -v paplay >/dev/null 2>&1; then
        paplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null || true
    elif command -v aplay >/dev/null 2>&1; then
        aplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null || true
    fi
}

# Função para remover alerta
remove_alert() {
    if [ -f "$ALERT_FILE" ]; then
        rm "$ALERT_FILE"
        echo -e "${GREEN}✅ Alerta removido - sistema limpo${NC}"
    fi
}

# Função principal de verificação
check_duplications() {
    log_message "Iniciando verificação de duplicações..."
    
    # Executar verificação silenciosa
    if ./scripts/check-duplications.sh >/dev/null 2>&1; then
        # Sem duplicações
        log_message "✅ Verificação OK - nenhuma duplicação encontrada"
        remove_alert
        return 0
    else
        # Duplicações encontradas
        log_message "❌ DUPLICAÇÕES DETECTADAS!"
        
        # Executar verificação detalhada para capturar erros
        local errors=$(./scripts/check-duplications.sh 2>&1 | grep "ERRO\|ERROR" | head -3)
        
        send_alert "Duplicações encontradas: $errors"
        return 1
    fi
}

# Verificação inicial
log_message "Monitor de duplicações iniciado"
check_duplications

# Loop principal
while true; do
    sleep $INTERVALO
    check_duplications
done
