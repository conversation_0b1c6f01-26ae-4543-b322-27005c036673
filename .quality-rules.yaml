# Regras de Qualidade - Sistema Tradição
# Este arquivo define as regras para prevenir duplicações e manter a arquitetura limpa

project_name: "Sistema Tradição"
version: "2.0"
last_updated: "2025-05-25"

# REGRAS DE ARQUITETURA
architecture_rules:
  
  # Handlers - APENAS UM HANDLER POR DOMÍNIO
  handlers:
    max_order_handlers: 1
    allowed_order_handlers:
      - "unified_order_handler.go"
    forbidden_patterns:
      - "*order_handler.go"
      - "*ordem_handler.go"
      - "*maintenance_order_handler.go"
    
  # Rotas - CONSOLIDAÇÃO OBRIGATÓRIA
  routes:
    max_order_route_files: 2  # unified + legacy redirects
    allowed_order_routes:
      - "unified_order_routes_new.go"
      - "legacy_redirects.go"  # apenas redirecionamentos
    forbidden_patterns:
      - "*order_routes.go"
      - "*ordem_routes.go"
      - "*maintenance_order_routes.go"
  
  # JavaScript - APENAS UM ARQUIVO UNIFICADO
  javascript:
    max_order_js_files: 1
    allowed_order_js:
      - "unified_orders.js"
    forbidden_patterns:
      - "orders.js"
      - "orders_*.js"
      - "ordem*.js"
      - "calendar-*.js"
      - "dashboard_orders.js"

# REGRAS DE ENDPOINTS
endpoint_rules:
  
  # Endpoints permitidos (padrão unificado)
  allowed_endpoints:
    - "GET /api/orders"
    - "GET /api/orders/:id"
    - "GET /api/orders/calendar"
    - "GET /api/orders/technician"
    - "GET /api/orders/metrics"
    - "POST /api/orders"
    - "PUT /api/orders/:id"
    - "DELETE /api/orders/:id"
  
  # Endpoints PROIBIDOS (duplicações)
  forbidden_endpoints:
    - "/api/ordens"
    - "/api/ordens/:id"
    - "/api/ordens/tecnico"
    - "/api/ordens/calendario"
    - "/api/calendar-events"
    - "/api/orders/list"
    - "/api/maintenance-orders"
  
  # Redirecionamentos permitidos (apenas para compatibilidade)
  allowed_redirects:
    - "/api/ordens -> /api/orders"
    - "/api/ordens/:id -> /api/orders/:id"
    - "/api/ordens/tecnico -> /api/orders/technician"
    - "/api/ordens/calendario -> /api/orders/calendar"

# REGRAS DE CÓDIGO
code_rules:
  
  # Funções - Evitar duplicação
  functions:
    max_similar_functions: 2  # máximo de funções com nomes similares
    forbidden_function_patterns:
      - "GetOrder*"  # usar apenas no handler unificado
      - "ListOrder*"  # usar apenas no handler unificado
      - "CreateOrder*"  # usar apenas no handler unificado
      - "*OrdemHandler"  # usar apenas unified
  
  # Estruturas - Consolidação obrigatória
  structs:
    max_order_structs: 5  # MaintenanceOrder, OrderFilters, etc.
    forbidden_struct_patterns:
      - "Order*Response"  # usar StandardResponse
      - "*OrdemRequest"  # usar padrão unificado
  
  # Imports - Evitar dependências desnecessárias
  imports:
    forbidden_import_patterns:
      - "handlers.*order.*"  # importar apenas unified
      - "routes.*order.*"  # importar apenas unified

# REGRAS DE TEMPLATES
template_rules:
  
  # Templates HTML
  html_templates:
    max_order_templates: 3
    allowed_order_templates:
      - "orders_gallery_style.html"
      - "order_detail.html"
      - "calendar_flip.html"
    forbidden_template_patterns:
      - "orders.html"
      - "ordem*.html"
      - "maintenance_order*.html"
  
  # Scripts em templates
  template_scripts:
    forbidden_script_patterns:
      - "orders.js"
      - "orders_gallery.js"
      - "dashboard_orders.js"
      - "calendar-flip.js"
    required_script:
      - "unified_orders.js"

# REGRAS DE BANCO DE DADOS
database_rules:
  
  # Queries - Otimização obrigatória
  queries:
    forbidden_query_patterns:
      - "SELECT * FROM maintenance_orders WHERE id = ?"  # usar joins
      - "N+1 queries"  # usar eager loading
    required_optimizations:
      - "JOIN com tabelas relacionadas"
      - "Paginação em todas as listagens"
      - "Filtros aplicados no banco"
  
  # Repositories - Consolidação
  repositories:
    max_order_repositories: 1
    allowed_repositories:
      - "MaintenanceOrderRepository"
    forbidden_patterns:
      - "*OrderRepo"
      - "*OrdemRepo"

# REGRAS DE SEGURANÇA
security_rules:
  
  # Ordem #18 - BLOQUEIO OBRIGATÓRIO
  order_18:
    must_be_blocked: true
    allowed_references:
      - "unified_order_handler.go"  # apenas para bloqueio
    forbidden_references:
      - "templates/*.html"
      - "static/js/*.js"
      - "outros handlers"
  
  # Validação - Obrigatória
  validation:
    required_validations:
      - "ID de ordem válido"
      - "Parâmetros de paginação"
      - "Filtros de entrada"
      - "Autenticação de usuário"

# REGRAS DE PERFORMANCE
performance_rules:
  
  # Cache - Implementação obrigatória
  cache:
    required_cache_layers:
      - "JavaScript (5 minutos)"
      - "API responses (configurável)"
    forbidden_cache_patterns:
      - "Cache sem TTL"
      - "Cache sem invalidação"
  
  # Paginação - Obrigatória
  pagination:
    max_items_per_page: 100
    default_items_per_page: 10
    required_pagination_endpoints:
      - "/api/orders"
      - "/api/orders/calendar"
      - "/api/orders/technician"

# REGRAS DE COMPATIBILIDADE
compatibility_rules:
  
  # Redirecionamentos - Temporários
  redirects:
    max_redirect_duration: "6 meses"
    required_redirect_status: 301  # Moved Permanently
    required_redirect_headers:
      - "Location"
      - "Cache-Control"
  
  # Versionamento - Futuro
  versioning:
    current_version: "v1"
    next_version: "v2"
    deprecation_notice_required: true

# EXCEÇÕES PERMITIDAS
exceptions:
  
  # Casos especiais onde duplicação é permitida
  allowed_duplications:
    - "Testes unitários"
    - "Documentação de exemplo"
    - "Migrations de banco"
    - "Arquivos de configuração"
  
  # Arquivos que podem ser ignorados
  ignored_files:
    - "*.test.go"
    - "*.example.go"
    - "migrations/*.sql"
    - "docs/*.md"

# AÇÕES AUTOMÁTICAS
automated_actions:
  
  # Quando duplicação é detectada
  on_duplication_detected:
    - "Bloquear commit"
    - "Enviar notificação"
    - "Gerar relatório"
    - "Sugerir correção"
  
  # Verificações automáticas
  automatic_checks:
    - "Pre-commit hook"
    - "CI/CD pipeline"
    - "Deploy blocker"
    - "Code review"

# MÉTRICAS DE QUALIDADE
quality_metrics:
  
  # Metas de qualidade
  targets:
    duplication_percentage: 0  # 0% duplicação
    code_coverage: 80  # 80% cobertura de testes
    performance_score: 90  # 90% performance
    maintainability_index: 85  # 85% manutenibilidade
  
  # Alertas
  alerts:
    duplication_threshold: 1  # alerta com 1% duplicação
    performance_threshold: 80  # alerta abaixo de 80%
    complexity_threshold: 10  # alerta acima de 10 complexidade
