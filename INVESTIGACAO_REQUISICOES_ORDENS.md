# Investigação: Fluxo de Requisições de Ordens de Manutenção

## Data da Investigação
**Data:** 25/05/2025
**Escopo:** Páginas de Calendário e Orders

## 🔍 Resumo Executivo

O sistema possui **múltiplos fluxos** para requisições de ordens de manutenção, com diferentes endpoints, handlers e métodos de processamento. Identificadas **inconsistências** e **duplicações** que podem causar confusão e problemas de manutenção.

## 📊 Mapeamento Completo do Fluxo

### 1. **PÁGINA CALENDÁRIO**

#### 🌐 **Rotas Frontend:**
- `/calendar` - Calendário geral (routes.go)
- `/pages/calendario` - Calendário principal (main.go)
- `/pages/calendario-flip` - Calendário flip (main.go)
- `/orders/calendar` - Calendário de ordens (unified_order_routes.go)

#### 🔗 **Endpoints API:**
- `/api/calendar-events` - Eventos gerais
- `/api/ordens/calendario` - Ordens para calendário
- `/api/ordens/tecnico?date=YYYY-MM-DD` - Ordens por data

#### ⚙️ **Handlers Responsáveis:**
1. **GetCalendarOrdersHandler** (`calendar_api_handler.go`)
   - Processa requisições `/api/ordens/calendario`
   - Filtra por mês/ano
   - Aplica filtros de filial
   - Retorna formato `CalendarOrderSummary`

2. **OrdemCalendarioHTML** (`ordens.go`)
   - Renderiza página HTML do calendário
   - Valida parâmetros mês/ano

3. **ShowOrderCalendar** (`order_handlers_extended.go`)
   - Renderiza `/orders/calendar`
   - Template: `ordens/calendar.html`

#### 🗄️ **Consultas no Banco:**
```sql
-- GetCalendarOrdersHandler
SELECT * FROM maintenance_orders
WHERE (due_date BETWEEN ? AND ?) OR (created_at BETWEEN ? AND ?)
AND branch_id = ? (se filtro aplicado)
ORDER BY due_date, created_at
```

#### 🎨 **JavaScript Frontend:**
- **Ordermtecnico.js**: `fetch('/api/ordens/tecnico?date=${formattedDate}')`
- **calendar-flip.js**: `fetch('/api/ordens/tecnico?date=${formattedDate}')`

### 2. **PÁGINA ORDERS**

#### 🌐 **Rotas Frontend:**
- `/orders` - Listagem principal
- `/orders/:id` - Detalhes da ordem
- `/orders/create` - Criação
- `/orders/calendar` - Calendário de ordens

#### 🔗 **Endpoints API:**
- `/api/orders` - Lista paginada
- `/api/orders/:id` - Detalhes específicos
- `/api/ordens` - Lista alternativa
- `/api/ordens/:id` - Detalhes alternativos
- `/api/ordens/tecnico` - Ordens do técnico

#### ⚙️ **Handlers Responsáveis:**

1. **ListOrdersPage** (`order_handlers_extended.go`)
   - Renderiza página principal `/orders`
   - Template: `ordens/orders_gallery_style.html`
   - Aplica filtros por técnico/role

2. **ListOrders** (`order_handlers.go`)
   - API `/api/orders` com paginação
   - Filtros: status, branch_id, start_date, end_date
   - Retorna métricas de contadores

3. **GetOrdemTecnicoHandler** (`ordem_tecnico_handler.go`)
   - API `/api/ordens/tecnico`
   - Filtra por técnico logado
   - Usa tabela `technician_orders`

4. **GetOrdemHandler** (`ordem_tecnico_handlers.go`)
   - API `/api/ordens/:id`
   - Detalhes completos da ordem
   - Carrega relacionamentos

#### 🗄️ **Consultas no Banco:**

**Para Listagem:**
```sql
-- Via Repository.GetAll()
SELECT * FROM maintenance_orders
WHERE (filtros aplicados)
AND id != 18 AND (is_test = false OR is_test IS NULL)
ORDER BY created_at DESC
LIMIT ? OFFSET ?
```

**Para Técnico:**
```sql
-- GetOrdemTecnicoHandler
SELECT maintenance_orders.*
FROM maintenance_orders
JOIN technician_orders ON technician_orders.order_id = maintenance_orders.id
WHERE technician_orders.technician_id = ?
ORDER BY created_at DESC
```

#### 🎨 **JavaScript Frontend:**
- **orders.js**: `fetch('/api/orders?${params}')`
- **orders_gallery.js**: `fetch('/api/orders')`
- **dashboard_orders.js**: `fetch('/api/ordens?exclude_ids=18')`

## 🚨 **Problemas Identificados**

### 1. **Duplicação de Endpoints**
- `/api/orders` vs `/api/ordens` - **MESMA FUNCIONALIDADE**
- `/api/orders/:id` vs `/api/ordens/:id` - **MESMA FUNCIONALIDADE**
- Múltiplos handlers fazendo a mesma coisa

### 2. **Inconsistências de Filtros**
- Alguns endpoints excluem ordem #18, outros não
- Filtros de `is_test` aplicados inconsistentemente
- Diferentes critérios de permissão por endpoint

### 3. **Múltiplos Templates**
- `calendar.html` vs `calendar_flip.html`
- `orders_gallery_style.html` vs outros templates
- Funcionalidades similares em templates diferentes

### 4. **JavaScript Duplicado**
- `Ordermtecnico.js` vs `calendar-flip.js` - **MESMA LÓGICA**
- `orders.js` vs `orders_gallery.js` - **FUNCIONALIDADES SOBREPOSTAS**

### 5. **Queries Ineficientes**
- Múltiplas consultas para carregar relacionamentos
- Falta de índices otimizados
- N+1 queries em alguns handlers

## 📈 **Fluxo de Dados Detalhado**

### **Calendário - Fluxo Completo:**
```
1. Usuário acessa /pages/calendario
2. Template calendar_flip.html carregado
3. JavaScript calendar-flip.js executa
4. Fetch para /api/ordens/tecnico?date=YYYY-MM-DD
5. GetOrdemTecnicoHandler processa
6. Query na tabela technician_orders + maintenance_orders
7. Retorna JSON com ordens filtradas
8. JavaScript renderiza no calendário
```

### **Orders - Fluxo Completo:**
```
1. Usuário acessa /orders
2. ListOrdersPage handler executa
3. Service.GetAllOrders() chamado
4. Repository.GetAll() executa query
5. Filtros aplicados (técnico, role, etc.)
6. Template orders_gallery_style.html renderizado
7. JavaScript orders_gallery.js carrega
8. Fetch adicional para /api/orders (duplicação!)
9. Dados renderizados na interface
```

## 🎯 **Recomendações de Melhoria**

### 1. **Consolidação de Endpoints**
- Manter apenas `/api/orders` (remover `/api/ordens`)
- Unificar handlers duplicados
- Padronizar formato de resposta

### 2. **Otimização de Queries**
- Implementar eager loading
- Adicionar índices necessários
- Usar uma única query com JOINs

### 3. **Limpeza de Templates**
- Consolidar templates similares
- Remover JavaScript duplicado
- Padronizar estrutura HTML

### 4. **Filtros Consistentes**
- Aplicar mesmos filtros em todos os endpoints
- Documentar regras de negócio
- Implementar middleware de filtros

## 📋 **Próximos Passos**

1. **Consolidar endpoints duplicados**
2. **Otimizar queries do banco**
3. **Limpar JavaScript redundante**
4. **Padronizar templates**
5. **Implementar testes de integração**

## 🗺️ **Diagrama de Fluxo Simplificado**

```
FRONTEND                    BACKEND                     DATABASE
┌─────────────────┐        ┌─────────────────┐        ┌─────────────────┐
│   CALENDÁRIO    │        │                 │        │                 │
│                 │        │  GetCalendar    │        │ maintenance_    │
│ calendar-flip.js├────────┤  OrdersHandler  ├────────┤ orders          │
│                 │ GET    │                 │ SELECT │                 │
│ /api/ordens/    │        │ calendar_api_   │        │ + technician_   │
│ calendario      │        │ handler.go      │        │ orders (JOIN)   │
└─────────────────┘        └─────────────────┘        └─────────────────┘

┌─────────────────┐        ┌─────────────────┐        ┌─────────────────┐
│     ORDERS      │        │                 │        │                 │
│                 │        │  ListOrders     │        │ maintenance_    │
│ orders.js       ├────────┤  Page Handler   ├────────┤ orders          │
│                 │ GET    │                 │ SELECT │                 │
│ /api/orders     │        │ order_handlers_ │        │ + branches      │
│                 │        │ extended.go     │        │ + equipment     │
└─────────────────┘        └─────────────────┘        └─────────────────┘

PROBLEMAS IDENTIFICADOS:
❌ Endpoints duplicados (/api/orders vs /api/ordens)
❌ Handlers duplicados (mesma funcionalidade)
❌ JavaScript redundante (calendar-flip.js vs Ordermtecnico.js)
❌ Queries ineficientes (N+1 problem)
❌ Templates similares não consolidados
```

## 🔧 **Arquitetura Recomendada**

```
FRONTEND                    BACKEND                     DATABASE
┌─────────────────┐        ┌─────────────────┐        ┌─────────────────┐
│   UNIFICADO     │        │                 │        │                 │
│                 │        │  Unified Order  │        │ Optimized       │
│ orders.js       ├────────┤  Handler        ├────────┤ Queries with    │
│ (consolidado)   │ GET    │                 │ SELECT │ Proper JOINs    │
│                 │        │ Single endpoint │        │ and Indexes     │
│ /api/orders     │        │ /api/orders     │        │                 │
└─────────────────┘        └─────────────────┘        └─────────────────┘

BENEFÍCIOS:
✅ Endpoint único e consistente
✅ Handler consolidado
✅ JavaScript otimizado
✅ Queries eficientes
✅ Manutenção simplificada
```

---

**Status:** ✅ Investigação Completa
**Complexidade:** Alta - Múltiplos fluxos duplicados
**Prioridade:** Alta - Impacta performance e manutenibilidade
